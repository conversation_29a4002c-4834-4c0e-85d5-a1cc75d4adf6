import slash from 'slash2';
import routes from './routes'
import defaultSettings from './defaultSettings'; // https://umijs.org/config/
// import webpackPlugin from './plugin.config';
import deploy from './deploy';
import deployConfig from './deploy.config.js';

const { pwa, primaryColor } = defaultSettings; // preview.pro.ant.design only do not use in your production ;
const { ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION, deployEnv = 'dev' } = process.env;
const plugins = [
  [
    'umi-plugin-react',
    {
      // antd版本升级成功后将改为 antd: true
      antd: false,
      dva: {
        hmr: true,
      },
      locale: {
        // default false
        enable: true,
        // default zh-CN
        default: 'zh-CN',
        // default true, when it is true, will use `navigator.language` overwrite default
        baseNavigator: true,
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
        webpackChunkName: true,
        level: 3,
      },
      pwa: pwa
        ? {
            workboxPluginMode: 'InjectManifest',
            workboxOptions: {
              importWorkboxFrom: 'local',
            },
          }
        : false,
    },
  ],
  [
    'umi-plugin-pro-block',
    {
      moveMock: false,
      moveService: false,
      modifyRequest: true,
      autoAddMenu: true,
    },
  ],
  [
    '@leopard/umi-plugin-sso-auth',
    {
      appCode: 'leopard-bi-eventlog',   //应用appCode
      // loginCode: [1006],
      target: deploy[deployEnv].ssoTarget,
      // tempAccount: `${deploy[deployEnv]['host']}user/tempAccount` // 临时账号回调地址
    }
  ],
];

export default {
  plugins,
  block: {
    defaultGitUrl: 'https://github.com/ant-design/pro-blocks',
  },
  hash: true,
  targets: {
    ie: 11,
  },
  // devtool: deployEnv === 'development' ? 'source-map' : false,
  devtool: false,
  // umi routes: https://umijs.org/zh/guide/router.html
  base: deploy[deployEnv].base,
  // publicPath: deploy[deployEnv].publicPath,
  publicPath: deployConfig.assets_public_path,
  outputPath: `./dist/${deployConfig.output_assets_dir}`,
  routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': primaryColor,
  },
  define: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION:
      ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION || '', // preview.pro.ant.design only do not use in your production ; preview.pro.ant.design 专用环境变量，请不要在你的项目中使用它。
    deployEnv,
  },
  ignoreMomentLocale: true,
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  disableRedirectHoist: true,
  cssLoaderOptions: {
    modules: true,
    getLocalIdent: (context, _, localName) => {
      if (
        context.resourcePath.includes('node_modules') ||
        context.resourcePath.includes('ant.design.pro.less') ||
        context.resourcePath.includes('global.less')
      ) {
        return localName;
      }

      const match = context.resourcePath.match(/src(.*)/);

      if (match && match[1]) {
        const antdProPath = match[1].replace('.less', '');
        const arr = slash(antdProPath)
          .split('/')
          .map(a => a.replace(/([A-Z])/g, '-$1'))
          .map(a => a.toLowerCase());
        return `antd-pro${arr.join('-')}-${localName}`.replace(/--/g, '-');
      }

      return localName;
    },
  },
  manifest: {
    basePath: '/',
  },
  disableCSSModules: false,
  proxy: {
    '/sso/': {
      target: 'http://eventlog-test.yueyuechuxing.cn/',
      changeOrigin: true,
      pathRewrite: {},
    },
    '/admin/': {
      target: 'http://eventlog-test.yueyuechuxing.cn/',
      changeOrigin: true,
      pathRewrite: {},
    },
  },
};
