const isBuildCdn = process.argv.includes('cdn') // 是否上传静态资源至 CDN
const isPublicCdn = process.argv.includes('publicCdn') // 是否线上 cdn
const SYSTEM_SHORT_NAME = 'eventlog'
const ASSETS_DIR = SYSTEM_SHORT_NAME + '_static'
//带版本cdn部署配置
const withVersion = process.argv.includes('withVersion') // 是否带版本的cdn
const BRANCHNAME = isBuildCdn && withVersion ? require('child_process').execSync('git rev-parse --abbrev-ref HEAD').toString().replace(/\s+/, '') : '' //所在分支
const CDN_VESION = isBuildCdn && withVersion ? BRANCHNAME.split('/')[1] : '' //cdn版本
const CDN_SCORE_PATH = `//${isPublicCdn ? 'webstatic' : 'cdntest'}.yueyuechuxing.cn/yueyue/admin${withVersion ? ('/' + SYSTEM_SHORT_NAME) : ''}`
const ASSETS_PUBLIC_PATH = isBuildCdn ? `${CDN_SCORE_PATH}${withVersion ? ('/' + CDN_VESION) : ''}/` : '/public/'


module.exports = {
  system_short_name: SYSTEM_SHORT_NAME,
  assets_dir: ASSETS_DIR,
  output_assets_dir: SYSTEM_SHORT_NAME + '/' + CDN_VESION + '/',
  assets_public_path: ASSETS_PUBLIC_PATH,
  cdn_score_path: CDN_SCORE_PATH,
  cdn_vesion: CDN_VESION
}