/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-29 16:25:23
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-22 14:25:06
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/config/routes/index.js
 */
export default [
  {
    path: '/',
    component: '../layouts/BlankLayout',
    routes: [
      {
        path: '/',
        component: '../layouts/BasicLayout',
        // Routes: ['src/pages/Authorized'],
        routes: [
          {
            path: '/',
            redirect: '/information',
          },
          // 埋点管理(新)
          {
            path: '/buryingPointNew',
            name: 'buryingPointNew',
            icon: 'flag',
            routes: [
              // 事件配置
              {
                path: '/buryingPointNew/eventTracking',
                name: 'eventTracking',
                component: './eventTracking',
              },
              // 页面管理
              {
                path: '/buryingPointNew/pageTracking',
                name: 'pageTracking',
                component: './pageTracking',
              },
              // 事件审核
              {
                path: '/buryingPointNew/reviewEventTracking',
                name: 'reviewEventTracking',
                component: './reviewEventTracking',
              },
              // 事件详情
              {
                path: '/buryingPointNew/eventDetail',
                name: 'eventDetail',
                component: './eventDetail',
                hideInMenu: true,
              },
              // 页面详情
              {
                path: '/buryingPointNew/pageDetail',
                name: 'pageDetail',
                component: './pageTracking/component/pageDetail',
                hideInMenu: true,
              },
            ],
          },
          // 数据验证
          {
            path: '/dataVerification',
            name: 'dataVerification',
            icon: 'flag',
            routes: [
              // 事件
              {
                path: '/dataVerification/buriedDataVerification',
                name: 'buriedDataVerification',
                component: './buriedDataVerification',
              },
              // 埋点验证
              {
                path: '/dataVerification/buriedEventDataVerification',
                name: 'buriedEventDataVerification',
                component: './buriedEventDataVerification',
                hideInMenu: true,
              },
            ],
          },
          // 数据分析
          {
            path: '/dataAnalysis',
            name: 'dataAnalysis',
            icon: 'flag',
            routes: [
              // 概览
              {
                path: '/dataAnalysis/overviewTracking',
                name: 'overviewTracking',
                component: './overviewTracking',
              },
              // 事件
              {
                path: '/dataAnalysis/eventAnalysis',
                name: 'eventAnalysis',
                component: './eventAnalysis',
              },
            ],
          },
          // 埋点数据
          {
            path: '/information',
            name: 'information',
            icon: 'database',
            component: './information',
          },
          // 埋点文档
          {
            path: '/documents',
            name: 'documents',
            icon: 'file-text',
            routes: [
              // 手工埋点码表
              {
                path: '/documents/stopwatch',
                name: 'stopwatch',
                component: './stopwatch',
              },
              // 全埋点码表
              {
                path: '/documents/allStopwatch',
                name: 'allStopwatch',
                component: './allStopwatch',
              },
              // 埋点映射
              {
                path: '/documents/relevance',
                name: 'relevance',
                component: './relevance',
              },
              // 页面管理
              {
                path: '/documents/pageManagement',
                name: 'pageManagement',
                component: './pageManagement',
              },
              // 全埋点-页面/区块管理
              {
                path: '/documents/blockManagement',
                name: 'blockManagement',
                component: './blockManagement',
              },
              // 全埋点-按钮位置管理
              {
                path: '/documents/eventManagement',
                name: 'eventManagement',
                component: './eventManagement',
              },
              // 全埋点-系统字段值管
              {
                path: '/documents/fieldManagement',
                name: 'fieldManagement',
                component: './fieldManagement',
              },
            ],
          },
          // 埋点需求
          {
            path: '/demand',
            name: 'demand',
            icon: 'schedule',
            routes: [
              // 埋点需求提交
              {
                path: '/demand/submit',
                name: 'submit',
                component: './demand',
              },
              // 埋点需求审核
              {
                path: '/demand/audit',
                name: 'audit',
                component: './demand',
              },
            ],
          },
          // 埋点管理
          {
            path: '/buryingPoint',
            name: 'buryingPoint',
            icon: 'flag',
            routes: [
              // 埋点元素管理
              {
                path: '/buryingPoint/sourceManagement',
                name: 'sourceManagement',
                component: './sourceManagement',
              },
            ],
          },
          {
            component: '404',
          },
        ],
      },
    ],
  },
];
