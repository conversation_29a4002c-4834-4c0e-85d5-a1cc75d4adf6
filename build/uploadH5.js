const Promise = require('bluebird');
const path = require('path');
const { argv } = require('yargs');
const tar = require('tar');
const fse = require('fs-extra');
const fs = Promise.promisifyAll(require('fs'));
const https = require('https');
const log = require('./log');
const deployConfig = require('../config/deploy.config.js');
const FormData = require('form-data');

/**
 * 上传文件到oss
 * @param {string} tenantId
 * @param {string} filepath
 * @param {string} destPath
 */
function uploadH5(tenantId, filepath, destPath, isPublic, isSourceMap = false) {
  const startUploadTime = new Date().getTime();
  return new Promise((resolve, reject) => {
    let url = 'https://admin-daily.yueyuechuxing.cn';
    url += isPublic ? '/api/upload' : `/admin2/devops/cdn/h5/${tenantId}?parentPath=${destPath}`;

    const tarStat = fs.statSync(filepath);

    const data = new FormData();
    data.append('platform', deployConfig.system_short_name);
    data.append('file', fs.createReadStream(filepath));

    if (isSourceMap) {
      url = 'https://delivery-test.yueyuechuxing.cn/devopsApi/open/upload_source_map';
      const base_path = (isPublic ? 'webstatic' : 'cdntest') + '.yueyuechuxing.cn/yueyue/admin';
      data.append('base_path', base_path);
    }

    const option = {
      timeout: 120000, // 120秒超时
      method: 'post', // 请求类型
      headers: data.getHeaders(),
    };

    let sent = 0;

    const req = https.request(url, option, res => {
      res.on('data', d => {
        const now = new Date().getTime();
        const serverResTime = now - sent;
        const totalTime = now - startUploadTime;
        let data = null;
        try {
          data = JSON.parse(d);
        } catch (e) {
          console.log('JSON.parse 序列化化失败', e);
        }
        console.log('总计耗时：', totalTime);
        console.log('上传耗时：', totalTime - serverResTime);
        console.log('服务器相应时间：', serverResTime);
        if (res.statusCode === 200) {
          if (data && data.code === 1) {
            return resolve(data);
          }
        }
        console.log('d', d, d.toString());
        reject(data);
      });

      res.on('error', err => {
        console.log('========');
        console.log('错误信息-all', err);
        console.log('========');
        let data = null;
        try {
          data = JSON.parse(err);
        } catch (e) {
          console.log('error错误信息 JSON.parse 序列化化失败', e);
        }
        reject(data);
      });
    });

    req.on('drain', function(d) {
      // console.log(req.connection._bytesDispatched)
      // console.log('已发送', req.socket.bytesWritten)
      sent = new Date().getTime();
    });

    req.on('end', function(d) {
      // console.log(req.connection._bytesDispatched)
      // console.log('已发送', tarStat.size)
      sent = new Date().getTime();
    });

    /* setInterval(() => {
            console.log(req.connection.bytesWritten)
        }, 250) */

    req.on('error', function(d) {
      console.log('响应错误信息', d);
      reject();
      sent = new Date().getTime();
    });

    data.pipe(req);
  });
}

/**
 * 压缩文件
 * @param {string} dest
 * @param {string} clientType
 */
function tarFiles(dest, clientType) {
  if (!fse.pathExistsSync(dest)) {
    fse.mkdirpSync(dest);
  }
  const filepath = `${dest}/${clientType}.tar.gz`;
  return tar
    .c(
      {
        file: filepath,
        cwd: dest,
        gzip: true,
        filter: fp => !fp.startsWith('.') && !/\.(json|md|gz)$/.test(fp),
      },
      fse.readdirSync(dest),
    )
    .then(() => filepath);
}

async function mian(tenantId, output, destPath, isPublic) {
  log.info('开始压缩文件...');
  const name = destPath.replace(/\//g, '_');
  const fp = await tarFiles(output, name);
  log.info(`压缩文件结束，文件路径：${fp}`);
  log.info('开始上传文件...');
  const res = await uploadH5(tenantId, fp, destPath, isPublic);
  if (deployConfig.cdn_vesion) {
    log.info('上传成功！！');
    console.log(
      `入口文件：${`${res?.data}/${deployConfig.system_short_name}/${deployConfig.cdn_vesion}/`}index.html`,
    );
    console.log(
      `静态文件目录：${`${res?.data}/${deployConfig.system_short_name}/${deployConfig.cdn_vesion}`}/`,
    );
  } else {
    console.log(`上传成功！！>> 访问路径：${res}`);
  }
}

if (!argv.output) {
  throw Error('output参数缺失');
}
if (!argv.tenantId) {
  throw Error('tenantId参数缺失');
}
if (!argv.destPath) {
  throw Error('destPath参数缺失');
}
// if (argv.output && !path.isAbsolute(argv.output)) {  // FIXME: argv.output 修改为非绝对路径
if (!argv.output) {
  throw Error('output参数不合法：应该是个合法路径...');
}
const isPublic = argv.env && argv.env === 'public';
let promiseError = '';
const ct = setInterval(() => {
  if (promiseError) {
    throw Error(promiseError);
  }
}, 300);

// FIXME: argv.output 修改为非绝对路径
// mian(argv.tenantId, argv.output, argv.destPath, isPublic).then(() => {
mian(argv.tenantId, path.resolve(argv.output), argv.destPath, isPublic)
  .then(() => {
    // log.info('部署成功！！！');
    clearInterval(ct);
  })
  .catch(err => {
    log.error(err);
    promiseError = err;
  });
