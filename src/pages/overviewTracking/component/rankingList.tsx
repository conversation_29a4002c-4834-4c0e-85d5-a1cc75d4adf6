import React, { useEffect, useState } from 'react';
import { Progress, Tooltip } from '@blmcp/ui';
import { Divider } from 'antd';
import styles from '../indexSecEd.less';


const RankingList = (
  {
    colors = '', // 进度条的颜色
    rankingType = 1, // 排行榜类型
    angleOfView = 1, // 1:全局视角; 2:局部视角
    progressChange,
    progressList = [],
    progressInfo = {
      nameList: '',
      isShowText: false, // 是否显示提示文字
      tolTipText: '', // 提示文字
    },
  }) => {
  const [getIndex, SetIndex] = useState(10000)

  const progressOneClick = (item, index) => {
    SetIndex(index)
    // 全局视角下无法触发点击事件
    if (angleOfView !== 1) {
      const onItem = {
        ...item,
        ...progressInfo,
        rankingType,
      }
      // 将被点击的卡片数据传给父组件进行接口调取 以更新页面/事件的排行
      progressChange(onItem)
    }
  }
  // 动态获取排行榜中每条标题模块的宽度
  const getWidth = ids => {
    const divElement = document.getElementById(`${ids}`); // 通过id获取<div>元素
    // 存在divElement这个dom节点才能获取该节点的宽度
    if (divElement) {
      const computedStyle = getComputedStyle(divElement); // 获取计算后的样式对象
      const boxWidth = computedStyle.width; // 获取元素的高度值 默认带px
      return { width: `calc(100% - 20px - ${boxWidth})` }
    }
      return { width: 'calc(100% - 150px)' }
  }
  const progressDom = () => progressList.map((item, index) => {
      let num = 0
      let widthNum = 0
      if (progressList[0].cntEventPvs) {
        num = Number((item.cntEventPvs / progressList[0].cntEventPvs).toFixed(4))
      }
      if (num && 100 * Number(num) < 0.5) {
        widthNum = 0.5
      } else {
        widthNum = 100 * Number(num)
      }
      return (
        <div
          className={`${styles.progressOne}
                     ${(angleOfView === 2 && rankingType !== 3 && index !== getIndex) ? styles.hoverCss : ''}
                     ${(angleOfView === 2 && rankingType !== 3 && index === getIndex) ? styles.clickCss : ''}`
          }
          onClick={() => progressOneClick(item, index)}
        >
          <div className={styles.progressOneName}>
            <Tooltip title={item.progressName} placement="top" arrow={false} className={styles.progressName}>
              <span
                className={styles.progressName}
                style={ getWidth(item.progressId) }>
                {index + 1} {item.progressName}
              </span>
            </Tooltip>
            {/* 这里需要保证ID的唯一性 */}
            <div id={`${item.progressId}`} className={styles.progressTopNum}>
              pv: <span>{item.cntEventPvs}</span>
              <Divider type="vertical"/>
              uv: <span>{item.cntEventUvs}</span>
            </div>
          </div>
          <Progress percent={widthNum} showInfo={false} strokeColor={colors} />
        </div>
      )
    })


  useEffect(() => {
    SetIndex(10000)
  }, [progressList])
    return (
    <div className={ styles.rankingBox }>
      <div style={{ padding: '0 20px' }}>
         <span className={ styles.rankTitleText }>
           {progressInfo?.nameList}
         </span>
        <span className={ styles.rankTitleTextBig }>
          {
            (() => {
              if (rankingType === 1) {
                return '平台排行TOP10'
              } if (rankingType === 2) {
                return '页面TOP10'
              }
                return '事件TOP10'
            })()
          }
        </span>
      </div>
      {
        progressInfo?.isShowText ? (
          <div style = {{ lineHeight: '350px', textAlign: 'center' }}>
            { progressInfo.tolTipText}
          </div>
        ) : (
          <>
            {
              progressList.length ? (
                <div className={styles.progressBox}>
                  { progressDom() }
                </div>
              ) : (<div style = {{ lineHeight: '350px', textAlign: 'center' }}> 暂无数据 </div>)
            }
          </>
        )
      }

    </div>
  )
}

export default RankingList
