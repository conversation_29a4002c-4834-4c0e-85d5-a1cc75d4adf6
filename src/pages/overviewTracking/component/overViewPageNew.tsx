import React, { useEffect, useState } from 'react';
import { Affix, Col, DatePicker, Form, Row, Segmented, Select } from '@blmcp/ui';
import { StyleProvider } from '@ant-design/cssinjs';
import '@blmcp/ui/dist/lib/index.css';
import moment from 'moment';
import dayjs from 'dayjs';
import styles from '../indexSecEd.less';
import DataCardItemSecEd from './DataCardItemSecEd';
import LinechartSecEd from './linechartSecEd';
import RankingList from './rankingList';
import {
  generalCkEventTop10Data,
  generalPageTop10Data,
  generalTenantTop10Data,
  queryBoardData,
  queryPageList,
  queryTenantListDataTmpNewV2,
  queryTimingSequenceBoardData,
} from '../../../services/tracking';
import TenantSelectNew from '@/components/overViewTrackingCom/tenantSelectNew';
import TenantSelectNewPage from '@/components/overViewTrackingCom/tenantSelectNewPage';
import { connect } from 'dva';

const OverViewPageNew = props => {
  const { RangePicker } = DatePicker;
  const [form] = Form.useForm();
  const { platformList } = props;
  const DEFAULTLIST = ['2$$1'];

  // 初始化状态
  const [isInit, setIsInit] = useState(false);
  // 获取businessType状态
  const businessType = JSON.parse(sessionStorage.getItem('businessType'));
  // 默认时间 15 - T-1
  const [searchTime, SetSearchTime] = useState([
    moment()
      .subtract(15, 'day')
      .startOf('day')
      .format('YYYY-MM-DD'),
    moment()
      .subtract(1, 'day')
      .startOf('day')
      .format('YYYY-MM-DD'),
  ]);

  const [platformOptions, setPlatformOptions] = useState([]);
  // 平台组件loading状态
  const [spinning, setSpinning] = useState(false);
  // 平台下拉数据
  const [tenantIdData, SetTenantIdData] = useState([]);
  // 页面下拉框数据
  const [eventPage, setEventPage] = useState([]);
  const [cardItemsData, SetCardItemsData] = useState([{}]);
  const [chartKey, SetChartKey] = useState(1001);
  // 折线图数据
  const [chartInfo, SetChartInfo] = useState({});
  const [lineChartDate, SetLineChartDate] = useState([]);
  const lineTitleNames = [
    { key: 1001, count: 1005, titleName: '用户访问次数', units: '次' },
    { key: 1002, count: 1006, titleName: '访问用户人数', units: '人' },
    { key: 1003, count: 1007, titleName: '人均访问量', units: '次/人' },
    { key: 1004, count: 1008, titleName: '人均访问时长', units: '' },
  ];
  // 排行榜数据
  const platformInfo = {
    nameList: '',
    tolTipText: '',
    isShowText: false,
  };
  const [pageInfo, SetPageInfo] = useState({
    nameList: '',
    tolTipText: '',
    isShowText: false,
  });
  const [eventInfo, SetEventInfo] = useState({
    nameList: '',
    tolTipText: '',
    isShowText: false,
  });
  const [platformProgressList, SetPlatformProgressList] = useState([]);
  const [pageProgressList, SetPageProgressList] = useState([]);
  const [eventProgressList, SetEventProgressList] = useState([]);
  // 视角值
  const [angleOfView, SetAngleOfView] = useState(1);
  // 设置时间禁用项
  const disabledDate = current => {
    // 过去90天
    const historyTime = moment()
      .subtract(90, 'day')
      .startOf('day');
    const todayTime = moment()
      .subtract(1, 'day')
      .startOf('day');
    // 选择过去90的数据 current会精确到时分秒去比较
    return current && (current < historyTime || current > todayTime);
  };
  // 平台类型
  const [valueType, setValueType] = useState(2);
  // 选中的平台集合
  const [taskCarTeamIdSaved, setTaskCarTeamIdSaved] = useState([]);
  // 组件回调返回数据
  const [tenantCondition, setTenantCondition] = useState({
    eliminate: false,
    platformType: 2, // 平台类型
    selectIdList: DEFAULTLIST, // 选中集合的Id
    selectLabelList: [],
  });
  // 新页面字段
  const [pageCondition, setPageCondition] = useState({
    eliminate: false, // 排除状态
    selectIdList: [], // 选中集合的Id
    selectLabelList: [],
  });
  // 默认的页面数据
  const [defaultPageIds, setDefaultPageIds] = useState([]);

  // 获取平台下拉数据
  const getQueryTenantListData = async (defaultVal = false, type = 2) => {
    setSpinning(true);
    const { appType } = form.getFieldsValue();
    const params = {
      businessType,
      applicationType: appType,
      searchType: type, // 1-按平台名称，2-按平台分组
    };
    await queryTenantListDataTmpNewV2(params).then(res => {
      if (res?.code === 1 && res?.data) {
        let arr = [];
        res.data.forEach(item => {
          let info = {
            ...item,
            tenantId: type === 2 ? `2$$${item.key}` : item.key,
            tenantIdAndName: item.keyValue ?? item.value ?? '',
          };
          arr.push(info);
        });
        // 更新平台下拉框数据
        SetTenantIdData(arr);
        // 非点击平台名称/分组时执行
        if (defaultVal) {
          // setValueType(2);
          type === 2 && setTaskCarTeamIdSaved(DEFAULTLIST);
        } else {
          // 清空选中的平台
          form.setFieldsValue({ tenantIds: [] });
        }
      }
      setSpinning(false);
    });
  };

  const switchTag = (type = 2) => {
    // setValueType(type);
    getQueryTenantListData(false, type);
  };
  // 获取页面下拉数据
  const handleChangeEventPageId = (value = '') => {
    const searchParams = {
      businessType,
      applicationType: form.getFieldValue('appType'),
      pageNum: 1,
      pageSize: 10000,
      keyWord: value,
    };
    queryPageList(searchParams).then(res => {
      if (res?.code === 1 && res?.data && res.data.items) {
        const dataList = res.data.items.map(i => {
          i.pageUniqueId = i.pageUniqueId.toString();
          return i;
        });
        // 更新页面下拉框数据
        setEventPage(dataList);
        // 更新页面输入框默认值
        setDefaultPageIds([]);
      }
    });
  };

  // 参数拼接
  const getParams = () => {
    const { pageUniqueIds, tenantIds, appType } = form.getFieldsValue();
    const params = {
      businessType,
      appType: appType,
      startTime: searchTime[0] || '',
      endTime: searchTime[1] || '',
    };
    return params;
  };
  const getParamsNew = () => {
    const { appType } = form.getFieldsValue();
    let params = {
      businessType,
      appType: appType,
      boardModuleType: '',
      tenantCondition: null,
      pageCondition: null,
    };
    if (tenantCondition?.selectIdList && tenantCondition?.selectIdList.length) {
      params.tenantCondition = {
        comparator: tenantCondition?.eliminate ? 'NOT_IN' : 'IN',
        valueType: tenantCondition?.platformType,
        values: [],
      };
      if (tenantCondition?.platformType === 2) {
        let arr = [];
        tenantCondition?.selectIdList?.forEach(item => {
          let parts = item.split('$$');
          parts?.[1] && arr.push(parts?.[1]);
        });
        params.tenantCondition.values = arr;
        params.tenantCondition.comparator = 'IN';
      } else {
        params.tenantCondition.values = tenantCondition?.selectIdList;
      }
    }
    if (pageCondition?.selectIdList && pageCondition?.selectIdList.length) {
      params.pageCondition = {
        comparator: pageCondition?.eliminate ? 'NOT_IN' : 'IN',
        values: pageCondition?.selectIdList,
      };
    }
    return params;
  };

  // 获取平台的排行
  const getPlatformProgressData = async () => {
    const paramsNew = await getParamsNew();
    const params = getParams();
    if (params) {
      generalTenantTop10Data({ ...paramsNew, ...params }).then(res => {
        if (res.code === 1 && res.data) {
          // progressName
          const progressData = res.data.map(i => ({
            progressId: i.tenantId,
            progressName: i.tenantName,
            cntEventPvs: i.cntEventPvs,
            cntEventUvs: i.cntEventUvs,
          }));
          SetPlatformProgressList(progressData);
        } else {
          SetPlatformProgressList([]);
        }
      });
    }
  };
  // 获取页面的排行
  const getPageProgressData = async params => {
    if (params) {
      const paramsNew = await getParamsNew();
      generalPageTop10Data({ ...paramsNew, ...params }).then(res => {
        if (res.code === 1 && res.data) {
          const progressData = res.data.map(i => ({
            progressId: i.pageUniqueId,
            progressName: i.pageName,
            cntEventPvs: i.cntEventPvs,
            cntEventUvs: i.cntEventUvs,
          }));
          SetPageProgressList(progressData);
        } else {
          SetPageProgressList([]);
        }
      });
    }
  };
  // 获取事件的排行
  const getEventProgressData = async params => {
    if (params) {
      const paramsNew = await getParamsNew();
      generalCkEventTop10Data({ ...paramsNew, ...params }).then(res => {
        if (res.code === 1 && res.data) {
          const progressData = res.data.map(i => ({
            progressId: i.eventId,
            progressName: i.eventName,
            cntEventPvs: i.cntEventPvs,
            cntEventUvs: i.cntEventUvs,
          }));
          SetEventProgressList(progressData);
        } else {
          SetEventProgressList([]);
        }
      });
    }
  };
  // 平台排行数据变化
  useEffect(() => {
    if (!isInit) {
      return;
    }
    // 全局视角
    if (angleOfView == 1) {
      // 将页面和事件模块隐藏产品定义的文案
      SetPageInfo({ nameList: '全部平台/', tolTipText: '', isShowText: false });
      SetEventInfo({ nameList: '全部平台/全部页面/', tolTipText: '', isShowText: false });
    } else {
      // 下钻分析 判断一下平台排行是否有数据, 有数据的时候 页面显示-请点击字样提示, 无数据-空白
      if (platformProgressList.length) {
        SetPageInfo({ nameList: '', tolTipText: '请单击选中左侧平台', isShowText: true });
      } else {
        SetPageInfo({ nameList: '', tolTipText: '', isShowText: true });
      }
      // 此时事件显示提示语且提示语为: 空白
      SetEventInfo({ ...eventInfo, nameList: '', tolTipText: '', isShowText: true });
    }
  }, [platformProgressList]);
  // 页面排行数据变化
  useEffect(() => {
    if (!isInit) {
      return;
    }
    if (angleOfView == 1) {
      // 将页面和事件模块隐藏产品定义的文案
      SetPageInfo({ nameList: '全部平台/', tolTipText: '', isShowText: false });
      SetEventInfo({ nameList: '全部平台/全部页面/', tolTipText: '', isShowText: false });
    } else {
      // 下钻分析的时候 页面排行数据不为空,事件排行的提示语为: '请单击选中左侧页面', 为空: 提示语空白
      if (pageProgressList.length) {
        SetEventInfo({ nameList: '', tolTipText: '请单击选中左侧页面', isShowText: true });
      } else {
        SetEventInfo({ nameList: '', tolTipText: '', isShowText: true });
      }
    }
  }, [pageProgressList]);
  // 下钻分析时通过点击获取的租户
  const [progressTenantId, SetProgressTenantId] = useState('');
  // 排行榜各卡片点击事件
  const progressClick = item => {
    // 下钻分析时才可以触发点击事件 && 排行榜类型-平台
    if (angleOfView !== 1 && item.rankingType === 1) {
      // 点击某个平台 查询页面排行数据 清空事件的排行
      const params = getParams();
      params.tenantCondition = {
        comparator: 'IN',
        valueType: 1, // 1: 平台名称
        values: [item.progressId] || [],
      };
      SetProgressTenantId(item.progressId || '');
      getPageProgressData(params);
      SetPageInfo({
        ...pageInfo,
        isShowText: false,
        nameList: item.progressName ? `${item.progressName}/` : '',
      });
    } else if (angleOfView !== 1 && item.rankingType === 2) {
      // 点击某个页面 查询页面下事件的排行
      const params = getParams();
      // params.pageUniqueIds = [item.progressId] || [];
      // params.tenantIds = [progressTenantId];
      params.tenantCondition = {
        comparator: 'IN',
        valueType: 1, // 1: 平台名称
        values: [progressTenantId],
      };
      params.pageCondition = {
        comparator: 'IN',
        values: [item.progressId] || [],
      };
      getEventProgressData(params);
      // 查找事件TOP10的前半段标题
      let name = '';
      if (pageInfo.nameList && item.progressName) {
        name = `${pageInfo.nameList}${item.progressName}/`;
      } else if (pageInfo.nameList) {
        name = pageInfo.nameList;
      } else {
        name = item.progressName ? `${item.progressName}/` : '';
      }
      // 将事件TOP10的前半段标题进行更新
      SetEventInfo({
        ...eventInfo,
        nameList: name,
        isShowText: false,
      });
    }
  };
  // 视角切换
  const segmentedChange = value => {
    SetAngleOfView(value);
  };
  // 视角变化后更新数据
  useEffect(() => {
    if (!isInit) {
      return;
    }
    // 全局视角
    if (angleOfView === 1) {
      getPlatformProgressData();
      getPageProgressData(getParams());
      getEventProgressData(getParams());
    } else {
      // 获取平台的排行 (下钻分析的时候 不通过点击就不能查询和展示页面&事件数据)
      getPlatformProgressData();
    }
  }, [angleOfView]);

  // 时序看板
  const getQueryTimingSequenceBoardData = async val => {
    const { pageUniqueIds, tenantIds, appType } = form.getFieldsValue();
    const cardKey = val || chartKey;
    const item = lineTitleNames.find(i => i.key === cardKey);
    const paramsNew = await getParamsNew();
    let params = {
      businessType,
      appType: appType,
      boardModuleType: item?.count,
      startTime: searchTime[0] || '',
      endTime: searchTime[1] || '',
    };
    queryTimingSequenceBoardData({ ...paramsNew, ...params }).then(res => {
      if (res.code === 1 && res.data) {
        const {
          boardModuleType,
          cntMetricsValue,
          cntMetricsUnit,
          avgMetricsValue,
          avgMetricsUnit,
          data,
        } = res.data;
        // metricsValue转成数字类型且为null的时候置为0, 否则图表渲染会有问题
        data.forEach(i => {
          i.metricsValue = Number(i.metricsValue) || 0;
        });
        SetLineChartDate(data);
        SetChartInfo({
          count: Number(boardModuleType),
          units: item.units,
          titleName: item.titleName,
          cntMetricsValue, // 总和
          cntMetricsUnit, // 总和单位
          avgMetricsValue, // 均值
          avgMetricsUnit, // 均值单位
        });
      } else {
        // 接口获取失败
        SetLineChartDate([]);
        SetChartInfo({
          count: item.count,
          units: item.units,
          titleName: item.titleName,
        });
      }
    });
  };
  // card切换事件
  const cardHandleClick = val => {
    SetChartKey(Number(val));
    // 调取接口改变折线图数据
    getQueryTimingSequenceBoardData(val);
  };

  // 事件概览固值看板
  const getQueryBoardData = async () => {
    const params = await getParamsNew();
    queryBoardData(params).then(res => {
      if (res.code === 1 && res.data) {
        SetCardItemsData(res.data);
      } else {
        SetCardItemsData([{}]);
      }
    });
  };

  const getData = () => {
    const params = getParams();
    // 固值看板
    getQueryBoardData();
    getQueryTimingSequenceBoardData(null);
    // 平台排行
    getPlatformProgressData();
    // 全局视角
    if (angleOfView === 1) {
      getPageProgressData(params);
      // SetPageInfo({ ...pageInfo, nameList: '全部平台/', isShowText: false })
      getEventProgressData(params);
      // SetEventInfo({ ...eventInfo, nameList: '全部平台/全部页面/', isShowText: false })
    }
  };

  // form表单改变
  const onValuesChange = (values, allValues) => {
    // 业务线改变的时候 更新平台和页面下拉数据
    if (values.appType) {
      getQueryTenantListData(true);
      handleChangeEventPageId();
      // 昨日数据卡片重置为第一个
      SetChartKey(1001);
      // 排行榜重置为全局视角
      SetAngleOfView(1);
      // 重置选中的租户
      form.setFieldsValue({ tenantIds: [] });
      // 重置选中的页面
      form.setFieldsValue({ pageUniqueIds: [] });
    }
    // 时间改变在单独的时间Change事件中触发 因这里获取到时时间格式不正确
    if (!values.times) {
      getData();
    }
  };
  // 时间组件改变事件
  const onChangeTime = (date, dateString) => {
    SetSearchTime(dateString);
  };

  // 平台的回调数据
  const selectCallBack = info => {
    const forms = form.getFieldsValue();

    // 默认值
    setTaskCarTeamIdSaved(info?.selectIdList);
    setTenantCondition(info);
    setValueType(info?.platformType);

    // 确认后执行一下form表单的回调
    // onValuesChange({ tenantIds: info?.selectIdList }, null);
  };

  // 时间改变重新查询数据
  useEffect(() => {
    if (!isInit) {
      return;
    }
    getData();
  }, [searchTime, JSON.stringify(tenantCondition), JSON.stringify(pageCondition)]);
  useEffect(() => {
    if (!platformList.length) {
      return;
    }
    // 初始化应用下拉数据
    const appTypeArr = platformList?.map(item => {
      return {
        value: item.applicationType,
        label: item.applicationDesc,
      };
    });
    setPlatformOptions(appTypeArr);
    // 设置应用appType默认值
    const appTypeCopy = appTypeArr?.[0]?.value ?? null;
    form.setFieldValue('appType', appTypeCopy);
    form.setFieldValue('tenantIds', [valueType]);
    onValuesChange({ appType: appTypeCopy }, null);
  }, [JSON.stringify(platformList)]);
  // 初次进入页面
  useEffect(() => {
    segmentedChange(1);
    // 开启初始化状态
    setIsInit(true);
    console.log('setIsInit');
  }, []);

  const [container, setContainer] = React.useState<HTMLDivElement | null>(
    document.querySelector('#root .ant-layout .ant-layout'),
  );

  // 页面的回调数据
  const selectCallBackPage = info => {
    // 更新form表单中页面选项的值
    form.setFieldValue('pageUniqueIds', info?.selectIdList ?? []);
    // 更新页面输入框默认值
    setDefaultPageIds(info?.selectIdList ?? []);
    // 将返回值存入pageCondition
    setPageCondition(info);
    // 确认后执行一下form表单的回调
    // onValuesChange({ pageUniqueIds: info?.selectIdList }, null);
  };

  return (
    <div className={styles.overViewBox}>
      <StyleProvider hashPriority="high">
        <Affix offsetTop={64} target={() => container}>
          <div
            style={{
              minHeight: '50px',
              width: '100%',
              backgroundColor: '#F7F8FA',
              padding: '10px 0',
            }}
          >
            <Form
              form={form}
              layout="inline"
              labelCol={{ span: 0 }}
              wrapperCol={{ span: 24 }}
              onValuesChange={onValuesChange}
              autoComplete="off"
            >
              <Form.Item name="appType">
                <Select options={platformOptions} style={{ width: '200px' }}></Select>
              </Form.Item>

              <Form.Item name="times">
                <RangePicker
                  // value={searchTime}
                  allowClear={false}
                  defaultValue={[
                    dayjs(searchTime[0], 'YYYY-MM-DD'),
                    dayjs(searchTime[1], 'YYYY-MM-DD'),
                  ]}
                  disabledDate={disabledDate}
                  format="YYYY-MM-DD"
                  onChange={onChangeTime}
                />
              </Form.Item>
              <Form.Item name="tenantIds">
                <TenantSelectNew
                  form={form}
                  placeholder={'平台名称'}
                  spinning={spinning}
                  valueType={valueType}
                  platformEliminate={tenantCondition?.eliminate}
                  tenantOptions={tenantIdData}
                  defaultValueIds={taskCarTeamIdSaved}
                  switchTag={type => switchTag(type)}
                  selectCallBack={info => selectCallBack(info)}
                ></TenantSelectNew>
              </Form.Item>
              <Form.Item name="pageUniqueIds">
                <TenantSelectNewPage
                  title={'页面选择'}
                  platformEliminate={pageCondition?.eliminate} // 排除字段
                  tenantOptions={eventPage} // 数据源
                  fieldNames={{ title: 'pageName', key: 'pageUniqueId' }}
                  defaultValueIds={defaultPageIds}
                  selectCallBack={info => selectCallBackPage(info)}
                ></TenantSelectNewPage>
              </Form.Item>
            </Form>
          </div>
        </Affix>
        <Row gutter={10}>
          <Col span={6}>
            <DataCardItemSecEd
              cardKey={1001}
              showChart={chartKey === 1001}
              cardItemsData={cardItemsData}
              cardHandleClick={cardHandleClick}
            />
          </Col>
          <Col span={6}>
            <DataCardItemSecEd
              cardKey={1002}
              showChart={chartKey === 1002}
              cardItemsData={cardItemsData}
              cardHandleClick={cardHandleClick}
            />
          </Col>
          <Col span={6}>
            <DataCardItemSecEd
              cardKey={1003}
              showChart={chartKey === 1003}
              cardItemsData={cardItemsData}
              cardHandleClick={cardHandleClick}
            />
          </Col>
          <Col span={6}>
            <DataCardItemSecEd
              cardKey={1004}
              showChart={chartKey === 1004}
              cardItemsData={cardItemsData}
              cardHandleClick={cardHandleClick}
            />
          </Col>
        </Row>
        {/*  折线图部分  */}
        <LinechartSecEd
          chartTime={searchTime}
          chartDate={lineChartDate}
          chartInfo={chartInfo}
        ></LinechartSecEd>

        {/*  排行榜  */}
        <div className={styles.rankingText}>
          <span>排行榜</span>
          <Segmented
            value={angleOfView}
            options={[
              {
                label: '全局视角',
                value: 1,
              },
              {
                label: '下钻分析',
                value: 2,
              },
            ]}
            className={styles.segmenteds}
            onChange={segmentedChange}
          />
        </div>
        <Row gutter={20}>
          <Col span={8}>
            <RankingList
              colors="#4686ED"
              rankingType={1}
              angleOfView={angleOfView}
              progressInfo={platformInfo}
              progressList={platformProgressList}
              progressChange={item => {
                progressClick(item);
              }}
            ></RankingList>
          </Col>
          <Col span={8}>
            <RankingList
              colors="#11C26D"
              rankingType={2}
              angleOfView={angleOfView}
              progressInfo={pageInfo}
              progressList={pageProgressList}
              progressChange={item => {
                progressClick(item);
              }}
            ></RankingList>
          </Col>
          <Col span={8}>
            <RankingList
              colors="#FF6B3B"
              rankingType={3}
              angleOfView={angleOfView}
              progressInfo={eventInfo}
              progressList={eventProgressList}
              progressChange={() => {
                console.log('事件卡片不可点击');
              }}
            ></RankingList>
          </Col>
        </Row>
      </StyleProvider>
    </div>
  );
};

export default connect(({ user }) => ({
  platformList: user.platformList,
}))(OverViewPageNew);
