import React, { useEffect, useRef } from 'react';
import { Line } from '@antv/g2plot';
import { Divider } from 'antd';
import moment from 'moment';
import styles from '../index.less'


const drawChart = (ref, chartInfo) => {
  const line = new Line(ref.current, {
    data: chartInfo.data,
    padding: 'auto',
    xField: 'day',
    yField: 'metricsValue',
    slider: {
      start: 0,
      end: 1,
    },
    tooltip: {
      title: chartInfo.titleName,
      formatter: datum => {
        if (chartInfo.count === 1008) {
          return ({ name: datum.day, value: `${datum.metricsValue}分钟` })
        }
        return ({ name: datum.day, value: datum.metricsValue })
      },
    },
    point: {
      size: 5,
      shape: 'diamond',
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2,
      },
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: '#000',
          fill: 'red',
        },
      },
    },
  });
  line.render()
  return line
}

const LineChart = props => {
  const {
    chartTime = [],
    chartInfo = {
      count: null,
      titleName: '',
      data: [],
      cntMetricsValue: '', // 总和
      cntMetricsUnit: '', // 总和单位
      avgMetricsValue: '', // 均值
      avgMetricsUnit: '', // 均值单位
    },
  } = props
  const ref = useRef()
  const plotRef = useRef<any>()

  useEffect(() => {
    if (ref.current && chartInfo?.data) {
      if (plotRef?.current) {
        plotRef.current.changeData(chartInfo.data)
      } else {
        plotRef.current = drawChart(ref, chartInfo)
      }
    }
  }, [ref, chartInfo])

  return (
    < >
      <div className={ styles.titleTop }>
        <span className={ styles.titleText }>
          { chartInfo.titleName }
        </span>
        <span className={ styles.titleTime }>
          {`${moment(chartTime[0]).format('MM-DD')}--${moment(chartTime[1]).format('MM-DD')}`}
        </span>
      </div>
      <div style={{ margin: '12px 0' }}>
        <div style={{ display: 'inline-block' }}>
          <span className={styles.textCss}>合计</span><br/>
          <span className={styles.textNum}> { chartInfo.cntMetricsValue || 0 } </span>
          <span className={styles.textUnit}> {chartInfo.cntMetricsUnit} </span>
        </div>
        <Divider type="vertical" style={{ height: '32px', marginBottom: '25px' }}/>
        <div style={{ display: 'inline-block' }}>
          <span className={styles.textCss}>均值</span><br/>
          <span className={styles.textNum}> {chartInfo.avgMetricsValue || 0 } </span>
          <span className={styles.textUnit}> {chartInfo.avgMetricsUnit} </span>
        </div>
      </div>
      {/* 图表部分 */}
      {
        chartInfo?.data.length ? (
          <div ref={ref} style = {{ height: '400px' }}></div>
        ) : (
          <div style = {{ height: '400px', lineHeight: '400px', textAlign: 'center' }}> 暂无数据 </div>
        )
      }
    </>
  )
}
export default LineChart
