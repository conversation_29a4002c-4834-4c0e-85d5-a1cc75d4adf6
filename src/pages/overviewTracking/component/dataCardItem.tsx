import React from 'react';
import styles from '../index.less';
import RatioItem from './ratioItem';

const DataCardItem = ({ cardItemsData, cardKey, isRight = false }) => {
  const cardItemTitleEnum = [
    { key: 1001, value: '用户浏览数' },
    { key: 1002, value: '访问用户数' },
    { key: 1003, value: '人均访问量' },
    { key: 1004, value: '人均访问时长' },
  ];

  const curCardItems =
    cardItemsData && cardItemsData.find(item => Number(item?.boardModuleType) === Number(cardKey));

  const curCardItemTitle = cardItemTitleEnum.find(item => item.key === cardKey)?.value;
  return (
    <div className={isRight ? styles.dataCardRight : styles.dataCard}>
      <div className={styles.dataCardTitle}>{curCardItemTitle}</div>
      <div className={styles.dataCardMetrics}>
        {curCardItems?.metrics ?? '--'}
        {curCardItems?.metricsUnit}
      </div>
      <div>
        <span className={styles.dataCardMetricsComparison} style={{ marginRight: 10 }}>
          日环比：
          <RatioItem ratioValue={curCardItems?.metricsRt ?? '--'} />
        </span>
        <span className={styles.dataCardMetricsComparison}>
          周同比：
          <RatioItem ratioValue={curCardItems?.metricsWow ?? '--'} />
        </span>
      </div>
    </div>
  );
};
export default DataCardItem;
