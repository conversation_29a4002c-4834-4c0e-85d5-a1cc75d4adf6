import React, { useEffect, useState } from 'react';
import { Icon } from 'antd';

const RatioItem = ({ ratioValue }) => {
  const isUpRatio = Number(ratioValue) >= 0 || ratioValue === '--';
  return (
    <span style={{ color: isUpRatio ? 'rgba(34, 152, 28, 1)' : 'rgba(219, 50, 65, 1)' }}>
      {isUpRatio ? <Icon type="caret-up" /> : <Icon type="caret-down" />}
      <span>
        {ratioValue}
        {ratioValue === '--' ? '' : '%'}
      </span>
    </span>
  );
};
export default RatioItem;
