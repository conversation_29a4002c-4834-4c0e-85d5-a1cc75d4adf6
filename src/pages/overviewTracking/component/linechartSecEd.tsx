import React, { useEffect, useRef } from 'react';
import { Line } from '@antv/g2plot';
import { Divider } from 'antd';
import moment from 'moment';
import styles from '../indexSecEd.less';


const drawChart = (ref, chartInfo, chartDate) => {
  const line = new Line(ref.current, {
    data: chartDate,
    padding: 'auto',
    appendPadding: [0, 10, 0, 10],
    xField: 'day',
    yField: 'metricsValue',
    smooth: true, // 设置平滑曲线
    slider: {
      start: 0,
      end: 1,
    },
    tooltip: {
      title: chartInfo.titleName,
      showTitle: false,
      formatter: datum => {
        if (chartInfo.count === 1008) {
          return ({ name: datum.day, value: `${chartInfo.titleName}: ${datum.metricsValue}分钟` })
        }
        return ({ name: datum.day, value: `${chartInfo.titleName}: ${datum.metricsValue}` })
      },
      marker: {
        fill: '#fff',
        stroke: '#537AFF',
      },
    },
  });
  line.render()
  return line
}

const LinechartSecEd = props => {
  const {
    chartTime = [],
    chartDate = [],
    chartInfo = {
      count: null,
      titleName: '',
      units: '', // 单位
      cntMetricsValue: '', // 总和
      cntMetricsUnit: '', // 总和单位
      avgMetricsValue: '', // 均值
      avgMetricsUnit: '', // 均值单位
    },
  } = props
  const ref = useRef()
  const plotRef = useRef<any>()

  useEffect(() => {
    if (ref.current && chartDate.length) {
      if (plotRef?.current) {
        // 更新图表数据
        plotRef.current.changeData(chartDate)
        // 更新tooltip提示
        plotRef.current.update({
          tooltip: {
            formatter: datum => {
              if (chartInfo.count === 1008) {
                return ({ name: datum.day, value: `${chartInfo.titleName}: ${datum.metricsValue}分钟` })
              }
              return ({ name: datum.day, value: `${chartInfo.titleName}: ${datum.metricsValue}` })
            },
          },
        })
      } else {
        plotRef.current = drawChart(ref, chartInfo, chartDate)
      }
    }
  }, [ref, chartInfo, chartDate])


  // 处理昨日人均访问时长的数据展示样式
  const getCss = text => {
    let result = []
    if (text) {
      result = text.match(/[^0-9]+|[0-9]+/g); // 使用正则表达式匹配中文和数字部分
      return (
        <>
          {result.map(item => {
            if (isNaN(item)) {
              return <span className={styles.textUnit}>{item}</span>; // 中文部分
            }
            return <span className={styles.textNum}>{item}</span>; // 数字部分
          })}
        </>
      )
    }
      return <span className={styles.textNum}>0</span>
  }

  return (
    <div className={styles.shadow}>
      <div className={ styles.titleTop }>
        <div className={styles.titlebor}></div>
        <span className={ styles.titleText }>
          { chartInfo.titleName }
        </span>
        <Divider type="vertical"/>
        <span className={ styles.titleTime }>
          {`${moment(chartTime[0]).format('YYYY-MM-DD')} 至 ${moment(chartTime[1]).format('YYYY-MM-DD')}`}
        </span>
      </div>
      <div style={{ height: '50px', marginBottom: '20px' }}>
        <div style={{ display: 'inline-block' }}>
          <span className={styles.textCss}>累计</span><br/>
          {
            chartInfo.count !== 1008 ? (
              <span className={styles.textNum}>
                { Number(chartInfo.cntMetricsValue)
                  ? Number(chartInfo.cntMetricsValue).toLocaleString() : 0 }
                <span className={styles.textUnit}>{chartInfo.cntMetricsUnit}</span>
              </span>
            ) : (getCss(chartInfo.cntMetricsValue))
          }
          <span className={styles.textUnit}> {chartInfo.units} </span>
          {/* <span className={styles.textUnit}> {chartInfo.cntMetricsUnit}</span> */}
        </div>
        <Divider type="vertical" style={{ height: '32px', marginBottom: '25px' }}/>
        <div style={{ display: 'inline-block' }}>
          <span className={styles.textCss}>日均</span><br/>
          {
            chartInfo.count !== 1008 ? (
              <span className={styles.textNum}>
                {Number(chartInfo.avgMetricsValue)
                  ? Number(chartInfo.avgMetricsValue).toLocaleString() : 0 }
                <span className={styles.textUnit}>{chartInfo.avgMetricsUnit}</span>
              </span>
            ) : (getCss(chartInfo.avgMetricsValue))
          }
          <span className={styles.textUnit}> {chartInfo.units} </span>
          {/* <span className={styles.textUnit}> {chartInfo.avgMetricsUnit} </span> */}
        </div>
      </div>
      {/* 图表部分 */}
      <div ref={ref} style = {chartDate.length ? { height: '300px' } : { display: 'none' } }></div>
      <div style = {chartDate.length ? { display: 'none' } : { height: '300px', lineHeight: '300px', textAlign: 'center' }}> 暂无数据 </div>
    </div>
  )
}

export default LinechartSecEd
