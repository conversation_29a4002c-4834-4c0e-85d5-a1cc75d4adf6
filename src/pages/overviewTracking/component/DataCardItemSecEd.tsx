import React from 'react';
import styles from "../indexSecEd.less";
import RatioItem from "./ratioItem";
import blueBcg from '@/assets/blueBcg.png'
import purpleBcg from '@/assets/purpleBcg.png'
import greenBcg from '@/assets/greenBcg.png'
import orangeBcg from '@/assets/orangeBcg.png'

const DataCardItemSecEd = ({ cardItemsData, cardKey, showChart, cardHandleClick }) => {
  const cardItemTitleEnum = [
    { key: 1001, value: '昨日用户访问次数', units: '次', bgPic: blueBcg, bgCol: '#DDE7FA', bdCol: '#2E7EFF' },
    { key: 1002, value: '昨日访问用户人数', units: '人', bgPic: purpleBcg, bgCol: '#EDEFFF', bdCol: '#8A8CF6' },
    { key: 1003, value: '昨日人均访问量', units: '次/人', bgPic: greenBcg, bgCol: '#E4F6EB', bdCol: '#11C26D' },
    { key: 1004, value: '昨日人均访问时长', units: '', bgPic: orangeBcg, bgCol: '#FAF4E9', bdCol: '#F6BD87' },
  ];
  // 文字描述
  const curCardInfo = cardItemTitleEnum.find(item => item.key === cardKey)
  // 对应的数值
  const curCardItems = cardItemsData && cardItemsData.find(item => Number(item?.boardModuleType) === Number(cardKey));
  // 标题名称
  // const curCardItemTitle = cardItemTitleEnum.find(item => item.key === cardKey)?.value;

  // 处理昨日人均访问时长的数据展示样式
  const getCss = (text) => {
    let result = []
    if(text) {
      result = text.match(/[^0-9]+|[0-9]+/g); // 使用正则表达式匹配中文和数字部分
      return (
        <>
          {result.map(item => {
            if (isNaN(item)) {
              return <span className={styles.dataCardUnit}>{item}</span>; // 中文部分
            } else {
              return <span>{item}</span>; // 数字部分
            }
          })}
        </>
      )
    }else {
      return <span>0</span>
    }

  }

  return (
    <>
      <div
        className={styles.dataCard}
        style={ showChart ?
          {
            border: `1px solid ${curCardInfo.bdCol}`,
            backgroundColor: curCardInfo.bgCol,
            backgroundImage:`url(${curCardInfo.bgPic})`,
          } : {
            backgroundColor: curCardInfo.bgCol,
            backgroundImage:`url(${curCardInfo.bgPic})`,
          }
        }
        onClick={()=> cardHandleClick(curCardInfo.key)}
      >
        <div className={styles.dataCardTitle}>{curCardInfo.value}</div>
        <div className={styles.dataCardMetrics}>
          {
            curCardInfo.key === 1004 ? ( getCss(curCardItems?.metrics))
                : (
                  <>
                    {curCardItems?.metrics.toLocaleString() ?? 0 }
                    <span className={styles.dataCardUnit}>
                      {curCardItems?.metricsUnit}{curCardInfo?.units}
                    </span>
                  </>
                )
          }

        </div>
        <div style={{ margin: '5px 0' }}>
          <span className={styles.dataCardMetricsComparison} style={{ marginRight: 10 }}>
            日环比：
            <RatioItem ratioValue={curCardItems?.metricsRt ?? '--'} />
          </span>
            <span className={styles.dataCardMetricsComparison}>
            周同比：
            <RatioItem ratioValue={curCardItems?.metricsWow ?? '--'} />
          </span>
        </div>
        {
          showChart ? (
            <div
              className={styles.square}
              style={{
                  borderBottom: `1px solid ${curCardInfo.bdCol}`,
                  borderRight: `1px solid ${curCardInfo.bdCol}`,
                  backgroundColor: curCardInfo.bgCol
              }}
            ></div>) : (<></>)
        }
      </div>
    </>
  )
}

export default DataCardItemSecEd
