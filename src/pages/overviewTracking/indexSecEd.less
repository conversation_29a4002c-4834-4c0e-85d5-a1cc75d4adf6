.overViewStyle {
  background-color: #F7F8FA !important;
}
.overViewBox{}
.selectPop {
  overflow: visible !important;
}
.selectNotFoundContent {
  color: #606266;
  height: 50px;
  line-height: 50px;
  text-align: center;
}

.dataCard {
  margin: 16px 0;
  padding: 15px 0 15px 15px;
  height: 128px;
  border-radius: 8px;
  //background-color: #DDE7FA;
  cursor: pointer;
  transition: all 0.3s ease;
  //background-image: url("../../assets/overViewPic/purpleBcg.png");
  background-size: 100% 100%;
  .dataCardTitle{
    color: rgba(0, 0, 0, 0.9);
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
  }
  .dataCardTime{
    margin: 6px 0 5px 0;
    font-size: 12px;
    line-height: 12px;
  }
  .dataCardMetrics{
    margin: 15px 0;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
    .dataCardUnit{
      margin-left: 3px;
      font-size: 15px;
      color: #606266;
    }
  }
  .dataCardMetricsComparison{
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
  }
}
// 点击某个卡片的效果
  .square {
    position: absolute;
    bottom: 9px;
    left: calc(50% - 11px);
    width: 15px;
    height: 15px;
    transform: rotate(45deg);
  }
// 图表部分
.shadow {
  padding: 10px 20px;
  background-color: #FFF;
  border-radius: 8px;
}
.titleTop {
  height: 40px;
  line-height: 40px;
  .titlebor{
    display: inline-block;
    margin-right: 5px;
    height: 14px;
    border-radius: 1px;
    border: 1px solid #276BEC;
  }
  .titleText {
    font-size: 16px;
    font-weight: 700;
    color: #3D3D3D;
  }
  .titleTime {
    font-size: 13px;
    color: #909399;
  }
}
// 小的文字提示
.textCss {
  font-size: 12px;
  color: #767676;
}
.textNum {
  font-size: 20px;
  font-weight: 700;
  color: #3D3D3D;
}
.textUnit {
  font-size: 12px;
  font-weight: 700;
  color: #3D3D3D;
}
// 图表部分  end

.rankingText {
  padding: 20px 0;
  height: 70px;
  font-size: 20px;
  font-weight: 700;
  .segmenteds {
    float: right;
  }
}
// top排行框
.rankingBox {
  min-height: 700px;
  padding: 10px 0;
  border-radius: 10px;
  background-color: #FFF;
  //border: 1px solid blue;
  .rankTitleText {
    font-size: 16px;
    color: #979797;
  }
  .rankTitleTextBig {
    font-size: 16px;
    font-weight: 700;
    color: #3D3D3D;
  }
  // 排行的大盒子
  .progressBox {
    padding-top: 10px;
    // 循环出来的某一条
    .progressOne{
      height: 64px;
      padding: 10px 20px;
      .progressOneName{
        font-size: 14px;
        font-weight: 700;
        .progressName{
          display: inline-block;
          //width: calc(100% - 150px);
          overflow: hidden;
          text-overflow: ellipsis; /* 使用省略号表示超出部分 */
          white-space: nowrap; /* 防止文字换行 */
        }
      }
      // 进度条 pv uv
      .progressTopNum {
        float: right;
        display: inline-block;
        text-align: right;
        font-size: 12px;
        font-weight: 400;
        color: #979899;
      }
    }
  }
}

// 排行榜某条数据可点击的样式
.hoverCss {
  cursor: pointer; // 小手样式
}
.hoverCss:hover {
  background-color: #f9f9f9;
}
.clickCss {
  border-right: 3px solid #4686ED;
  background-color: #EDF6FF;
}
