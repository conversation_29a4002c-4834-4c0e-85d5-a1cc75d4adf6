.dataCard{
    border-Right: 1px solid #EEEFF1
}
.dataCardRight{
}
.dataCardTitle{
    color: rgba(0, 0, 0, 0.9);
    font-weight: 500;
    font-size: 13px;
    line-height: 20px;
}
.dataCardMetrics{
    color: rgba(0, 0, 0, 0.9);
    font-weight: 600;
    font-size: 20px;
    line-height: 20px;
}
.dataCardMetricsComparison{
    font-weight: normal;
    font-size: 12px;
    line-height: 18px;
}
.cardTitle{
    width: 100%;
    border-bottom: 1px solid #EEEFF1;
    padding: 14px 0;
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
}
.cardTitleTab{
    float: left;
    margin-Right: 10px;
    background-Color: #2E7EFF;
    width: 3px;
    height: 18px;
    border-Radius: 5px;
}

/*  ****************** 图表部分 start ******************  */
.timeInp {
  margin: 20px 0 30px 0;
  font-size: 16px;
  color: #000000;
  .timePicker {
    margin: 0 10px;
  }
}
.titleTop {
  margin: 10px 0;

  .titleText {
    font-size: 16px;
    font-weight: 700;
    color: #3D3D3D;
  }
  .titleTime {
    float: right;
    font-size: 12px;
    color: #767676;
  }
}
// 小的文字提示
.textCss {
  font-size: 12px;
  color: #767676;
}
.textNum {
  font-size: 20px;
  font-weight: 700;
  color: #3D3D3D;
}
.textUnit {
  font-size: 12px;
  font-weight: 700;
  color: #3D3D3D;
}
// 饼图
.pieBox {
  height: 350px;

  .tableBox {
    width: 58%;
    margin-top: 20px;
    margin-right: 2%;
    border-spacing: 0;
    display: inline-block;
  }
  .pieChart {
    position: absolute;
    top: 0;
    display: inline-block;
    //height: 60%;
    width: 40%;
    vertical-align: top;
  }
}


// 进度条
.progressTopBox {
  display: inline-block;
  margin-top: 15px;
  width: 100%;
  height: 30px;
  line-height: 30px;
  font-size: 14px;

  // 进度条序号
  .progressInx {
    display: inline-block;
    padding: 0 4px;
    height: 16px;
    color: #2B75ED;
    line-height: 16px;
    margin: 0 0 10px 0;
    font-weight: 700;
    text-align: center;
    border-radius: 4px;
    background-color: #C8DDFF;
  }
  // 进度条pv,uv
  .progressTopNum {
    float: right;
    display: inline-block;
    text-align: right;
    font-size: 12px;
    color: #979899;
  }
}

.progressBox {
  height: 24px;
  border-radius: 6px;
  background-color: #EFEFEF;

  .progressRate {
    height: 24px;
    line-height: 24px;
    text-align: right;
    color: #FFFFFF;
    font-size: 12px;
    font-weight: 700;
    border-radius: 6px;
    background-color: #5290F0;
  }
}
.ant-progress-outer {
  margin-right: calc(-2em - 16px);
  padding-right: calc(2em + 2px);
}
.ant-progress-inner {
  width: 80%;
}

/*  ****************** 图表部分 end ******************  */
