import React, { useEffect, useState, useRef } from 'react'
import styles from './style.less'
import { Divider, Form, Select, Input, Button, Table, Pagination, Tooltip , Icon, Modal, Popconfirm } from 'antd'
import { connect } from 'dva'
import { authBtn } from '@/utils/utils';
import { getUser } from '@/utils/user';
import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout'
import { EnumApplicationOptions, EnmuClientTypeOptions, EnumBusinessOptions, EnumStatusOptions } from '@/config'

const fieldManagement = ({ 
    dispatch,
    form,
    total = 0,
    listData = [],
    loading,
    ssoAuthorityRole: {
        data: { curResources = [] }
    }
}) => {
    const FormItem = Form.Item;
    const { Option } = Select;
    const [ currentPage, setCurrentPage ] = useState(1);
    const [ pageSize, setPageSize ] = useState(10);
    const [ authList, setAuthList ] = useState([]);
    const [ currentId, setCurrentId ] = useState('');
    const [ onLineVisible, setOnLineVisible ] = useState(false)
    const [ visible, setVisible ] = useState(false)
    const [ action, setAction ] = useState('')
    const { name: userName } = getUser()

    const formLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    };

    const { getFieldDecorator, setFields, resetFields } = form

    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align: 'center',
            render: (_, row, index) => {
                return (index+1) + ((currentPage-1) * pageSize)
            }
        },
        {
            title: '版本号',
            dataIndex: 'value',
            key: 'value',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '创建人',
            dataIndex: 'createUser',
            key: 'createUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            } 
        },
        {
            title: '编辑人',
            dataIndex: 'updateUser',
            key: 'updateUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '创建时间',
            dataIndex: 'gmtCreate',
            key: 'gmtCreate',
            align: 'center',
            render: (_, row) => {
                return moment(_).format('YYYY-MM-DD HH:mm')
            }
        },
        {
            title: '最后编辑时间',
            dataIndex: 'gmtModified',
            key: 'gmtModified',
            align: 'center',
            render: (_, row) => {
                return moment(_).format('YYYY-MM-DD HH:mm')
            }
        },
        {
            title: '状态',
            dataIndex: 'isDeleted',
            key: 'isDeleted',
            align: 'center',
            render: (_, row) => {
                return (
                    <div>
                        {
                            _ !== 0 ? (
                                <span>已作废</span>
                            ) : <span style={{'color': 'chartreuse'}}>有效</span>
                        }
                    </div>
                )
            }
        },
        {
            title: '操作',
            dataIndex: 'option',
            key: 'option',
            align: 'center',
            render: (_, row) => {
                return (
                    <div>
                      {isAuthBtn('update') ? (
                        <Button 
                            type="link"
                            onClick={() => {
                                setCurrentId(row.id)
                                setAction('update')
                                setVisible(true)
                                form.setFields({
                                    value: { value: row.value }
                                })
                            }}
                        >
                            编辑
                        </Button>
                      ) : null}
                      {(isAuthBtn('del') && row.isDeleted === 0) ? (
                        <Popconfirm
                            title="确定作废该版本号么?"
                            onConfirm={() => {
                                setCurrentId(row.id)
                                delResume(row.id, 2)
                            }}
                            onCancel={()=>{}}
                            okText="是"
                            cancelText="否"
                        >
                            <Button 
                                type="link"
                            >
                                作废
                            </Button>
                        </Popconfirm>
                      ) : null}
                      {(isAuthBtn('resume'))  && row.isDeleted === 1 ? (
                        <Button 
                            type="link"
                            onClick={() => {
                                setCurrentId(row.id)
                                delResume(row.id, 4)
                            }}
                        >
                            恢复
                        </Button>
                      ) : null}
                    </div>
                  );
            },
        },
    ]

    const showTotal = Total => `共${Total}条`;

    /**
     * 列表查询
     * @param {*} value 
     */
    const queryData = value => {
        const parmas = {
            pageNum: value.pageNum,
            pageSize: value.pageSize,
        };
        console.log(parmas, 'parmas')
        dispatch({
            type: 'fieldManagement/fetchlistData',
            payload: parmas,
        });
    };

    /**
     * 新建
     * @param {object} values 
     */
    const onAdd = () => {
        form.validateFields(async (err, values) => {
            const parmas = {
                value: !values.value || Array.isArray(values.value)
                    ? undefined
                    : values.value,
                createUser: userName
            };
            dispatch({
                type: 'fieldManagement/add',
                payload: parmas,
                callback: () => {
                    setVisible(false)
                    const query = {
                      pageNum: 1,
                      pageSize: pageSize,
                    };
                    queryData(query);
                }
            });
        });
    }

    /**
     * 编辑
     * @param {object} values 
     */
    const onUpdate = () => {
        form.validateFields(async (err, values) => {
            const parmas = {
                id: currentId,
                value: !values.value || Array.isArray(values.value)
                    ? undefined
                    : values.value,
                operationType: 3,
                updateUser: userName
            };
            dispatch({
                type: 'fieldManagement/update',
                payload: parmas,
                callback: () => {
                    setVisible(false)
                    const query = {
                      pageNum: currentPage,
                      pageSize: pageSize,
                    };
                    queryData(query);
                }
            });
        });
    }

    /**
     * 作废 、恢复
     */
    const delResume = (id, type) => {
        dispatch({
            type: 'fieldManagement/update',
            payload: {
                id: id,
                operationType: type,
                updateUser: userName
            },
            callback: () => {
                const query = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 切换页码
     * @param {*} page 
     * @param {*} pageSize 
     */
    const handlePageChange = (page, pageSize) => {
        setCurrentPage(page);
        queryData({ pageNum: page, pageSize });
    };

    /**
     * 切换 pageSize
     * @param {*} current 
     * @param {*} pageSize 
     */
    const handlePageSizeChange = (current, pageSize) => {
        setPageSize(pageSize)
        queryData({ pageNum: currentPage, pageSize });
    };

    /**
     * 按钮权限确认
     * @param {*} key 
     * @returns 
     */
    const isAuthBtn = key => {
        return authList.includes(key);
    };

    /**
     * 表单验证
     * @param {*} rule 
     * @param {*} value 
     * @param {*} callback 
     */
    const checkCnData = (rule, value, callback) => {
        if (value) {
            if (/[\u4E00-\u9FA5]/g.test(value)) {
              callback(new Error('只能输入字母、数字、符号!'));
            }
        }
        callback();
    };

    useEffect(() => {
        const params = {
          pageNum: 1,
          pageSize: 10,
        };
        queryData(params);
    }, []);

    useEffect(() => {
        if (Array.isArray(curResources)) {
          curResources.map(c => {
            if (c.resourceKey === 'documents') {
              const authBtnList = authBtn(c.subList, 'fieldManagement');
              setAuthList(authBtnList);
            }
          });
        }
    }, [curResources]);

    return (
        <PageHeaderWrapper
            title={false}
            content={
                <div className='wrapper'>
                    <Divider className='divider'></Divider>
                    {
                        isAuthBtn('add') && (<Button 
                            type="primary"
                            onClick={() => {
                                setAction('add')
                                form.resetFields();
                                setVisible(true)
                            }}
                        >新建版本号</Button>)
                    }
                    <Table
                        columns={columns}
                        dataSource={listData}
                        style={{ marginTop: '20px' }}
                        bordered
                        rowKey="id"
                        loading={loading}
                        scroll={{ x: true, scrollToFirstRowOnChange: true }}
                        pagination={false}
                    />
                    <Pagination
                        style={{ float: 'right', marginTop: '20px' }}
                        showQuickJumper
                        showSizeChanger
                        disabled={total === 0}
                        showTotal={showTotal}
                        current={currentPage}
                        onChange={handlePageChange}
                        onShowSizeChange={handlePageSizeChange}
                        defaultPageSize={10}
                        total={total}
                        pageSizeOptions={['10', '20', '50', '100']}
                    />
                    <Modal
                        wrapClassName="event-modal"
                        visible={visible}
                        title={`${action === 'add' ? '新增' : '编辑'}字段项`}
                        width={400}
                        onOk={action === 'add' ? onAdd : onUpdate}
                        onCancel={()=>{ setVisible(false) }}
                    >
                        <Form>
                            <FormItem label="字段类型" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                                {getFieldDecorator('blockName', {
                                    rules: [],
                                    initialValue: [],
                                })(<span>版本号</span>)}
                            </FormItem>
                            <FormItem label="版本号值" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
                                {getFieldDecorator('value', {
                                    rules: [
                                        { required: true, message: '版本号值不能为空' },
                                        { validator: checkCnData, trigger: 'blur' }
                                    ],
                                    initialValue: [],
                                })(<Input placeholder="请输入版本号值" />)}
                            </FormItem>
                        </Form>
                    </Modal>
                </div>
            }
        >
        </PageHeaderWrapper>
    )

}

const fieldManagementForm = Form.create()(fieldManagement);

export default connect(({ fieldManagement, loading, ssoAuthorityRole }) => ({
    listData: fieldManagement.listData,
    total: fieldManagement.total,
    loading: loading.effects['fieldManagement/fetchlistData'],
    ssoAuthorityRole,
}))(fieldManagementForm);