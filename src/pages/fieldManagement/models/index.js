/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-22 14:32:39
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-22 22:09:37
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/fieldManagement/modal/index.js
 */
import { notification, message } from 'antd';
import { getListData, add, update } from '../services';

const UserModel = {
  namespace: 'fieldManagement',
  state: {
    total: 0,
    listData: [],
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      console.log(data, 'data')
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
    *add({ payload, callback }, { call, put }) {
      const response = yield call(add, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
          notification.success({
              message: '成功',
              description: msg,
          });
          callback()
      } else {
          notification.warn({
              message: '失败',
              description: msg,
          });
      }
    },
    *update({ payload, callback }, { call, put }) {
        const response = yield call(update, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: msg,
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    }
  },
  reducers: {
    saveListData(state, action) {
      return { ...state, listData: action.payload }
    },
    saveTotal(state, action) {
      return { ...state, total: action.payload }
    }
  },
};
export default UserModel;