/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-22 14:33:57
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-22 22:08:27
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/fieldManagement/services/index.js
 */
import request from '@/utils/request';

// 获取版本号list
export async function getListData(pamas) {
  return request('/admin/v1/eventlog/burypoint/pbb/version/queryDetail', {
    method: 'POST',
    data: pamas,
  });
}

/**
 * 新建页面
 * @param {*} params 
 * @returns 
 */
 export async function add(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/version/add', {
        method: 'POST',
        data: params,
    });
}

/**
 * 编辑页面
 * @param {*} params 
 * @returns 
 */
export async function update(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/version/update', {
        method: 'POST',
        data: params,
    });
}