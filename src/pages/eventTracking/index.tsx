import React from 'react';
import { Tabs } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { connect } from 'dva';
import EventList from './components/eventList';
import PSB from '@/styles/pagingSutionBottom.less';

window.BlmAnalysis &&
  window.BlmAnalysis.pageView({
    pageId: 'p_leopard_cp_00000018',
    eventId: 'e_leopard_cp_pv_00000076',
  });

const { TabPane } = Tabs;
const EventTracking = ({ platformList, isReview = false }) => (
  <PageHeaderWrapper
    title={false}
    className={`${PSB.pageHeaderWrapperStyle} pageNew`}
    content={
      <Tabs defaultActiveKey="3" className={PSB.tabBox}>
        {platformList?.map(item => (
          <TabPane tab={item.applicationDesc} key={item.applicationType}>
            <EventList applicationType={item.applicationType} isReview={isReview} />
          </TabPane>
        ))}
      </Tabs>
    }
  ></PageHeaderWrapper>
);

export default connect(({ user }) => ({
  platformList: user.platformList,
}))(EventTracking);
