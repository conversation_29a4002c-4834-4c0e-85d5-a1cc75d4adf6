import React, { useEffect, useState } from 'react';
import { Button, Icon, Tag, Dropdown, Menu, Pagination, message } from 'antd';
import { Image, Table } from '@blmcp/ui';
import { EyeOutlined } from '@ant-design/icons';
import { updateStatus } from '../../../services/tracking';

import PSB from '@/styles/pagingSutionBottom.less';

const EventTable = ({
  dataSource,
  isReview,
  businessType,
  applicationType,
  tableTotalNum,
  handleSearch,
  pageData,
  pageSizeData,
  eventTableHeight,
}) => {
  const eventTypeEnum = [
    { key: 'pv', value: '页面浏览事件' },
    { key: 'click', value: '模块点击事件' },
    { key: 'exposure', value: '模块曝光事件' },
  ];
  const statusEnum = [
    { key: 0, value: '待提交' },
    { key: 1, value: '待审核' },
    { key: 2, value: '审核驳回' },
    { key: 3, value: '待开发' },
    { key: 4, value: '待测试' },
    { key: 5, value: '已上线' },
    { key: 6, value: '已取消' },
  ];
  // 更多按钮图标切换
  const [expand, setExpand] = useState(false);
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };
  // 表格可滚动高度
  const [tableHeight, setTableHeight] = useState(300);

  // 状态流转
  const handleStatusChange = (eventId, status) => {
    const searchParams = {
      trackingType: 1,
      businessType,
      applicationType,
      eventId,
      status,
    };
    updateStatus(searchParams).then(res => {
      if (res?.code === 1 && res.data) {
        message.success('状态流转成功');
        handleSearch();
      }
    });
  };
  const handleMenuClick = (e, item) => {
    handleStatusChange(item.eventId, Number(e.key));
    handleSearch();
  };
  const columns = [
    {
      title: '事件ID',
      //   width: 100,
      dataIndex: 'eventId',
      key: 'eventId',
      align: 'center',
    },
    {
      title: '事件名称',
      //   width: 100,
      dataIndex: 'eventName',
      key: 'eventName',
      align: 'center',
    },
    {
      title: '事件唯一标识',
      //   width: 120,
      dataIndex: 'eventUniqueId',
      key: 'eventUniqueId',
      align: 'center',
    },
    {
      title: '事件截图',
      dataIndex: 'picture',
      key: 'picture',
      align: 'center',
      render: (text, record) => (
        <Image
          width={30}
          height={30}
          src={record.picture}
          preview={{ mask: <EyeOutlined rev="horizontal" /> }}
        />
      ),
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      //   width: 100,
      align: 'center',
      render: row => eventTypeEnum.find(item => item.key === row)?.value || '',
    },
    // 一对多
    {
      title: '关联页面',
      //   width: 150,
      dataIndex: 'relatedPage',
      key: 'relatedPage',
      align: 'center',
      render: (row, item) => <div>{item.pageList && item.pageList[0]}</div>,
    },
    {
      title: '创建信息',
      //   width: 100,
      dataIndex: 'createUserName',
      key: 'createUserName',
      align: 'center',
      render: (row, item) => (
        <div>
          <p>{item.createUserName}</p>
          <p>{item.gmtCreate}</p>
        </div>
      ),
    },
    {
      title: '更新信息',
      //   width: 100,
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      align: 'center',
      render: (row, item) => (
        <div>
          <p>{item.updateUserName}</p>
          <p>{item.gmtModified}</p>
        </div>
      ),
    },
    {
      title: '状态',
      width: 100,
      fixed: 'right',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      //   render: row => statusEnum.find(item => item.key === row)?.value || '',
      render: (row, item) => {
        let color = '#FFF3EB';
        let fontColor = '#FF9C2A';
        if (row === 2) {
          color = '#FFEEF0';
          fontColor = '#FF5964';
        } else if (row === 3 || row === 4) {
          color = '#EFF3FF';
          fontColor = '#276BEC';
        } else if (row === 5) {
          color = '#EDFFEC';
          fontColor = '#44AB42';
        }
        const menu = (
          //   <Menu onClick={handleMenuClick}>
          <Menu onClick={e => handleMenuClick(e, item)}>
            <Menu.Item key="3">待开发</Menu.Item>
            <Menu.Item key="4">待测试</Menu.Item>
            <Menu.Item key="5">已上线</Menu.Item>
          </Menu>
        );
        return (
          <>
            {[0, 1, 2, 6].includes(row) && (
              <Tag color={color} key={row} style={{ color: fontColor, padding: '6px 8px' }}>
                {statusEnum.find(e => e.key === row)?.value || ''}
              </Tag>
            )}
            {[3, 4, 5].includes(row) && (
              <Dropdown overlay={menu} trigger={['click']}>
                <Tag color={color} key={row} style={{ color: fontColor, padding: '6px 8px' }}>
                  {statusEnum.find(e => e.key === row)?.value || ''}
                  <Icon type="down" />
                </Tag>
              </Dropdown>
            )}
          </>
        );
      },
    },
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 180,
      align: 'center',
      render: row => (
        <section>
          {!isReview &&
            // 待审核
            (row.status === 1 ? (
              <>
                <Button
                  type="link"
                  target="_brank"
                  href={`/buryingPointNew/eventDetail?businessType=${businessType}&applicationType=${applicationType}&pageStatus=detail&eventId=${row.eventId}`}
                >
                  查看
                </Button>
                <Button
                  type="link"
                  target="_brank"
                  onClick={() => handleStatusChange(row.eventId, 6)}
                >
                  撤回
                </Button>
              </>
            ) : (
              <>
                {/* 待提交状态为提交和编辑 */}
                {row.status === 0 ? (
                  <Button
                    type="link"
                    target="_brank"
                    onClick={() => handleStatusChange(row.eventId, 1)}
                  >
                    提交
                  </Button>
                ) : (
                  <Button
                    type="link"
                    target="_brank"
                    href={`/buryingPointNew/eventDetail?businessType=${businessType}&applicationType=${applicationType}&pageStatus=detail&eventId=${row.eventId}`}
                  >
                    查看
                  </Button>
                )}
                <Button
                  type="link"
                  target="_brank"
                  href={`/buryingPointNew/eventDetail?businessType=${businessType}&applicationType=${applicationType}&pageStatus=edit&eventId=${row.eventId}`}
                >
                  编辑
                </Button>
              </>
            ))}
          {isReview && (
            <Button
              type="link"
              target="_brank"
              href={`/buryingPointNew/eventDetail?businessType=${businessType}&applicationType=${applicationType}&pageStatus=review&eventId=${row.eventId}`}
            >
              审核
            </Button>
          )}
        </section>
      ),
    },
  ];
  const handleChangePagination = (page, pageSize) => {
    handleSearch(page, pageSize);
  };
  const handleOnShowSizeChange = (current, size) => {
    handleSearch(1, size);
  };

  useEffect(() => {
    const contentPagination = document.querySelector('.eventTracking_content_pagination');
    // 表格头部高度
    const containerTableThead = document.querySelector('.ant-table-thead');
    if (contentPagination && containerTableThead) {
      // 表格可滚动高度 = 整体高度 - 分页器高度 - table表格的头部高度
      const height =
        eventTableHeight -
        contentPagination.getBoundingClientRect().height -
        containerTableThead.getBoundingClientRect().height;
      setTableHeight(height);
    }
  }, [eventTableHeight]);

  return (
    <div className={PSB.content2} style={{ height: eventTableHeight }}>
      <div className={`${PSB.content_table} pageTracking-content_table`}>
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          scroll={{ x: '120%', y: tableHeight }}
        />
      </div>
      <div className={`${PSB.content_pagination} eventTracking_content_pagination`}>
        <Pagination
          showSizeChanger
          showQuickJumper
          defaultCurrent={1}
          current={pageData}
          pageSize={pageSizeData}
          total={tableTotalNum}
          showTotal={total => `共 ${total} 条`}
          onChange={handleChangePagination}
          onShowSizeChange={(current, size) => handleOnShowSizeChange(current, size)}
          style={{ float: 'right', padding: '16px 0' }}
        />
      </div>
    </div>
  );
};

export default EventTable;
