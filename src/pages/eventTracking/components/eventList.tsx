import React, { useCallback, useEffect, useState } from 'react';
import { Button, Form, Input, Select } from '@blmcp/ui';
import { DownOutlined } from '@ant-design/icons';
import EventTable from './eventTable';
import {
  queryEventList,
  queryDetailsPage,
  queryUserInfo,
  queryNoApproveEventList,
} from '../../../services/tracking';
import styles from '@/styles/pagingSutionBottom.less';

const { Option } = Select;

const EventList = ({ applicationType, isReview }) => {
  const [form] = Form.useForm();
  // const { getFieldDecorator } = form;
  const businessType = JSON.parse(sessionStorage.getItem('businessType'));
  // 列表表格数据
  const [tableDataSourace, setTableDataSourace] = useState([]);
  // 关联页面下拉框数据
  const [eventPage, setEventPage] = useState({ data: [], fetching: true });
  // 创建人下拉框数据
  const [createUserId, setCreateUserId] = useState({ data: [], fetching: true });
  // 更新人下拉框数据
  const [updateUserId, setUpdateUserId] = useState({ data: [], fetching: true });
  const [tableTotalNum, setTableTotalNum] = useState([]);
  const [pageData, setPageData] = useState(1);
  const [pageSizeData, setPageSizeData] = useState(10);
  // 更多按钮图标切换
  const [expand, setExpand] = useState(false);
  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };
  const eventTypeEnum = [
    { key: 'pv', value: '页面浏览事件' },
    { key: 'click', value: '模块点击事件' },
    { key: 'exposure', value: '模块曝光事件' },
  ];
  const statusEnum = [
    { key: 0, value: '待提交' },
    { key: 1, value: '待审核' },
    { key: 2, value: '审核驳回' },
    { key: 3, value: '待开发' },
    { key: 4, value: '待测试' },
    { key: 5, value: '已上线' },
    { key: 6, value: '已取消' },
  ];
  const [eventTableHeight, setEventTableHeight] = useState(200);

  // 搜索函数
  const handleSearch = (pageNum = 1, pageSize = 10) => {
    form.validateFields().then(formValue => {
      setPageData(pageNum);
      setPageSizeData(pageSize);
      let relatePagesValue = '';
      if (!isReview) {
        relatePagesValue = form.getFieldValue('relatePages');
      }
      const eventIdValue = form.getFieldValue('eventId');
      const queryParams = {
        ...form.getFieldsValue(),
        businessType,
        applicationType,
        trackingType: 1,
        pageNum,
        pageSize,
        relatePages: relatePagesValue ? [relatePagesValue] : [],
        eventId: eventIdValue?.length ? Number(eventIdValue) : '',
      };
      const searchApi = isReview ? queryNoApproveEventList : queryEventList;
      searchApi(queryParams).then(res => {
        if (res.code === 1 && res.data) {
          setTableDataSourace(res.data?.items);
          // 存储总条数
          setTableTotalNum(res.data.totalNum);
        }
      });
    });
  };
  // 关联页面搜索函数
  const handleChangeEventPageId = (value = '') => {
    // pageName
    const searchParams = {
      businessType,
      applicationType,
      trackingType: 1,
      pageNum: 1,
      pageSize: 50,
      pageName: value,
    };
    queryDetailsPage(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        setEventPage({ data: res.data?.items, fetching: !!res.data?.items.length });
      }
    });
  };
  // 创建人更新人搜索
  const handleChangeUser = (value = '', isCreate = true) => {
    // pageName
    const searchParams = {
      keyWord: value,
    };
    queryUserInfo(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        if (isCreate) {
          setCreateUserId({ data: res.data, fetching: !res.data.length });
        } else {
          setUpdateUserId({ data: res.data, fetching: !res.data.length });
        }
      }
    });
  };
  // 重置表单函数
  const handleResert = () => {
    form.resetFields();
    // 重置完表单重新搜索列表
    handleSearch();
  };

  // 动态计算table表格与分页的高度
  const getEventTableHeight = () => {
    const content = document.querySelector('.eventTracking-content');
    const contentForm = document.querySelector(`.eventTracking-content_form${applicationType}`);
    if (content && contentForm) {
      const height =
        content.getBoundingClientRect().height - contentForm.getBoundingClientRect().height;
      setEventTableHeight(height);
    }
  };
  // 页面初次渲染调用搜索函数
  useEffect(() => {
    handleSearch();

    // 在加载和窗口大小改变时重新计算表格高度
    getEventTableHeight();
    // form表单的高度变化时重新计算表格高度  获取目标元素
    const containerForm = document.querySelector(`.eventTracking-content_form${applicationType}`);
    // 创建 MutationObserver 对象
    const observer = new MutationObserver(mutationsList => {
      mutationsList.forEach(item => {
        if (item.type === 'attributes' && item.attributeName === 'style') {
          // 当元素的 style 属性发生变化时会被触发
          getEventTableHeight();
        }
      });
    });
    // 配置观察选项
    const config = { attributes: true, attributeFilter: ['style'], subtree: true };
    // 开始监听
    observer.observe(containerForm, config);
    window.addEventListener('resize', getEventTableHeight);
    return () => {
      window.removeEventListener('resize', getEventTableHeight);
      observer.disconnect();
    };
  }, []);
  const validatorNumber = (rule, value, callback) => {
    const regexPattern = /^[0-9]+$/;
    if (value && !regexPattern.test(value)) {
      callback('仅允许输入数字');
    } else {
      callback();
    }
  };
  return (
    <div className="eventTracking-content" style={{ height: 'calc(100vh - 195px)' }}>
      <div className={`${styles.content_form} eventTracking-content_form${applicationType}`}>
        <Form form={form} layout="inline" labelAlign="right" {...formItemLayout}>
          <Form.Item
            label="事件ID"
            name="eventId"
            className={styles.formItemCss}
            rules={[{ required: false, validator: validatorNumber }]}
          >
            <Input placeholder="请输入事件ID" allowClear/>
          </Form.Item>
          <Form.Item
            label="事件名称"
            name="eventName"
            className={styles.formItemCss}
            rules={[{ required: false }]}
          >
            <Input placeholder="请输入事件名称" allowClear/>
          </Form.Item>
          <Form.Item
            label="事件标识"
            name="eventUniqueId"
            className={styles.formItemCss}
            rules={[{ required: false }]}
          >
            <Input placeholder="请输入事件标识" allowClear/>
          </Form.Item>
          <Form.Item
            label="事件类型"
            name="eventType"
            className={expand ? styles.formItemExpand : styles.formItemUnExpand}
            rules={[{ required: false }]}
          >
            <Select
              placeholder="请选择事件类型"
              allowClear
            >
              {eventTypeEnum.map(item => (
                <Option value={item.key} key={item.key}>
                  {item.value}
                </Option>
              ))}
            </Select>
          </Form.Item>
          {!isReview && (
            <Form.Item
              label="关联页面"
              name="relatePages"
              className={expand ? styles.formItemExpand : styles.formItemUnExpand}
              rules={[{ required: false }]}
            >
              <Select
                showSearch
                placeholder="请选择页面"
                // onChange={handleChangeEventPageId}
                onFocus={handleChangeEventPageId}
                onSearch={value => handleChangeEventPageId(value)}
                filterOption={false}
                allowClear
                popupClassName="emptySelectContentText"
                notFoundContent="请输入关键字选择"
              >
                {eventPage?.data.map(item => (
                  <Option value={item.id} key={item.id}>
                    {item.pageName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          {!isReview && (
            <Form.Item
              label="状态"
              name="status"
              className={expand ? styles.formItemExpand : styles.formItemUnExpand}
              rules={[{ required: false }]}
            >
              <Select placeholder="请选择状态" allowClear>
                {statusEnum.map(item => (
                  <Option value={item.key} key={item.key}>
                    {item.value}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}
          <Form.Item
            label="创建人"
            name="createUserId"
            className={expand ? styles.formItemExpand : styles.formItemUnExpand}
            rules={[{ required: false }]}
          >
            <Select
              showSearch
              placeholder="请输入创建人姓名"
              onSearch={value => handleChangeUser(value, true)}
              filterOption={false}
              allowClear
              popupClassName="emptySelectContentText"
              notFoundContent="请输入关键字选择"
            >
              {createUserId?.data.map(item => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="更新人"
            name="updateUserId"
            className={expand ? styles.formItemExpand : styles.formItemUnExpand}
            rules={[{ required: false }]}
          >
            <Select
              showSearch
              placeholder="请输入更新人姓名"
              onSearch={value => handleChangeUser(value, false)}
              filterOption={false}
              allowClear
              popupClassName="emptySelectContentText"
              notFoundContent="请输入关键字选择"
            >
              {updateUserId?.data.map(item => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <div style={{ textAlign: 'right' }}>
            <a
              style={{ fontSize: 12 }}
              onClick={() => {
                setExpand(!expand);
              }}
            >
              <DownOutlined rev="horizontal" rotate={expand ? 180 : 0} /> {expand ? '收起' : '更多'}
            </a>
            <Button
              blm-click='{"eventId": "e_leopard_cp_click_00000082", "pageId": "p_leopard_cp_00000018"}'
              style={{ marginRight: 16, marginTop: 5 }}
              onClick={handleResert}
            >
              重置
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              style={{ marginRight: 16, marginTop: 5 }}
              blm-click='{"eventId": "e_leopard_cp_exposure_00000048", "pageId": "p_leopard_cp_00000008"}'
              onClick={() => handleSearch()}
            >
              搜索
            </Button>
            {!isReview && (
              <Button
                type="primary"
                target="_brank"
                style={{ marginTop: 5, color: '#fff', backgroundColor: '#1890ff' }}
                blm-exposure='{"eventId": "e_leopard_cp_click_00000080", "pageId": "p_leopard_cp_00000018"}'
                href={`/buryingPointNew/eventDetail?businessType=${businessType}&applicationType=${applicationType}&pageStatus=add`}
              >
                新增事件
              </Button>
            )}
          </div>
        </Form>
      </div>
      <div className="eventTracking-content_eventTable">
        <EventTable
          dataSource={tableDataSourace}
          isReview={isReview}
          businessType={businessType}
          applicationType={applicationType}
          tableTotalNum={tableTotalNum}
          handleSearch={handleSearch}
          pageData={pageData}
          pageSizeData={pageSizeData}
          eventTableHeight={eventTableHeight}
        />
      </div>
    </div>
  );
};
// const EventListForm = Form.create()();

export default EventList;
