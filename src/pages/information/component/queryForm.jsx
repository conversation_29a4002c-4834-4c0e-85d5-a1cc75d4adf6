/* eslint-disable no-empty-pattern */
/* eslint-disable no-use-before-define */
/* eslint-disable @typescript-eslint/no-use-before-define */
import {
  Row,
  Col,
  Button,
  Form,
  Select,
  DatePicker,
  Checkbox,
  Tag,
  TreeSelect,
  Modal,
  Input,
} from 'antd';
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { connect } from 'dva';
import { getEnumParamData, getTennantAdcode, getUserTennant } from '../services';

const QueryForm = ({ form, dispatch, type, getAllData, clickQueryData, loading }) => {
  const FormItem = Form.Item;
  const { RangePicker } = DatePicker;
  const { CheckableTag } = Tag;
  const { Option } = Select;
  const { SHOW_PARENT } = TreeSelect;
  const tagsFromServer = [
    { label: '昨天', key: '-1' },
    { label: '近3天', key: '3' },
    { label: '近7天', key: '7' },
    { label: '近14天', key: '14' },
    { label: '近1月', key: '30' },
    { label: '近3月', key: '90' },
    { label: '近半年', key: '180' },
  ];
  const [selectedTags, setSelectedTags] = useState([]);
  const [saveVisible, setSaveVisible] = useState(false);
  const [checked, setChecked] = useState(false);
  const [checkedData, setCheckedData] = useState(null);
  const [checkedPF, setCheckedPF] = useState(false);
  const [checkedPFData, setCheckedPFData] = useState(null);
  const [clientVersionData, setClientVersionData] = useState([]);
  const [adcodeData, setAdcodeData] = useState([]);
  const [pfData, setPFData] = useState([
    { label: 'IOS', value: '1' },
    { label: 'ANDROID', value: '2' },
  ]);
  const [cpData, setCpData] = useState([]);
  const [pageEngNameData, setPageEngNameData] = useState([]);
  const [btnEngNameData, setBtnEngNameData] = useState([]);
  const [btnDisable, setBtnDisable] = useState(false);
  const { getFieldDecorator, setFieldsValue, getFieldValue } = form;

  useEffect(() => {
    if (checkedData) {
      setFieldsValue({ dimensionCombination: checkedData });
    }
  }, [checkedData]);

  useEffect(() => {
    if (checkedPFData) {
      setFieldsValue({ pfCode: checkedPFData });
    }
  }, [checkedPFData]);

  const formLayout = {
    labelCol: { span: 4, push: 2 },
    wrapperCol: { span: 17, push: 2 },
  };

  const newPfData = [
    { label: 'IOS', enumValue: '1' },
    { label: 'ANDROID', enumValue: '2' },
  ];

  const saveQueryInfo = query => {
    const params = {
      ...query,
    };
    dispatch({
      type: 'information/saveQuery',
      payload: params,
      callback: () => {
        setSaveVisible(false);
      },
    });
  };

  const parmasFormate = (value, pre, allData, preAllData) => {
    if (value.includes('all')) {
      return allData.map(c => c.enumValue);
    }
    if (pre) {
      let prent = pre;
      if (pre.includes('all')) {
        prent = preAllData.map(p => p.enumValue);
      }
      const flag = prent.filter(f => value.includes(f));

      if (flag.length !== 0) {
        const newData = value.filter(v => !flag.includes(v));
        const flagData = [];
        flag.map(f => {
          const data = allData.filter(a => a.parentId === f);
          const newParams = data.map(d => d.enumValue);
          flagData.push(...newParams);
        });
        return [...newData, ...flagData];
      }
      return value;
    }

    return value;
  };

  useEffect(() => {
    if (clickQueryData) {
      const tennantList = clickQueryData.tenantId.length !== 0 ? clickQueryData.tenantId : ['all'];
      const params = {
        dimensionCombination: clickQueryData.dimensionCombination,
        btnEngName: clickQueryData.btnEngName,
        pageEngName: clickQueryData.pageEngName,
        tenantId: clickQueryData.tenantId,
        adcode: clickQueryData.adcode,
        clientVersion: clickQueryData.clientVersion,
        pfCode: clickQueryData.pfCode,
        terminalCode: clickQueryData.terminalCode,
        statisticType: 'pv',
        dataDate: clickQueryData.dataDate,
      };
      const startTime = moment(clickQueryData.dataDate.startTime);
      const endTime = moment(clickQueryData.dataDate.endTime);
      const timeValue = endTime.diff(startTime, 'day');
      getAdcode(tennantList);
      setSelectedTags([String(timeValue)]);
      if (clickQueryData.dimensionCombination.length === 6) {
        setChecked(true);
      }
      if (clickQueryData.pfCode.length === 0) {
        setCheckedPF(true);
        getBtnEngName(['all']);
      } else {
        getBtnEngName(clickQueryData.pageEngName);
      }
      if (clickQueryData.btnEngName.length === 0 && clickQueryData.pageEngName.length === 0) {
        setBtnDisable(true);
      }
      setFieldsValue({
        dimensionCombination:
          clickQueryData.dimensionCombination.length === 6
            ? ['all', ...clickQueryData.dimensionCombination]
            : clickQueryData.dimensionCombination,
        btnEngName: clickQueryData.btnEngName,
        pageEngName: clickQueryData.pageEngName.length === 0 ? ['all'] : clickQueryData.pageEngName,
        tenantId:
          clickQueryData.tenantId.length !== 0
            ? clickQueryData.tenantId.map(d => String(d))
            : ['all'],
        clientVersion:
          clickQueryData.clientVersion.length === 0 ? ['all'] : clickQueryData.clientVersion,
        pfCode:
          clickQueryData.pfCode.length !== 0
            ? clickQueryData.pfCode.map(d => String(d))
            : ['all', ...pfData.map(d => String(d.value))],
        dataDate: [startTime, endTime],
      });
      getAllData(params);
      saveAdcodeFormate(tennantList, clickQueryData.adcode);
    }
  }, [clickQueryData]);

  // const getSomeName = (value, allData) => {
  //   if (value.includes('all')) {
  //     const nameData = allData.map(d => d.enumValueDesc);
  //     return nameData;
  //   }
  //   const nameData = [];
  //   value.map(i => {
  //     const newData = allData.map(a => {
  //       if (i === a.enumValue) {
  //         return a.enumValueDesc;
  //       }
  //     });
  //     nameData.push(...newData);
  //   });
  //   return nameData;
  // };

  const queryData = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const startTime = moment(values.dataDate[0]).format('YYYY-MM-DD');
      const endTime = moment(values.dataDate[1]).format('YYYY-MM-DD');
      const version = values.clientVersion.includes('all')
        ? []
        : parmasFormate(values.clientVersion, values.pfCode, clientVersionData, newPfData);
      const pageEng = values.pageEngName.includes('all')
        ? []
        : parmasFormate(values.pageEngName, undefined, pageEngNameData, []);
      const btnEng = parmasFormate(
        values.btnEngName,
        values.pageEngName,
        btnEngNameData,
        pageEngNameData,
      );
      const params = {
        terminalCode: type,
        statisticType: 'pv',
        dataDate: { startTime, endTime },
        pfCode: values.pfCode.includes('all') ? [] : values.pfCode.map(d => Number(d)),
        clientVersion: version,
        adcode: values.adcode.includes('all')
          ? adcodeData.map(d => Number(d.enumValue))
          : values.adcode.map(d => Number(d)),
        tenantId: values.tenantId.includes('all') ? [] : values.tenantId.map(d => Number(d)),
        pageEngName: pageEng,
        btnEngName: btnEng,
        dimensionCombination: values.dimensionCombination.includes('all')
          ? values.dimensionCombination.slice(1)
          : values.dimensionCombination,
      };
      getAllData(params);
    });
  };

  const getPFCode = async () => {
    const { code, data } = await getEnumParamData({ enumCategory: 'pf', parentId: ['0'] });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.enumValueDesc, value: d.enumValue }));
      setPFData(newData);
      const param = newData.map(d => d.value);
      getClientVersion(param);
    }
  };

  const getClientVersion = async value => {
    const { code, data } = await getEnumParamData({ parentId: value });
    if (code === 1 && data) {
      setClientVersionData(data);
    }
  };

  const saveAdcodeFormate = async (value, nowValue) => {
    // if (type === 2) {
    //   const { code, data } = await getTennantAdcode({ enumCategory: 'city', parentId: ['0'] });
    //   if (code === 1 && data) {
    //     if (data.length === nowValue.length) {
    //       setFieldsValue({ adcode: ['all'] });
    //     } else {
    //       setFieldsValue({ adcode: nowValue.map(d => String(d)) });
    //     }
    //   }
    // } else {
    const newValue = value.includes('all')
      ? cpData.map(p => Number(p.enumValue))
      : value.map(v => Number(v));
    const { code, data } = await getTennantAdcode({ tenantList: newValue });
    if (code === 1 && data) {
      if (data.length === nowValue.length) {
        setFieldsValue({ adcode: ['all'] });
      } else {
        setFieldsValue({ adcode: nowValue.map(d => String(d)) });
      }
    }
    // }
  };

  const getAdcode = async value => {
    // if (type === 2) {
    //   const { code, data } = await getTennantAdcode({ enumCategory: 'city', parentId: ['0'] });
    //   if (code === 1 && data) {
    //     setAdcodeData(data);
    //   }
    // } else {
    const newValue = value.includes('all')
      ? cpData.map(p => Number(p.enumValue))
      : value.map(v => Number(v));
    const { code, data } = await getTennantAdcode({ tenantList: newValue });
    if (code === 1 && data) {
      const newData = data.map(d => ({ enumValueDesc: d.city, enumValue: d.adCode }));
      setAdcodeData(newData);
    }
    // }
  };

  const getCp = async () => {
    const { code, data } = await getUserTennant({});
    if (code === 1 && data) {
      const newData = data.map(d => ({ enumValueDesc: d.briefName, enumValue: String(d.id) }));
      setCpData(newData);
    }
  };

  const getPageEngName = async () => {
    const { code, data } = await getEnumParamData({ enumCategory: 'page', parentId: ['0'] });
    if (code === 1 && data) {
      setPageEngNameData(data);
      const newData = data.map(d => d.enumValue);
      getBtnEngName(newData);
    }
  };

  useEffect(() => {
    // if (type === 2) {
    //   getAdcode();
    // }
    const param = pfData.map(d => d.value);
    getClientVersion(param);
    getCp();
    getPageEngName();
  }, []);

  // useEffect(() => {
  //   if (pageEngNameData.length !== 0) {
  //     queryData();
  //   }
  // }, [pageEngNameData]);

  const treeFormate = (pre, data) => {
    let newData = pre;
    if (pre.includes('all')) {
      newData = pageEngNameData.map(i => i.enumValue);
    }
    if (pre.length === 0) return null;
    return newData.map(t => {
      const typeData = data.filter(d => d.parentId === t);
      const preData = pageEngNameData.filter(i => i.enumValue === t)[0];
      return (
        <TreeNode value={preData.enumValue} title={preData.enumValueDesc} key={preData.enumValue}>
          {typeData.map(item => (
            <TreeNode value={item.enumValue} title={item.enumValueDesc} key={item.enumValue} />
          ))}
        </TreeNode>
      );
    });
  };

  const getBtnEngName = async value => {
    const newValue = value.includes('all') ? pageEngNameData.map(p => p.enumValue) : value;
    const { code, data } = await getEnumParamData({ parentId: newValue });
    if (code === 1 && data) {
      setBtnEngNameData(data);
    }
  };

  const closeSaveVisible = () => {
    setSaveVisible(false);
  };

  const saveQueryData = () => {
    setSaveVisible(true);
  };

  const defaultDate = [moment().subtract(7, 'day'), moment().subtract(0, 'day')];

  const handleChange = (tag, c) => {
    if (!c) return false;
    setSelectedTags([tag]);
    switch (tag) {
      case '-1':
        setFieldsValue({ dataDate: [moment().subtract(1, 'day'), moment().subtract(1, 'day')] });
        break;
      case '3':
        setFieldsValue({ dataDate: [moment().subtract(3, 'day'), moment().subtract(0, 'day')] });
        break;
      case '7':
        setFieldsValue({ dataDate: [moment().subtract(7, 'day'), moment().subtract(0, 'day')] });
        break;
      case '14':
        setFieldsValue({ dataDate: [moment().subtract(14, 'day'), moment().subtract(0, 'day')] });
        break;
      case '30':
        setFieldsValue({ dataDate: [moment().subtract(1, 'month'), moment().subtract(0, 'day')] });
        break;
      case '90':
        setFieldsValue({ dataDate: [moment().subtract(3, 'month'), moment().subtract(0, 'day')] });
        break;
      case '180':
        setFieldsValue({ dataDate: [moment().subtract(6, 'month'), moment().subtract(0, 'day')] });
        break;
      case '365':
        setFieldsValue({ data: [moment().subtract(1, 'year'), moment().subtract(0, 'day')] });
        break;
      default:
        return false;
    }
  };

  const saveAllQuery = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const startTime = moment(values.dataDate[0]).format('YYYY-MM-DD');
      const endTime = moment(values.dataDate[1]).format('YYYY-MM-DD');
      const version = values.clientVersion.includes('all')
        ? []
        : parmasFormate(values.clientVersion, values.pfCode, clientVersionData, newPfData);
      const pageEng = values.pageEngName.includes('all')
        ? []
        : parmasFormate(values.pageEngName, undefined, pageEngNameData, []);
      const btnEng = parmasFormate(
        values.btnEngName,
        values.pageEngName,
        btnEngNameData,
        pageEngNameData,
      );
      const params = {
        queryName: values.name,
        terminalCode: type,
        dataDate: { startTime, endTime },
        pfCode: values.pfCode.includes('all') ? [] : values.pfCode.map(d => Number(d)),
        clientVersion: version,
        adcode: values.adcode.includes('all')
          ? adcodeData.map(d => Number(d.enumValue))
          : values.adcode.map(d => Number(d)),
        tenantId: values.tenantId.includes('all') ? [] : values.tenantId.map(d => Number(d)),
        pageEngName: pageEng,
        btnEngName: btnEng,
        dimensionCombination: values.dimensionCombination.includes('all')
          ? values.dimensionCombination.slice(1)
          : values.dimensionCombination,
      };
      saveQueryInfo(params);
    });
  };

  const checkOptions = [{ label: '全部', value: 'all' }, ...pfData];

  const getDisabled = value => {
    if (value === 'all') {
      const pf = getFieldValue('pfCode');
      const cv = getFieldValue('clientVersion');
      const ac = getFieldValue('adcode');
      const ti = getFieldValue('tenantId');
      const pen = getFieldValue('pageEngName');
      const ben = getFieldValue('btnEngName');
      if (!pf || !cv || !ac || !ti || !pen || !ben) {
        return false;
      }
      return (
        pf.includes('all') ||
        cv.includes('all') ||
        ac.includes('all') ||
        ti.includes('all') ||
        pen.includes('all') ||
        ben.includes('all')
      );
    }
    const newData = getFieldValue(value);
    if (!newData) return false;
    return newData.includes('all');
  };

  const checkOptionsAll = [
    { label: '全部', value: 'all', disabled: getDisabled('all') },
    { label: '平台', value: 'pfCode', disabled: getDisabled('pfCode') },
    { label: '版本', value: 'clientVersion', disabled: getDisabled('clientVersion') },
    { label: '城市', value: 'adcode', disabled: getDisabled('adcode') },
    { label: 'cp', value: 'tenantId', disabled: getDisabled('tenantId') },
    { label: '页面', value: 'pageEngName', disabled: getDisabled('pageEngName') },
    { label: '按钮', value: 'btnEngName', disabled: btnDisable },
  ];

  const passengerOptions = [
    { label: '全部', value: 'all', disabled: getDisabled('all') },
    { label: '平台', value: 'pfCode', disabled: getDisabled('pfCode') },
    { label: '版本', value: 'clientVersion', disabled: getDisabled('clientVersion') },
    { label: '城市', value: 'adcode', disabled: getDisabled('adcode') },
    { label: '页面', value: 'pageEngName', disabled: getDisabled('pageEngName') },
    { label: '按钮', value: 'btnEngName', disabled: btnDisable },
  ];

  const checkAllData = [
    'all',
    'pfCode',
    'clientVersion',
    'adcode',
    'tenantId',
    'pageEngName',
    'btnEngName',
  ];
  // type === 2
  //   ? ['all', 'pfCode', 'clientVersion', 'adcode', 'pageEngName', 'btnEngName']
  //   : ['all', 'pfCode', 'clientVersion', 'adcode', 'tenantId', 'pageEngName', 'btnEngName'];
  const checkPFAllData = ['all', ...pfData.map(i => i.value)];
  const checkFormate = value => {
    if (checked && value.includes('all')) {
      const newValue = value.slice(1);
      setChecked(false);
      setCheckedData(newValue);
    }
    if (!checked && value.includes('all')) {
      setChecked(true);
      setCheckedData(checkAllData);
    }
    if (checked && !value.includes('all')) {
      setChecked(false);
      setCheckedData([]);
    }
    if (!checked && !value.includes('all') && value.length === 6) {
      setChecked(true);
      setCheckedData(checkAllData);
    }
  };

  const dCombinationCheck = ty => {
    const newData = getFieldValue('dimensionCombination');
    if (newData.length === 5 && !newData.includes(ty)) {
      setChecked(true);
      setCheckedData(['all', ...newData, ty]);
      return;
    }
    if (ty === 'pageEngName' && newData.length === 4 && !newData.includes(ty)) {
      setChecked(true);
      setCheckedData(['all', ...newData, ty, 'btnEngName']);
      return;
    }
    if (ty === 'pageEngName' && !newData.includes(ty)) {
      setChecked(false);
      setCheckedData([...newData, ty, 'btnEngName']);
      return;
    }
    if (!newData.includes(ty)) {
      setCheckedData([...newData, ty]);
    }
  };

  const checkFormatePF = value => {
    setFieldsValue({
      clientVersion: [],
    });
    if (checkedPF && value.includes('all')) {
      const newValue = value.slice(1);
      setCheckedPF(false);
      setCheckedPFData(newValue);
      getClientVersion(newValue);
    }
    if (!checkedPF && value.includes('all')) {
      setCheckedPF(true);
      setCheckedPFData(checkPFAllData);
      dCombinationCheck('pfCode');
      getClientVersion(checkPFAllData.slice(1));
    }
    if (checkedPF && !value.includes('all')) {
      setCheckedPF(false);
      setCheckedPFData([]);
    }
    if (!checkedPF && !value.includes('all') && value.length === checkPFAllData.length - 1) {
      setCheckedPF(true);
      setCheckedPFData(checkPFAllData);
      dCombinationCheck('pfCode');
      getClientVersion(checkPFAllData.slice(1));
    }
  };

  const selectDataFormate = (value, ty) => {
    const allData = getFieldValue(ty);
    if (value === 'all') {
      const oj = {};
      oj[ty] = ['all'];
      setFieldsValue(oj);
      dCombinationCheck(ty);
    }
    if (allData[0] === 'all' && allData[0] !== value) {
      const oj = {};
      oj[ty] = [value];
      setFieldsValue(oj);
    }
  };

  const { TreeNode } = TreeSelect;

  const treeNodeFormate = (pre, data) => {
    if (pre.includes('all')) {
      const newData = pre.slice(1);
      return newData.map(t => {
        const typeData = data.filter(d => d.parentId === t);
        const preData = pfData.filter(i => i.value === t)[0];
        return (
          <TreeNode value={preData.value} title={preData.label} key={preData.value}>
            {typeData.map(item => (
              <TreeNode value={item.enumValue} title={item.enumValueDesc} key={item.enumValue} />
            ))}
          </TreeNode>
        );
      });
    }
    const newData = pre;
    return newData.map(t => {
      const typeData = data.filter(d => d.parentId === t);
      const preData = pfData.filter(i => i.value === t)[0];
      return (
        <TreeNode value={preData.value} title={preData.label} key={preData.value}>
          {typeData.map(item => (
            <TreeNode value={item.enumValue} title={item.enumValueDesc} key={item.enumValue} />
          ))}
        </TreeNode>
      );
    });
  };

  const renderContent = () => (
    <>
      <Row>
        <Col span={8} style={{ zIndex: 2 }}>
          <FormItem wrapperCol={{ span: 22 }} style={{ marginBottom: '10px' }}>
            {getFieldDecorator('dataDate', {
              rules: [{ required: true, message: '请选择时间' }],
              initialValue: null,
            })(<RangePicker onChange={() => setSelectedTags([])} />)}
          </FormItem>
        </Col>
        <Col>
          {tagsFromServer.map(tag => (
            <CheckableTag
              style={{ marginTop: '5px' }}
              key={tag.key}
              checked={selectedTags.indexOf(tag.key) > -1}
              onChange={c => handleChange(tag.key, c)}
            >
              {tag.label}
            </CheckableTag>
          ))}
        </Col>
      </Row>
      <Row>
        <Col span={7} style={{ zIndex: 2 }}>
          <FormItem label="平台" style={{ marginBottom: '10px' }}>
            {getFieldDecorator('pfCode', {
              rules: [{ required: true, message: '请选择平台' }],
              initialValue: [],
            })(
              <Checkbox.Group
                options={checkOptions}
                onChange={checkFormatePF}
                style={{ width: '600px' }}
              />,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={7} style={{ zIndex: 2 }}>
          <FormItem label="版本" style={{ marginBottom: '10px' }}>
            {getFieldDecorator('clientVersion', {
              rules: [{ required: true, message: '请选择版本' }],
              initialValue: [],
            })(
              <TreeSelect
                style={{
                  minWidth: `${
                    200 + 50 * (getFieldValue('clientVersion').length - 1) > 800
                      ? 800
                      : 200 + 50 * (getFieldValue('clientVersion').length - 1)
                  }px`,
                }}
                dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                placeholder="请选择版本"
                allowClear
                treeCheckable
                showCheckedStrategy={SHOW_PARENT}
                treeDefaultExpandAll
                treeNodeFilterProp="title"
                filterTreeNode
                onChange={value => {
                  if (value.includes('all')) {
                    dCombinationCheck('clientVersion');
                  }
                }}
              >
                <TreeNode value="all" title="全部" key="all">
                  {clientVersionData.length !== 0
                    ? treeNodeFormate(getFieldValue('pfCode'), clientVersionData)
                    : null}
                </TreeNode>
              </TreeSelect>,
            )}
          </FormItem>
        </Col>
      </Row>
      {/* {type === 2 ? null : ( */}
      <Row>
        <Col span={7} style={{ zIndex: 2 }}>
          <FormItem label="cp" style={{ marginBottom: '10px' }}>
            {getFieldDecorator('tenantId', {
              rules: [{ required: true, message: '请选择租户' }],
              initialValue: [],
            })(
              <Select
                placeholder="请选择租户"
                mode="multiple"
                style={{
                  minWidth: `${
                    200 + 50 * (getFieldValue('tenantId').length - 1) > 800
                      ? 800
                      : 200 + 50 * (getFieldValue('tenantId').length - 1)
                  }px`,
                }}
                allowClear
                showSearch
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                onSelect={value => selectDataFormate(value, 'tenantId')}
                onChange={value => {
                  getAdcode(value);
                  setFieldsValue({ adcode: [] });
                }}
              >
                <Option value="all" key="all">
                  全部
                </Option>
                {cpData.map(it => (
                  <Option value={it.enumValue} key={it.enumValue}>
                    {it.enumValueDesc}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Col>
      </Row>
      {/* )} */}
      <Row>
        <Col span={7} style={{ zIndex: 2 }}>
          <FormItem label="城市" style={{ marginBottom: '10px' }}>
            {getFieldDecorator('adcode', {
              rules: [{ required: true, message: '请选择城市' }],
              initialValue: [],
            })(
              <Select
                placeholder="请选择城市"
                mode="multiple"
                allowClear
                showSearch
                style={{
                  minWidth: `${
                    200 + 50 * (getFieldValue('adcode').length - 1) > 800
                      ? 800
                      : 200 + 50 * (getFieldValue('adcode').length - 1)
                  }px`,
                }}
                optionFilterProp="children"
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                onSelect={value => selectDataFormate(value, 'adcode')}
              >
                {adcodeData.length === 0 ? null : (
                  <Option value="all" key="all">
                    全部
                  </Option>
                )}
                {adcodeData.map(it => (
                  <Option value={it.enumValue} key={it.enumValue}>
                    {it.enumValueDesc}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={7} style={{ zIndex: 2 }}>
          <FormItem label="页面" style={{ marginBottom: '10px' }}>
            {getFieldDecorator('pageEngName', {
              rules: [{ required: true, message: '请选择页面' }],
              initialValue: [],
            })(
              <TreeSelect
                showSearch
                style={{
                  minWidth: `${
                    200 + 50 * (getFieldValue('pageEngName').length - 1) > 800
                      ? 800
                      : 200 + 50 * (getFieldValue('pageEngName').length - 1)
                  }px`,
                }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择页面"
                allowClear
                treeCheckable
                showCheckedStrategy={SHOW_PARENT}
                searchPlaceholder="快速查询页面名称"
                treeDefaultExpandAll
                treeNodeFilterProp="title"
                filterTreeNode
                onChange={value => {
                  getBtnEngName(value);
                  dCombinationCheck('btnEngName');
                  if (value.includes('all')) {
                    dCombinationCheck('pageEngName');
                  }
                  if (value.length === 0) {
                    setBtnDisable(false);
                  } else {
                    setBtnDisable(true);
                  }
                  setFieldsValue({ btnEngName: [] });
                }}
              >
                <TreeNode value="all" title="全部" key="all">
                  {pageEngNameData.map(p => (
                    <TreeNode value={p.enumValue} title={p.enumValueDesc} key={p.enumValue} />
                  ))}
                </TreeNode>
              </TreeSelect>,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={7} style={{ zIndex: 2 }}>
          <FormItem label="按钮" style={{ marginBottom: '10px' }}>
            {getFieldDecorator('btnEngName', {
              rules: [],
              initialValue: [],
            })(
              <TreeSelect
                showSearch
                style={{
                  minWidth: `${
                    200 + 50 * (getFieldValue('btnEngName').length - 1) > 800
                      ? 800
                      : 200 + 50 * (getFieldValue('btnEngName').length - 1)
                  }px`,
                }}
                dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                placeholder="请选择按钮"
                allowClear
                treeCheckable
                showCheckedStrategy={SHOW_PARENT}
                searchPlaceholder="快速查询按钮名称"
                treeDefaultExpandAll
                treeNodeFilterProp="title"
                filterTreeNode
                onChange={value => {
                  if (value.includes('all')) {
                    dCombinationCheck('btnEngName');
                    setBtnDisable(true);
                  } else if (value.length === 0) {
                    dCombinationCheck('btnEngName');
                    setBtnDisable(true);
                  } else {
                    setBtnDisable(false);
                  }
                }}
              >
                <TreeNode value="all" title="全部" key="all">
                  {btnEngNameData.length !== 0
                    ? treeFormate(getFieldValue('pageEngName'), btnEngNameData)
                    : null}
                </TreeNode>
              </TreeSelect>,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={18} style={{ zIndex: 2 }}>
          <FormItem
            label="维度合计"
            style={{ marginBottom: '10px' }}
            labelCol={{ span: 2 }}
            wrapperCol={{ push: 0 }}
          >
            {getFieldDecorator('dimensionCombination', {
              rules: [],
              initialValue: [],
            })(
              <Checkbox.Group
                options={checkOptionsAll}
                // options={type === 2 ? passengerOptions : checkOptionsAll}
                onChange={checkFormate}
              />,
            )}
          </FormItem>
        </Col>
      </Row>
      <FormItem>
        <Button
          type="primary"
          size="small"
          onClick={queryData}
          loading={loading.effects['information/fetchChartsData']}
        >
          查询
        </Button>
        <Button
          type="primary"
          size="small"
          ghost
          style={{ marginLeft: '20px' }}
          onClick={saveQueryData}
        >
          保存条件
        </Button>
        <Button
          type="primary"
          size="small"
          ghost
          style={{ marginLeft: '20px' }}
          onClick={() => {
            setSelectedTags([null]);
            setChecked(false);
            setCheckedPF(false);
            setFieldsValue({
              dimensionCombination: [],
              btnEngName: [],
              pageEngName: [],
              tenantId: [],
              adcode: [],
              clientVersion: [],
              pfCode: [],
              dataDate: null,
            });
            setBtnDisable(false);
            setBtnEngNameData([]);
          }}
        >
          清空条件
        </Button>
      </FormItem>
    </>
  );

  return (
    <Form {...formLayout} layout="vertical" style={{ marginTop: '20px' }}>
      {renderContent()}
      <Modal visible={saveVisible} onCancel={closeSaveVisible} onOk={saveAllQuery} width="300px">
        <Form layout="vertical" style={{ marginTop: '40px' }}>
          <FormItem>
            {getFieldDecorator('name', {
              rules: [
                {
                  required: saveVisible,
                  message: '请输入保存的名称',
                },
              ],
            })(<Input maxLength={10} placeholder="请输入名称,最多十个汉字" />)}
          </FormItem>
        </Form>
      </Modal>
    </Form>
  );
};

const ComponentForm = Form.create()(QueryForm);

export default connect(({ loading }) => ({
  loading,
}))(ComponentForm);
