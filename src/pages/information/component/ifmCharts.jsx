import { <PERSON>, <PERSON>eo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Axis } from 'bizcharts';
import React from 'react';
import moment from 'moment';

const ChartsComponent = ({ data }) => {
  const cols = {
    dataDate: {
      range: [0.05, 1],
    },
    value: {
      type: 'linear',
      min: 0,
      formatter(val) {
        return val;
      },
    },
  };

  const tickLine = {
    lineWidth: 1, // 刻度线宽
    stroke: '#ccc', // 刻度线的颜色
    length: 5, // 刻度线的长度, **原来的属性为 line**,可以通过将值设置为负数来改变其在轴上的方向
    alignWithLabel: false, // alignWithLabel设为false，且数据类型为 category 时，tickLine 的样式变为 category 数据专有样式
  };

  return (
    <div>
      <Chart height={500} data={data} scale={cols} forceFit padding={['auto', 'auto', 30, 'auto']}>
        <Legend position="top-center" marker="hyphen" />
        <Axis
          name="dataDate"
          tickLine={tickLine}
          label={{
            formatter: val => `${moment(val).format('MM-DD')}`,
          }}
        />
        <Axis
          name="value"
          line={{
            stroke: '#ccc',
            lineWidth: 1,
          }}
        />
        <Tooltip
          useHtml
          showTitle={false}
          g2-tooltip={{
            boxShadow: 'none',
            color: '#fff',
            backgroundColor: '#222',
          }}
          crosshairs={{
            type: 'y',
          }}
          style={{
            color: 'red',
          }}
        />
        <Geom type="line" position="dataDate*value" size={2} color="lineChartName" shape="smooth" />
      </Chart>
    </div>
  );
};

export default ChartsComponent;
