import React, { useEffect } from 'react';
import moment from 'moment';
import echarts from 'echarts';
import 'echarts/lib/chart/line';
// 引入提示框和标题组件
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/legend';

const ChartsComponent = ({ data = [], id }) => {
  const allData = data;

  const categoryData = allData.map(d => d.dataDate).filter((e, i, self) => self.indexOf(e) === i);

  const someDate = categoryData.map(d => allData.filter(f => f.dataDate === d));
  // const maxData = someDate.reduce((num1, num2) => {
  //   if (num1.length > num2.length) {
  //     return num1;
  //   }
  //   return num2;
  // });
  const maxData = [];
  allData.map(a => {
    if (!maxData.includes(a[`${id}LineChartName`])) {
      maxData.push(a[`${id}LineChartName`]);
    }
  });

  let legendData = [];
  let newAllData = [];
  const lineName = `${id}LineChartName`;
  if (maxData) {
    legendData = maxData;
    newAllData = someDate.map(d => {
      const datas = maxData.filter(v => !d.some(val => v === val[lineName]));
      const oneData = d[0];
      const newData = datas.map(w => {
        const newObj = {
          dataDate: oneData.dataDate,
          pv: 0,
          uv: 0,
        };
        newObj[lineName] = w;
        return newObj;
      });
      return [...d, ...newData];
    });
  }
  const dataFormate = dataVal => {
    const val = newAllData.map(d => {
      let valData;
      d.map(f => {
        if (f[lineName] === dataVal) {
          valData = f[id];
        }
      });
      return valData;
    });
    return val;
  };
  const seriesData = legendData.map(s => ({
    name: s,
    type: 'line',
    smooth: true,
    data: dataFormate(s),
  }));

  useEffect(() => {
    const myChart = echarts.init(document.getElementById(id));
    myChart.setOption(
      {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              show: false,
              backgroundColor: '#6a7985',
            },
          },
          formatter: value => {
            let res = '';
            value.map(val => {
              res += `${val.marker}${val.seriesName}： ${val.value}<br>`;
            });
            return res;
          },
        },
        legend: {
          type: 'scroll',
          data: legendData,
          height: '200px',
          itemHeight: 16,
        },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {},
        //   },
        // },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: categoryData,
            axisLabel: {
              formatter: val => moment(val).format('MM-DD'),
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: seriesData,
      },
      true,
    );
  }, [data]);

  return <div style={{ minHeight: 500 }} id={id}></div>;
};

export default ChartsComponent;
