import {
  Table,
  Radio,
  Pagination,
  List,
  Tabs,
  Icon,
  Dropdown,
  But<PERSON>,
  <PERSON>u,
  Divider,
  Empty,
  Spin,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import QueryForm from './component/queryForm';
import Charts from './component/lineECharts';
import { getUserTerminal } from './services';
import { downloadData } from '../../utils/utils';

const InformationComponent = ({
  tableData = [],
  pvuvData,
  queryList,
  total = 0,
  dispatch,
  loading,
  spinning = false,
}) => {
  const [type, setType] = useState(1);
  const [terminalCode, setTerminalCode] = useState(1);
  const [queryData, setQueryData] = useState(null);
  const [clickQueryData, setClickQueryData] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [appData, setAppData] = useState([]);

  const typeChange = e => {
    setType(e.target.value);
  };

  const getQueryList = value => {
    const params = {
      terminalCode: value,
    };
    dispatch({
      type: 'information/fetchQueryListData',
      payload: params,
    });
  };

  const deleteQuery = value => {
    const params = {
      id: value,
    };
    dispatch({
      type: 'information/deleteQueryData',
      payload: params,
      callback: () => getQueryList(terminalCode),
    });
  };

  const terminalCodeChange = e => {
    setTerminalCode(e.target.value);
    getQueryList(e.target.value);
  };

  const getVisualData = (query, ty) => {
    const params = {
      ...query,
      statisticType: ty,
    };
    dispatch({
      type: 'information/fetchChartsData',
      payload: params,
    });
  };

  const getTableData = query => {
    const params = {
      pageNo: 1,
      pageSize: 10,
      ...query,
    };
    dispatch({
      type: 'information/fetchTableData',
      payload: params,
    });
  };

  const getApps = async () => {
    const { code, data } = await getUserTerminal({});
    if (code === 1 && data) {
      if (data.length !== 0) {
        setType(data[0].id);
      }
      setAppData(data);
    }
  };

  useEffect(() => {
    getQueryList(terminalCode);
    getApps();
    return () => {
      dispatch({
        type: 'information/saveTableData',
        payload: [],
      });
      dispatch({
        type: 'information/savePvUvData',
        payload: [],
      });
    };
  }, []);

  const { TabPane } = Tabs;
  const columns = [
    {
      title: '日期',
      dataIndex: 'dataDate',
      key: 'dataDate',
      align: 'center',
    },
    {
      title: '页面',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
    },
    {
      title: '按钮',
      dataIndex: 'btnName',
      key: 'btnName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '版本',
      dataIndex: 'clientVersion',
      key: 'clientVersion',
      align: 'center',
    },
    {
      title: '城市',
      dataIndex: 'city',
      key: 'city',
      align: 'center',
    },
    {
      title: 'cp',
      dataIndex: 'tenantName',
      key: 'tenantName',
      align: 'center',
    },
    {
      title: 'pv',
      dataIndex: 'pv',
      key: 'pv',
      align: 'center',
    },
    {
      title: 'uv',
      dataIndex: 'uv',
      key: 'uv',
      align: 'center',
    },
  ];
  const getQueryData = value => {
    setQueryData(value);
    getTableData(value);
    getVisualData(value, 'pv');
  };

  const showTotal = Total => `共${Total}条`;
  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
    const parmas = {
      ...queryData,
      pageNo: page,
      pageSiz: pageSize,
    };
    getTableData(parmas);
  };
  const handlePageSizeChange = (current, pageSize) => {
    const parmas = {
      ...queryData,
      pageNo: currentPage,
      pageSiz: pageSize,
    };
    getTableData(parmas);
  };

  const setQuerInfo = data => {
    setClickQueryData(data);
  };
  const exportTableData = () => {
    if (!queryData) return;
    const params = {
      terminalCode: queryData.terminalCode,
      dataDate: queryData.dataDate,
      pfCode: queryData.pfCode,
      clientVersion: queryData.clientVersion,
      adcode: queryData.adcode,
      tenantId: queryData.tenantId,
      pageEngName: queryData.pageEngName,
      btnEngName: queryData.btnEngName,
      dimensionCombination: queryData.dimensionCombination,
    };
    downloadData(params);
  };

  const menu = (
    <Menu>
      <div style={{ maxHeight: '300px', overflow: 'auto', padding: '10px', background: '#fff' }}>
        <Radio.Group value={type} buttonStyle="solid" size="small" onChange={terminalCodeChange}>
          {appData.map(a => (
            <Radio.Button value={a.id} key={a.id}>
              {a.name}
            </Radio.Button>
          ))}
        </Radio.Group>
        {queryList.length !== 0 ? (
          <List
            dataSource={queryList}
            renderItem={item => (
              <List.Item style={{ cursor: 'pointer' }}>
                <div style={{ minWidth: '250px' }}>
                  <span onClick={() => setQuerInfo(item)}>{item.queryName}</span>
                  <span style={{ float: 'right', fontSize: '16px' }}>
                    <Icon type="delete" onClick={() => deleteQuery(item.id)} />
                  </span>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <div
            style={{ height: '100px', lineHeight: '100px', textAlign: 'center', padding: '0 10px' }}
          >
            请先保存查询条件再进行快速查询
          </div>
        )}
      </div>
    </Menu>
  );
  return (
    <PageHeaderWrapper
      title={false}
      content={
        <div style={{ marginTop: '20px' }}>
          <div>
            <Radio.Group value={type} buttonStyle="solid" onChange={typeChange}>
              {appData.map(a => (
                <Radio.Button value={a.id} key={a.id}>
                  {a.name}
                </Radio.Button>
              ))}
            </Radio.Group>
            <span style={{ float: 'right' }}>
              <Dropdown
                overlay={menu}
                onVisibleChange={v => {
                  if (v) {
                    getQueryList(terminalCode);
                  }
                }}
              >
                <Button style={{ width: 250 }}>
                  快速查询{' '}
                  <span style={{ float: 'right' }}>
                    <Icon type="search" />
                  </span>
                </Button>
              </Dropdown>
            </span>
          </div>
          <QueryForm type={type} getAllData={getQueryData} clickQueryData={clickQueryData} />
          <Divider style={{ height: '2px' }} />
          <Spin spinning={spinning}>
            <Tabs type="card">
              <TabPane tab="pv" key="pv">
                {pvuvData.length !== 0 ? (
                  <Charts data={pvuvData} id="pv" />
                ) : (
                  <Empty description="暂无数据" />
                )}
              </TabPane>
              <TabPane tab="uv" key="uv">
                {pvuvData.length !== 0 ? (
                  <Charts data={pvuvData} id="uv" />
                ) : (
                  <Empty description="暂无数据" />
                )}
              </TabPane>
            </Tabs>
          </Spin>
          <Divider style={{ height: '2px' }} />
          <div style={{ color: '#0d1a26', fontWeight: 500 }}>
            数据列表
            <Button
              type="primary"
              style={{ float: 'right' }}
              disabled={tableData.length === 0}
              onClick={exportTableData}
            >
              导出
            </Button>
          </div>
          <Table
            columns={columns}
            dataSource={tableData}
            style={{ marginTop: '20px' }}
            bordered
            rowKey="id"
            loading={loading.effects['information/fetchTableData']}
            pagination={false}
          />
          <Pagination
            style={{ float: 'right', marginTop: '20px' }}
            showQuickJumper
            showSizeChanger
            disabled={total === 0}
            showTotal={showTotal}
            current={currentPage}
            onChange={handlePageChange}
            onShowSizeChange={handlePageSizeChange}
            total={total}
          />
        </div>
      }
    ></PageHeaderWrapper>
  );
};

export default connect(({ information, loading }) => ({
  tableData: information.tableData,
  pvuvData: information.pvuvData,
  queryList: information.queryList,
  total: information.total,
  loading,
  spinning: loading.effects['information/fetchChartsData'],
}))(InformationComponent);
