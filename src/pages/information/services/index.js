import request from '@/utils/request';

// 枚举值码表同步
export async function getEnumParamData(params) {
  return request('/admin/v1/eventlog/sync/list/enumparam', {
    params,
  });
}

// PV&UV统计图查询
export async function getPvUvChartsData(params) {
  return request('/admin/v1/eventlog/query/graph/page/pvuv', {
    method: 'POST',
    data: params,
  });
}

// PV&UV统计列表查询
export async function getPvUvTableData(params) {
  return request('/admin/v1/eventlog/query/form/page/pvuv', {
    method: 'POST',
    data: params,
  });
}

//  PV&UV统计列表导出
export async function exportPvUvTableData(params) {
  return request('/admin/v1/eventlog/export/form/list/pvuv', {
    method: 'POST',
    data: params,
  });
}

// 查询条件保存
export async function saveQueryData(params) {
  return request('/admin/v1/eventlog/save/quickquery', {
    method: 'POST',
    data: params,
  });
}

// 快速查询列表查询
export async function getQueryListData(params) {
  return request('/admin/v1/eventlog/query/list/quickquery', {
    method: 'POST',
    data: params,
  });
}

// 查询条件删除
export async function deleteQueryData(params) {
  return request(`/admin/v1/eventlog/delete/quickquery?id=${params.id}`, {
    method: 'PUT',
  });
}

// 获取多租户开城接口
export async function getTennantAdcode(params) {
  return request('/admin/v1/eventlog/get/tenant/adcode', {
    method: 'POST',
    data: params,
  });
}

// 获取用户多租户接口
export async function getUserTennant(params) {
  return request('/admin/v1/eventlog/auth/tenant/list/get', {
    params,
  });
}

// 获取用户应用接口
export async function getUserTerminal(params) {
  return request('/admin/v1/eventlog/auth/terminal/list/get', {
    params,
  });
}
