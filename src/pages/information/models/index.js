import { notification, message } from 'antd';
import { getPvUvChartsData, getPvUvTableData, saveQueryData, getQueryListData, deleteQueryData } from '../services';

const Model = {
  namespace: 'information',
  state: {
    tableData: [],
    pvuvData: [],
    queryList: [],
    total: 0,
  },
  effects: {
    *fetchTableData({ payload }, { call, put }) {
      const response = yield call(getPvUvTableData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveTableData',
          payload: Array.isArray(data.dataDetail) ? data.dataDetail : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalCount : 0,
        });
      } else {
        notification.warn({
          message: '请求PV&UV统计列表失败',
          description: msg,
        });
      }
    },
    *fetchChartsData({ payload }, { call, put }) {
      const response = yield call(getPvUvChartsData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
          yield put({
            type: 'savePvUvData',
            payload: Array.isArray(data.dataDetail) ? data.dataDetail : [],
          });
      } else {
        notification.warn({
          message: '请求PV&UV统计列表失败',
          description: msg,
        });
      }
    },
    *fetchQueryListData({ payload }, { call, put }) {
      const response = yield call(getQueryListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveQueryList',
          payload: Array.isArray(data) ? data : [],
        });
      } else {
        notification.warn({
          message: '请求保存条件列表失败',
          description: msg,
        });
      }
    },
    *saveQuery({ payload, callback }, { call, put }) {
      const response = yield call(saveQueryData, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('保存成功');
      } else {
        notification.warn({
          message: '保存失败',
          description: msg,
        });
      }
    },
    *deleteQueryData({ payload, callback }, { call, put }) {
      const response = yield call(deleteQueryData, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        message.success('删除成功');
        callback();
      } else {
        notification.warn({
          message: '删除失败',
          description: msg,
        });
      }
    },
  },
  reducers: {
    saveTableData(state, action) {
      return { ...state, tableData: action.payload }
    },
    savePvUvData(state, action) {
      return { ...state, pvuvData: action.payload }
    },
    saveQueryList(state, action) {
      return { ...state, queryList: action.payload }
    },
    saveTotal(state, action) {
      return { ...state, total: action.payload }
    },
  },
};
export default Model;
