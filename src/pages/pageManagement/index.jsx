import {
  Input,
  Button,
  Form,
  Select,
  notification,
  Icon,
  Tree,
  Divider,
  Row,
  Col,
  Modal,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { authBtn } from '@/utils/utils';
import styles from './style.less';
import AddPage from './component/addPage';
import { candelete, canupdate, pageDownload, isexist, getUserTerminal } from './services';

const PageComponent = ({
  dispatch,
  form,
  loading,
  listData = [],
  ssoAuthorityRole: {
    data: { curResources = [] },
  },
}) => {
  const FormItem = Form.Item;
  const { Option } = Select;
  const { TreeNode, DirectoryTree } = Tree;
  const [visible, setVisible] = useState(false);
  const [addFileVisible, setAddFileVisible] = useState(false);
  const [detail, setDetail] = useState(null);
  const [imageData, setImageData] = useState(null);
  const [imageId, setImageId] = useState(null);
  const [appData, setAppData] = useState([]);
  const [authList, setAuthList] = useState([]);
  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };

  const { getFieldDecorator, setFieldsValue, getFieldValue } = form;

  const closeModal = () => {
    setVisible(false);
    setAddFileVisible(false);
    setDetail(null);
  };

  const openModal = () => {
    setVisible(true);
  };

  const openAddFileModal = () => {
    setAddFileVisible(true);
  };

  const getApps = async () => {
    const { code, data } = await getUserTerminal({});
    if (code === 1 && data) {
      setAppData(data);
    }
  };

  const queryData = () => {
    form.validateFields(async (err, values) => {
      if (err) return false;
      const params = {
        terminalName: values.terminalName.label,
        pfName: values.pfName.label,
      };
      dispatch({
        type: 'pageManagement/fetchlistData',
        payload: params,
      });
    });
  };

  const onBlurExists = async (val, type) => {
    const terminalName = getFieldValue('terminalName').label;
    const pfName = getFieldValue('pfName').label;
    const params = {
      terminalName,
      pfName,
      resourceType: 0,
    };
    if (Array.isArray(terminalName) || Array.isArray(pfName) || Array.isArray(val) || val === '') {
      return;
    }
    params[type] = val;
    const { code, data } = await isexist(params);
    if (code === 1) {
      if (data === true) {
        notification.warn({
          message: '文件夹名称已存在',
          description: '请重新输入名称',
        });
      }
    }
  };

  const isAuthBtn = key => authList.includes(key);

  useEffect(() => {
    getApps();
    queryData();
  }, []);

  useEffect(() => {
    if (Array.isArray(curResources)) {
      curResources.map(c => {
        if (c.resourceKey === 'documents') {
          const authBtnList = authBtn(c.subList, 'pageManagement');
          setAuthList(authBtnList);
        }
      });
    }
  }, [curResources]);

  const closeFolder = () => {
    setAddFileVisible(false);
    setDetail(null);
  };

  const createFolder = () => {
    form.validateFields(async (err, values) => {
      if (err) return false;
      const params = {
        terminalName: values.terminalName.label,
        pfName: values.pfName.label,
        pageName: values.pageName,
        resourceType: 0,
      };
      if (detail === null) {
        dispatch({
          type: 'pageManagement/createPageresourceData',
          payload: params,
          callback: () => {
            queryData();
            setAddFileVisible(false);
          },
        });
      } else {
        dispatch({
          type: 'pageManagement/editPageresourceData',
          payload: { ...params, id: detail.id },
          callback: () => {
            queryData();
            setAddFileVisible(false);
            setDetail(null);
          },
        });
      }
    });
  };

  const renderContent = () => (
    <>
      <FormItem label="应用">
        {getFieldDecorator('terminalName', {
          rules: [{ required: true, message: '请选择应用' }],
          initialValue: { label: '司机端', key: 1 },
        })(
          <Select placeholder="请选择" style={{ minWidth: '150px' }} labelInValue>
            {appData.map(a => (
              <Option value={a.id} key={a.id}>
                {a.name}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>
      <FormItem label="平台">
        {getFieldDecorator('pfName', {
          rules: [{ required: true, message: '请选择平台' }],
          initialValue: { label: 'IOS/ANDROID', key: 3 },
        })(
          <Select placeholder="请选择" labelInValue style={{ minWidth: '150px' }}>
            <Option value={3} key={3}>
              IOS/ANDROID
            </Option>
            <Option value={4} key={4}>
              h5
            </Option>
            <Option value={5} key={5}>
              web
            </Option>
            <Option value={6} key={6}>
              服务端
            </Option>
          </Select>,
        )}
      </FormItem>
      <FormItem>
        {isAuthBtn('query') ? (
          <Button type="primary" size="default" onClick={queryData} loading={loading}>
            查询
          </Button>
        ) : null}
      </FormItem>
      <FormItem>
        {isAuthBtn('addWenjianjia') ? (
          <Button type="primary" size="default" onClick={openAddFileModal}>
            新增文件夹
          </Button>
        ) : null}
      </FormItem>
      <FormItem>
        {isAuthBtn('addYemian') ? (
          <Button type="primary" size="default" onClick={openModal}>
            新增页面
          </Button>
        ) : null}
      </FormItem>
    </>
  );

  const deleteTreeData = params => {
    dispatch({
      type: 'pageManagement/pageresourceDelete',
      payload: params,
      callback: () => {
        queryData();
        if (params.id === imageId) {
          setImageData(null);
        }
      },
    });
  };

  const queryIsDelete = async (id, name, e) => {
    e.stopPropagation();
    const params = {
      id,
      pageName: name,
    };
    const { code, data, msg } = await candelete(params);
    if (code === 1 && data) {
      deleteTreeData(params);
    } else {
      notification.warn({
        message: '该页面埋点正在审核中，禁止删除',
        description: msg,
      });
    }
  };

  const queryIsEdit = async (info, e) => {
    e.stopPropagation();
    const params = {
      id: info.id,
      pageName: info.pageName,
    };
    const { code, data, msg } = await canupdate(params);
    if (code === 1 && data) {
      if (info.resourceType === 0) {
        setAddFileVisible(true);
        setFieldsValue({ pageName: info.pageName });
      } else {
        openModal();
      }
      setDetail(info);
    } else {
      notification.warn({
        message: '不可编辑',
        description: msg,
      });
    }
  };

  const getImgData = async url => {
    const params = {
      path: url,
    };
    const { code, data, msg } = await pageDownload(params);
    if (code === 1 && data) {
      setImageData(data);
    } else {
      notification.warn({
        message: '获取图片失败',
        description: msg,
      });
    }
  };

  const treeFormate = data =>
    data.map(d => {
      if (d.resourceType === 0) {
        return (
          <TreeNode
            title={
              <span>
                {d.pageName}
                <span className={styles.folderOption}>
                  {isAuthBtn('update') ? (
                    <Icon
                      type="edit"
                      onClick={e => queryIsEdit(d, e)}
                      style={{ marginRight: '10px' }}
                    />
                  ) : null}
                  {isAuthBtn('delete') ? (
                    <Icon type="delete" onClick={e => queryIsDelete(d.id, d.pageName, e)} />
                  ) : null}
                </span>
              </span>
            }
            key={d.id}
            selectable={false}
            className={styles.folder}
          >
            {d.subResourceList ? treeFormate(d.subResourceList) : null}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          title={
            <span
              style={{ display: 'inline-block', width: '300px' }}
              onClick={() => {
                getImgData(d.pageUrl);
                setImageId(d.id);
              }}
            >
              {d.pageName}
              <span className={styles.pageOption}>
                {isAuthBtn('update') ? (
                  <Icon
                    type="edit"
                    style={{ marginRight: '10px' }}
                    onClick={e => queryIsEdit(d, e)}
                  />
                ) : null}
                {isAuthBtn('delete') ? (
                  <Icon type="delete" onClick={e => queryIsDelete(d.id, d.pageName, e)} />
                ) : null}
              </span>
            </span>
          }
          key={d.id}
          icon={<Icon type="picture" onClick={() => getImgData(d.pageUrl)} />}
          className={styles.page}
        >
          {d.subResourceList ? treeFormate(d.subResourceList) : null}
        </TreeNode>
      );
    });

  return (
    <PageHeaderWrapper
      title={false}
      content={
        <div style={{ marginTop: '10px', minHeight: '400px' }}>
          <Divider style={{ margin: '10px 0' }} />
          <Form {...formLayout} layout="inline" style={{ marginBottom: '20px' }}>
            {renderContent()}
          </Form>
          <Row>
            <Col span={10}>
              <DirectoryTree>{treeFormate(listData)}</DirectoryTree>
            </Col>
            {imageData ? (
              <Col span={10} offset={1}>
                <img src={imageData} alt={imageData.name} style={{ width: '400px' }} />
              </Col>
            ) : null}
          </Row>
          <Modal
            visible={addFileVisible}
            width="400px"
            title={
              <div style={{ textAlign: 'center' }}>{detail ? '编辑文件夹' : '新增文件夹'}</div>
            }
            centered
            onCancel={closeFolder}
            onOk={createFolder}
          >
            <Form style={{ marginTop: '20px' }} layout="horizontal">
              <FormItem
                style={{ margin: 0 }}
                label="文件夹名称"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 15 }}
              >
                {getFieldDecorator('pageName', {
                  initialValue: [],
                  rules: [{ required: addFileVisible, message: '请输入文件夹名称，10字以内' }],
                })(
                  <Input
                    maxLength={10}
                    placeholder="请输入文件夹名称，10字以内"
                    onBlur={() => onBlurExists(getFieldValue('pageName'), 'pageName')}
                  />,
                )}
              </FormItem>
            </Form>
          </Modal>
          <AddPage
            visible={visible}
            closeModal={closeModal}
            queryData={queryData}
            appData={appData}
            detail={detail}
          />
        </div>
      }
    ></PageHeaderWrapper>
  );
};

const PageComponentForm = Form.create()(PageComponent);

export default connect(({ pageManagement, loading, ssoAuthorityRole }) => ({
  loading: loading.effects['pageManagement/fetchlistData'],
  listData: pageManagement.listData,
  ssoAuthorityRole,
}))(PageComponentForm);
