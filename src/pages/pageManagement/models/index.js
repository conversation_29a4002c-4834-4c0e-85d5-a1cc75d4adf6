import { notification, message } from 'antd';
import { getListData, createPageresource, editPageresource, deletePageresource } from '../services';

const UserModel = {
  namespace: 'pageManagement',
  state: {
    listData: [],
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data) ? data : [],
        });
      } else {
        notification.warn({
          message: '请求页面列表失败',
          description: msg,
        });
      }
    },
    *createPageresourceData({ payload, callback }, { call }) {
      const response = yield call(createPageresource, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('创建成功');
      } else {
        notification.warn({
          message: '创建失败',
          description: msg,
        });
      }
    },
    *editPageresourceData({ payload, callback }, { call }) {
      const response = yield call(editPageresource, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('编辑成功');
      } else {
        notification.warn({
          message: '编辑失败',
          description: msg,
        });
      }
    },
    *pageresourceDelete({ payload, callback }, { call }) {
      const response = yield call(deletePageresource, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('删除成功');
      } else {
        notification.warn({
          message: '删除失败',
          description: msg,
        });
      }
    },
  },
  reducers: {
    saveListData(state, action) {
      return { ...state, listData: action.payload }
    },
  },
};
export default UserModel;
