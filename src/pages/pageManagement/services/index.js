import request from '@/utils/request';

// 页面树查询
export async function getListData(pamas) {
  return request('/admin/v1/eventlog/pageresource/getlist', {
    method: 'POST',
    data: pamas,
  });
}

// 页面下拉选择框
export async function getPageList(params = {}) {
  return request('/admin/v1/eventlog/pageresource/getflatlistonlypage', {
    method: 'POST',
    data: params,
  });
}

// 新增文件夹/页面
export async function createPageresource(params = {}) {
  return request('/admin/v1/eventlog/pageresource/add', {
    method: 'POST',
    data: params,
  });
}

// 查询是否可以编辑
export async function canupdate(params = {}) {
  return request('/admin/v1/eventlog/pageresource/canupdate', {
    method: 'POST',
    data: params,
  });
}

// 编辑文件夹\页面
export async function editPageresource(params = {}) {
  return request('/admin/v1/eventlog/pageresource/update', {
    method: 'POST',
    data: params,
  });
}

// 查询是否可以删除
export async function candelete(params = {}) {
  return request('/admin/v1/eventlog/pageresource/candelete', {
    method: 'POST',
    data: params,
  });
}

// 删除资源
export async function deletePageresource(params = {}) {
  return request('/admin/v1/eventlog/pageresource/delete', {
    method: 'POST',
    data: params,
  });
}

// 查询资源是否存在
export async function isexist(params = {}) {
  return request('/admin/v1/eventlog/pageresource/isexist', {
    method: 'POST',
    data: params,
  });
}

// 查询资源详情
export async function pageresourceDetail(params = {}) {
  return request('/admin/v1/eventlog/pageresource/get', {
    method: 'POST',
    data: params,
  });
}

// 页面上传
export async function pageUpload(params = {}) {
  return request('/admin/v1/eventlog/upload', {
    method: 'POST',
    data: params,
  });
}

// 页面下载
export async function pageDownload(params = {}) {
  return request('/admin/v1/eventlog/getPresignedPubUrl', {
    params,
  });
}

// 获取用户应用接口
export async function getUserTerminal(params) {
  return request('/admin/v1/eventlog/auth/terminal/list/get', {
    params,
  });
}
