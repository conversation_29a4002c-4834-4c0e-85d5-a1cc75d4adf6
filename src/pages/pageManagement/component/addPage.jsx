/* eslint-disable no-use-before-define */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable import/named */
import {
  Input,
  Button,
  Form,
  Select,
  Radio,
  Modal,
  Row,
  Col,
  Tree,
  Upload,
  Icon,
  notification,
  message,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { getEnumParamData } from '../../stopwatch/services';
import { pageUpload, getListData, isexist } from '../services';

const { TreeNode, DirectoryTree } = Tree;

const StopComponent = ({
  dispatch,
  form,
  loading,
  visible,
  detail,
  closeModal,
  queryData,
  appData,
}) => {
  const FormItem = Form.Item;
  const { Option } = Select;
  const [sdpVisible, setSdpVisible] = useState(false);
  const [pfData, setPFData] = useState([]);
  const [treeNodeKey, setTreeNodeKey] = useState(null);
  const [treeData, setTreeData] = useState([]);
  const [fId, setFId] = useState(0);
  const [radioValue, setRadioValue] = useState(2);

  const { getFieldDecorator, setFieldsValue, getFieldValue, resetFields } = form;

  useEffect(() => {
    getPFCode();
    resetFields();
    if (detail) {
      const values = {
        terminalName: detail.terminalName,
        pfName: detail.pfName,
        pageName: detail.pageName,
        pageEngName: detail.pageEngName,
        upload: [
          {
            uid: `1236219${detail.pageName}`, // 注意，这个uid一定不能少，否则上传失败
            name: detail.pageName,
            status: 'done',
            url: detail.pageUrl,
          },
        ],
      };
      setFieldsValue(values);
    }
  }, [detail]);

  const openPostion = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      if (!values.upload[0].url) {
        notification.warn({
          message: '请重新上传图片再提交',
          description: '图片地址错误',
        });
        return;
      }
      const params = {
        terminalName: values.terminalName,
        pfName: values.pfName,
      };
      setRadioValue(2);
      getTreeData(params);
      setSdpVisible(true);
    });
  };

  const savePageData = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      if (treeNodeKey === null) {
        notification.warn({
          message: '操作错误',
          description: '请先选择位置再保存',
        });
        return;
      }
      if (!values.upload[0].url) {
        notification.warn({
          message: '请重新上传图片再提交',
          description: '图片地址错误',
        });
        return;
      }
      const params = {
        terminalName: values.terminalName,
        pfName: values.pfName,
        pageName: values.pageName,
        pageEngName: values.pageEngName,
        resourceType: 1,
        resourcePid: Number(fId),
        pageUrl: values.upload[0].url,
      };
      if (detail) {
        dispatch({
          type: 'pageManagement/editPageresourceData',
          payload: { ...params, id: detail.id },
          callback: () => {
            setSdpVisible(false);
            closeModal();
            queryData();
          },
        });
      } else {
        dispatch({
          type: 'pageManagement/createPageresourceData',
          payload: params,
          callback: () => {
            setSdpVisible(false);
            closeModal();
            queryData();
          },
        });
      }
    });
  };

  const normFile = e => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const onBlurExists = async (val, type) => {
    const terminalName = getFieldValue('terminalName');
    const pfName = getFieldValue('pfName');
    const params = {
      terminalName,
      pfName,
      resourceType: 1,
    };
    if (Array.isArray(terminalName) || Array.isArray(pfName) || Array.isArray(val) || val === '') {
      return;
    }
    params[type] = val;
    const { code, data } = await isexist(params);
    if (code === 1) {
      if (data === true) {
        notification.warn({
          message: type === 'pageName' ? '页面中文名称已存在' : '页面英文名称已存在',
          description: '请重新输入名称',
        });
      }
    }
  };

  const radioOnChange = e => {
    setRadioValue(e.target.value);
  };

  const getPFCode = async () => {
    const { code, data } = await getEnumParamData({ enumCategory: 'pf', parentId: ['0'] });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.enumValueDesc, value: d.enumValue }));
      setPFData(newData);
    }
  };

  const getTreeData = async params => {
    const { code, data } = await getListData(params);
    if (code === 1 && data) {
      setTreeData(data);
    }
  };

  const imgUpload = async option => {
    const newfile = {
      uid: option.file.uid, // 注意，这个uid一定不能少，否则上传失败
      name: option.file.name,
      status: 'uploading',
      url: '',
    };
    setFieldsValue({ upload: [newfile] });
    const formData = new FormData();
    formData.append('file', option.file);
    formData.append('contentType', option.file.type);
    const res = await pageUpload(formData);
    if (res) {
      newfile.status = 'done';
      newfile.url = res.url;
      setFieldsValue({ upload: [newfile] });
      message.success('图片上传成功！');
    } else {
      newfile.status = 'error';
      setFieldsValue({ upload: [newfile] });
      notification.warn({
        message: '上传失败',
        description: '',
      });
    }
  };

  const handleChange = ({ file, fileList }) => {
    if (file.status === 'removed') {
      setFieldsValue({ upload: [] });
    }
  };

  const renderContent = () => (
    <>
      <Row>
        <Col span={11}>
          <FormItem label="应用">
            {getFieldDecorator('terminalName', {
              rules: [{ required: true, message: '请选择应用' }],
              initialValue: [],
            })(
              <Select placeholder="请选择应用" style={{ minWidth: '150px' }}>
                {appData.map(a => (
                  <Option value={a.name} key={a.name}>
                    {a.name}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Col>
        <Col span={11} offset={2}>
          <FormItem label="平台">
            {getFieldDecorator('pfName', {
              rules: [{ required: true, message: '请选择平台' }],
              initialValue: [],
            })(
              <Select placeholder="请选择平台" style={{ minWidth: '150px' }}>
                {pfData.map(d => (
                  <Option value={d.label} key={d.label}>
                    {d.label}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={11}>
          <FormItem label="页面中文名称">
            {getFieldDecorator('pageName', {
              rules: [{ required: true, message: '请输入页面中文名称' }],
              initialValue: [],
            })(
              <Input
                placeholder="请输入页面中文名称"
                onBlur={() => onBlurExists(getFieldValue('pageName'), 'pageName')}
              />,
            )}
          </FormItem>
        </Col>
        <Col span={11} offset={2}>
          <FormItem label="页面英文名称">
            {getFieldDecorator('pageEngName', {
              rules: [{ required: true, message: '请输入页面英文名称' }],
              initialValue: [],
            })(
              <Input
                placeholder="请输入页面英文名称"
                onBlur={() => onBlurExists(getFieldValue('pageEngName'), 'pageEngName')}
              />,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={11}>
          <FormItem label="页面图片上传">
            {getFieldDecorator('upload', {
              rules: [{ required: true, message: '请上传页面图片' }],
              valuePropName: 'fileList',
              getValueFromEvent: normFile,
            })(
              <Upload
                showUploadList={{
                  showDownloadIcon: false,
                }}
                accept={imgFormat}
                customRequest={imgUpload}
                onChange={handleChange}
              >
                <Button type="primary">上传</Button>
              </Upload>,
            )}
          </FormItem>
        </Col>
      </Row>
    </>
  );

  const imgFormat =
    '.bmp,.jpg,.png,.tif,.gif,.pcx,.tga,.exif,.fpx,.svg,.psd,.cdr,.pcd,.dxf,.ufo,.eps,.ai,.raw,.WMF,.webp';

  const treeFormate = (data, id) =>
    data.map(d => {
      if (d.resourceType === 0) {
        return (
          <TreeNode title={d.pageName} key={d.id}>
            {d.subResourceList ? treeFormate(d.subResourceList, d.id) : null}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          disabled={detail ? detail.id === d.id : false}
          title={
            <span>
              {d.pageName}
              {Number(treeNodeKey) === d.id ? (
                <span style={{ marginLeft: '20px' }}>
                  <Radio.Group
                    onChange={e => {
                      radioOnChange(e);
                      if (e.target.value === 1) {
                        setFId(id);
                      } else {
                        setFId(d.id);
                      }
                    }}
                    value={radioValue}
                  >
                    <Radio value={1}>同级</Radio>
                    <Radio value={2}>子级</Radio>
                  </Radio.Group>
                </span>
              ) : null}
            </span>
          }
          key={d.id}
          icon={<Icon type="picture" />}
        >
          {d.subResourceList ? treeFormate(d.subResourceList, d.id) : null}
        </TreeNode>
      );
    });

  return (
    <Modal
      visible={visible}
      title={<div style={{ textAlign: 'center' }}>{detail ? '编辑页面' : '新增页面'}</div>}
      onCancel={closeModal}
      destroyOnClose
      centered
      onOk={openPostion}
    >
      <Form layout="vertical">{renderContent()}</Form>
      <Modal
        visible={sdpVisible}
        title={<div style={{ textAlign: 'center' }}>页面位置选择</div>}
        onCancel={() => setSdpVisible(false)}
        centered
        width="600px"
        onOk={savePageData}
      >
        <DirectoryTree
          onSelect={key => {
            setTreeNodeKey(key[0]);
            setFId(key[0]);
          }}
        >
          {treeFormate(treeData)}
        </DirectoryTree>
      </Modal>
    </Modal>
  );
};

const ComponentForm = Form.create()(StopComponent);

export default connect(() => ({}))(ComponentForm);
