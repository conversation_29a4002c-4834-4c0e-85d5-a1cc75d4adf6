import {
  Input,
  Button,
  Form,
  Select,
  Divider,
  notification,
  Menu,
  Row,
  Col,
  Tabs,
  Card,
  Empty,
  Typography,
  message,
} from 'antd';

import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { authBtn } from '@/utils/utils';
import router from 'umi/router';
import { isEqual, transform, isObject, cloneDeep, differenceBy } from 'lodash';
import Title from 'antd/lib/skeleton/Title';
import styles from './style.less';
import { getVersionData, getListData, getDetail, updateTracking } from './services';
import { func } from 'prop-types';

const PageComponent = ({
  dispatch,
  form,
  pageQuery,
  ssoAuthorityRole: {
    data: { curResources = [] },
  },
}) => {
  const { Search } = Input;
  const { SubMenu } = Menu;
  const { TabPane } = Tabs;
  const FormItem = Form.Item;
  const { Option } = Select;
  const [versionData, setVersionData] = useState([]);
  const [listData, setListData] = useState([]);

  const [version, setVersion] = useState(null);
  const [pageId, setPageId] = useState(null);
  const [sName, setSName] = useState('');

  const [editable, setEditable] = useState(false);
  const [detail, setDetail] = useState(null);
  const [oldDetail, setOldDetail] = useState(null);
  const [formData, setFormData] = useState(null);
  const [authList, setAuthList] = useState([]);

  // const tabs = [
  //   {
  //     key: 'driver',
  //     label: '司机端',
  //   },
  //   {
  //     key: 'hubble',
  //     label: '哈勃后台',
  //   },
  //   {
  //     key: 'tenant',
  //     label: '租户后台',
  //   },
  // ];

  const getVersions = async () => {
    const response = await getVersionData();
    if (!response) return;
    const { code, data, msg } = response;
    setVersionData(data);
  };

  const getPages = async () => {
    const query = {
      version,
    };
    if (sName) {
      query.name = sName;
    }
    const response = await getListData(query);
    if (!response) return;
    const { code, data, msg } = response;
    if (code === 1) {
      setListData(Array.isArray(data) ? data : []);
    } else {
      notification.warn({
        message: '请求列表失败',
        description: msg,
      });
    }
  };

  const getFormData = async () => {
    const response = await getDetail({
      pageId,
      version,
    });
    if (!response) return;
    const { code, data, msg } = response;
    if (code === 1) {
      setEditable(false);
      const copyData = cloneDeep(data);
      copyData.tracks = {
        buttons: [],
        others: [],
      };
      data.tracks.forEach((track, index) => {
        let type = track.ele == 1 ? 'buttons' : 'others';
        copyData.tracks[type].push(track);
      });
      setDetail(copyData);
      setOldDetail(copyData);
    } else {
      notification.warn({
        message: '请求页面元素失败',
        description: msg,
      });
    }
  };

  useEffect(() => {
    getVersions();
  }, []);

  useEffect(() => {
    if (pageId) {
      setTimeout(() => {
        getFormData();
      }, 0);
    }
  }, [pageId]);

  useEffect(() => {
    if (version) {
      getPages();
    }
  }, [version]);

  useEffect(() => {
    if (versionData.length) {
      setVersion(versionData[0]);
    }
  }, [versionData]);

  useEffect(() => {
    if (listData.length) {
      setPageId(listData[0].pageId);
    }
  }, [listData]);

  // useEffect(() => {
  //   if(sName && version) {
  //     getPages()
  //   }
  // }, [sName])

  useEffect(() => {
    if (Array.isArray(curResources)) {
      curResources.map(c => {
        if (c.resourceKey === 'documents') {
          const authBtnList = authBtn(c.subList, 'sourceManagement');
          setAuthList(authBtnList);
        }
      });
    }
  }, [curResources]);

  const handleTabChange = tab => {
    setSName('');
  };

  const handleVersionChange = ver => {
    setSName('');
    setVersion(ver);
    // getPages({
    //   version: ver,
    // });
  };

  const handleKeywordChange = e => {
    setSName(e.target.value || '');
  };

  const handleSearch = sn => {
    setPageId(null);
    setDetail(null);
    getPages();
  };

  const handleIdChange = id => {
    setPageId(id);
  };

  const handleEditOrSave = async () => {
    if (!editable) {
      const detailCopy = cloneDeep(detail);
      setOldDetail(detailCopy);
    }
    if (editable) {
      if (isEqual(detail, oldDetail)) {
        return message.warning('数据无改变！');
      }
      const formData = { ...detail };
      formData.version = version;
      formData.tracks = [];
      const editedButtons =
        (detail.tracks.buttons &&
          detail.tracks.buttons.filter((track, index) => {
            return !isEqual(track, oldDetail.tracks.buttons[index]);
          })) ||
        [];
      const editedOthers =
        (detail.tracks.others &&
          detail.tracks.others.filter((track, index) => {
            return !isEqual(track, oldDetail.tracks.others[index]);
          })) ||
        [];
      formData.tracks = formData.tracks
        .concat(editedButtons)
        .concat(editedOthers)
        .map(track => {
          delete track.ele;
          return track;
        });
      const response = await updateTracking(formData);
      if (!response) {
        setEditable(!editable);
        return;
      }
      const { code, data, msg } = response;
      if (code === 1) {
        if(detail.pageShowName !== oldDetail.pageShowName) {
          let index = listData.findIndex((page) => {
            return page.pageId === pageId
          })
          if(index !== -1) {
            listData[index].pageShowName = detail.pageShowName
            setListData(listData)
          }
        }
        setOldDetail(detail);
        message.success('编辑成功！');
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    }
    setEditable(!editable);
  };

  const changePageShowName = pageShowName => {
    const newDetail = cloneDeep(detail);
    newDetail.pageShowName = pageShowName;
    setDetail(newDetail);
  };

  const changeFormItem = (trackingShowName, type, index) => {
    const newDetail = cloneDeep(detail);
    newDetail.tracks[type][index].trackingShowName = trackingShowName;
    setDetail(newDetail);
  };

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  };

  const renderContent = () => (
    <>
      <Card
        title="页面元素"
        extra={
          <Button
            htmlType="button"
            type={editable ? 'success' : 'primary'}
            onClick={handleEditOrSave}
            //disabled={ editable && isEqual(detail, oldDetail) }
          >
            {editable ? '保存' : '编辑'}
          </Button>
        }
        className={styles.sourceEditForm}
      >
        <Form {...formItemLayout} className={styles.boxForm}>
          <div className={styles.formTitle}>
            <Typography style={
              {
                height: '50px'
              }
            }>{detail.pageShowName || `页面ID：${detail.pageId}`}</Typography>
          </div>
          <div className={styles.formItems}>
            <Row style={
                  {
                    margin: '0 25px',
                    borderBottom: '1px solid #e8e8e8'
                  }
                }>
              <Col span={12}>
                <div
                  style={{
                    padding: '10px 0 0 10px'
                  }}
                >
                  页面ID：<br />{detail.pageId}
                </div>
              </Col>
              <Col span={12}>
                <Form.Item label="页面名称">
                  <Input
                    key={detail.pageId}
                    defaultValue={detail.pageShowName}
                    onChange={e => changePageShowName(e.target.value)}
                    disabled={!editable}
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
          <div className={styles.formTitle}>
            <Typography style={
              {
                height: '50px'
              }
            }>按钮</Typography>
            <p>备注说明：页面内部页面信息与服务进行交互的功能按钮</p>
          </div>
          <div className={styles.formItems}>
            {detail.tracks.buttons.map((track, index) => {
              return (
                <Row key={'btn' + index} style={
                  {
                    margin: '0 25px',
                    borderBottom: '1px solid #e8e8e8'
                  }
                }>
                  <Col span={12}>
                    <div
                      style={{
                        padding: '10px 0 0 10px',
                      }}
                    >
                      按钮ID：<br />{track.trackingId}
                    </div>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="按钮名称">
                      <Input
                        defaultValue={track.trackingShowName}
                        placeholder="请输入"
                        onChange={e => changeFormItem(e.target.value, 'buttons', index)}
                        disabled={!editable}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              );
            })}
          </div>
          <div className={styles.formTitle}>
            <Typography style={
              {
                height: '50px'
              }
            }>控件</Typography>
            <p>备注说明：页面内部对信息的录入操作的工具</p>
          </div>
          <div className={styles.formItems}>
            {detail.tracks.others.map((track, index) => {
              return (
                <Row key={'track' + index} style={
                  {
                    margin: '0 25px',
                    borderBottom: '1px solid #e8e8e8'
                  }
                }>
                  <Col span={12}>
                    <div
                      style={{
                        padding: '10px 0 0 10px',
                      }}
                    >
                      控件ID：<br />{track.trackingId}
                    </div>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="控件名称">
                      <Input
                        defaultValue={track.trackingShowName}
                        placeholder="请输入"
                        onChange={e => changeFormItem(e.target.value, 'others', index)}
                        disabled={!editable}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              );
            })}
          </div>
        </Form>
      </Card>
    </>
  );

  return (
    <PageHeaderWrapper
      title={false}
      content={
        <div style={{ marginTop: '10px' }} className="wrapSource">
          <Divider style={{ margin: '10px 0' }} />
          <Row className={styles.boxTab}>
            <Col span={8}>
              <Tabs
                defaultActiveKey={'driver'}
                onChange={handleTabChange}
                style={{
                  marginBottom: '-16px',
                }}
              >
                <TabPane tab="司机端" key="driver"></TabPane>
                <TabPane tab="哈勃后台" key="hubble" disabled></TabPane>
                <TabPane tab="租户后台" key="tenant" disabled></TabPane>
              </Tabs>
            </Col>
            <Col
              span={8}
              style={{
                textAlign: 'center',
                padding: '5px 0',
              }}
            >
              APP版本：<Select
                placeholder="请选择APP版本"
                onChange={handleVersionChange}
                value={version}
                defaultActiveFirstOption={false}
                style={{ width: 150 }}
              >
                {versionData.map((d, index) => (
                  <Option value={d} key={'d' + index}>
                    {d}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col
              span={8}
              style={{
                padding: '5px 10px 0',
              }}
            >
              <Search
                onSearch={handleSearch}
                onChange={handleKeywordChange}
                defaultValue={sName}
                value={sName}
                enterButton
                disabled={!version}
                placeholder="请输入页面ID/页面名称搜索"
              />
            </Col>
          </Row>
          {listData.length === 0 ? (
            <Empty description={version ? '暂无数据' : '请选择APP版本'} />
          ) : (
            <div className={styles.sectionContent}>
              <Card title="页面列表" className="pageList" style={{ width: 254 }}>
                <ul
                  className="ant-menu ant-menu-light ant-menu-root ant-menu-vertical"
                  direction="ltr"
                  role="menu"
                >
                  {listData.map((item, index) => (
                    <li
                      className={`ant-menu-submenu ant-menu-submenu-vertical${
                        detail && detail.pageId === item.pageId ? ' ant-menu-submenu-open' : ''
                      }`}
                      onClick={() => handleIdChange(item.pageId)}
                      key={'mn' + index}
                      role="menuitem"
                    >
                      <div
                        className="ant-menu-submenu-title"
                        role="button"
                        aria-expanded="false"
                        aria-haspopup="true"
                      >
                        <span>{item.pageShowName || `页面ID：${item.pageId}`}</span>
                        <i className="ant-menu-submenu-arrow"></i>
                      </div>
                    </li>
                  ))}
                </ul>
              </Card>
              {!detail ? (
                <Empty description={pageId ? '暂无数据' : '请在左侧列表中选择要编辑或查看的页面'} />
              ) : (
                renderContent()
              )}
            </div>
          )}
        </div>
      }
    ></PageHeaderWrapper>
  );
};

const PageComponentForm = Form.create()(PageComponent);

export default connect(({ sourceManagement, ssoAuthorityRole }) => ({
  pageQuery: sourceManagement.pageQuery,
  ssoAuthorityRole,
}))(PageComponentForm);
