:global {
  .wrapSource{
    // height: ~"calc(100vh - 204px)";
    .pageList {
      margin-right: 10px;
      overflow: hidden;
      .ant-card-head{
        position: relative;
      }
      .ant-card-body {
        padding: 0;
        .ant-menu-vertical{
          border-right: 0;
        }
      }
      .ant-menu-submenu{
        border-bottom: 1px solid #e8e8e8;
      }
    }
    .ant-btn-success{
      color: #fff;
      background-color: hsl(141, 53%, 53%);
      border-color: hsl(141, 53%, 53%);
      &:hover, &:focus {
        color: #fff;
        background-color: hsl(141, 53%, 53%);
        border-color: hsl(141, 53%, 53%);
      }
    }
  }
}

.boxTab {
  border: 1px solid #e8e8e8;
  margin-bottom: 10px;
  height: 45px;
}

.sectionContent {
  display: flex;
  //height: ~"calc(100vh - 204px)";
  .sourceEditForm {
    flex: 1;
    position: relative;
    .boxForm{
      flex-flow: column;
      display: flex;
      .formTitle{
        flex: 0.2;
        margin-top: 30px;
      }
      .formItems{
        max-height: 180px;
        overflow: hidden;
        overflow-y: auto;
        margin-bottom: 20px;
      }
    }
  }
}

.formTitle{
  padding-left: 16px;
  border-bottom: 1px solid #e8e8e8;
  position: relative;
  height: 40px;
  margin-bottom: 10px;
  flex: 1;
  &::before{
    width: 4px;
    height: 24px;
    background-color: #1890ff;
    content: '';
    display: inline-block;
    overflow: hidden;
    position: absolute;
    left: 0;
    top: 3px;
  }
  p, article{
    display: inline-block;
  }
  p{
    color: #999;
    font-size: 12px;
    margin: 0 0 0 20px;
  }
}

.formItems{
  flex: 1;
}