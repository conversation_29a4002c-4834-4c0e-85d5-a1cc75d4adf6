import request from '@/utils/request';

// APP版本查询
export async function getVersionData(pamas) {
  return request('/admin/v1/eventlog/buryConfig/queryVersionList', {
    method: 'POST'
  });
}

export async function getListData(pamas) {
  return request('/admin/v1/eventlog/buryConfig/queryListByVersion', {
    method: 'POST',
    data: pamas,
  });
}

export async function getDetail(pamas) {
  return request('/admin/v1/eventlog/buryConfig/queryInfoListByPageIdAndVersion', {
    method: 'POST',
    data: pamas,
  });
}

export async function updateTracking(pamas) {
  return request('/admin/v1/eventlog/buryConfig/updateTracking', {
    method: 'POST',
    data: pamas,
  });
}
