
// 二维码样式
.titleLeft {
  font-size: 14px;
  font-weight: bold;
}
.titleRight {
  margin: 0 10px;
  font-size: 12px;
  color: #979899;
}

.container {
  display: flex;
  align-items: center;
  border: 1px solid #DADADB;
  margin-top: 8px;
}
// 二维码图片部分
.containerPic{
  position: relative;
  display: inline-block;
  height: 140px;
  width: 140px;
  .overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.9); /* 设置蒙版的背景颜色和透明度 */
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 45px;
    font-size: 12px;
  }
}
// 文字部分
.containerText {
  display: inline-block;
  color: #606266;
  font-size: 14px;
}
