// 埋点数据验证
import React, { useEffect, useState } from 'react';
import { Tabs } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { connect } from 'dva';
import PSB from '@/styles/pagingSutionBottom.less';
import DataValidation from '@/pages/buriedDataVerification/component/DataValidation';

const BuriedDataVerification = props => {
  const { TabPane } = Tabs;
  const { platformList } = props;

  return (
    <PageHeaderWrapper
      title={false}
      className={PSB.pageHeaderWrapperStyle}
      content={
        <Tabs defaultActiveKey="3" className={PSB.tabBox}>
          {platformList?.map(item => (
            <TabPane tab={item.applicationDesc} key={item.applicationType}>
              <DataValidation applicationType={item.applicationType} />
            </TabPane>
          ))}
        </Tabs>
      }
    />
  );
};

export default connect(({ user }) => ({
  platformList: user.platformList,
}))(BuriedDataVerification);
