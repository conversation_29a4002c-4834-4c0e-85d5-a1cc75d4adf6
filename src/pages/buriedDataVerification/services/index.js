import request from '@/utils/request';
import axios from 'axios'

// 绑定账号接口
export async function testBind(params = {}) {
  return request('/admin/v1/eventlog/tracking/test/testBind', {
    method: 'post',
    data: params,
  });
}

// 生成二维码接口
export function generateQrcode(params = {}) {
  return axios('/admin/v1/eventlog/tracking/test/generate/qrcode', {
    method: 'POST',
    data: params,
    responseType: 'blob',
  });
}
// 确认绑定
export async function confirmBind(params = {}) {
  return request('/admin/v1/eventlog/tracking/test/confirmBind', {
    method: 'POST',
    data: params,
  })
}
