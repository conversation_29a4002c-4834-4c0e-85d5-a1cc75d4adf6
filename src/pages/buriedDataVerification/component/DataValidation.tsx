import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message, Steps } from 'antd';
import { getUser } from '@/utils/user';
import { testBind, generateQrcode, confirmBind } from '../services/index';
import styles from '../index.less'

const { Step } = Steps;

const DataValidationFun = ({ applicationType, form }) => {
  const { getFieldDecorator } = form
  const businessType = JSON.parse(sessionStorage.getItem('businessType'))
  const [currentStep, setCurrentStep] = useState(0)
  // 是否禁用URL地址输入框
  const [isDisabled, setIsDisabled] = useState(false)
  const [validateAddress, setValidateAddress] = useState('')
  // 接口轮训状态
  const [isPolling, setIsPolling] = useState(false);
  const [getQRcodeURL, setGetQRcodeURL] = useState('');
  // 二维码状态
  const [QRcodeType, setQRcodeType] = useState(0)


  // 生成需要的URL
  const generateUrl = () => {
    const { email } = getUser()
    const { webUrl } = form.getFieldsValue()
    const list = webUrl.split('?')
    let urlText = ''
    // url上有参数
    if (webUrl.includes('?') && list.length > 1) {
      urlText = `${list[0]}?__blmValidation=${email}&${list[1]}`
    } else if (webUrl.includes('#')) {
      const arr = webUrl.split('#')
      urlText = `${arr[0]}?__blmValidation=${email}#${arr[1]}`
    } else {
      urlText = `${list[0]}?__blmValidation=${email}`
    }
    setValidateAddress(urlText)
    return {
      inputUrl: webUrl, // 用户输入的URL
      outputUrl: urlText, // 经过加工后 带参数的URL
    }
  }

  // 确认URl获取是否正确 以便能够正确获取对应的域名
  const getHostname = () => {
    const { webUrl } = form.getFieldsValue()
    let parser = null
    try {
      parser = new URL(webUrl)
    } catch (e) {
      message.error('请确认URL是否正确')
    }
    return parser
  }
  // 确认绑定
  const confirmBindEvent = () => {
    const { email } = getUser()
    const parser = getHostname()
    if (!parser) { return }
    const params = {
      businessType,
      applicationType,
      debugKey: email || '', // 埋点平台的邮箱
      urlHost: parser.hostname || '', // 用户输入URL的域名
    }
    confirmBind(params).then(res => {
      if (res?.code === 1 && res.data && res.data.code) {
        // res.data.code: 1-绑定成功; 2-绑定中; 3-二维码过期
         setQRcodeType(res.data.code)
      } else {
        setQRcodeType(0)
        message.error(res.msg);
      }
    })
  }
  useEffect(() => {
    const { email } = getUser()
    // QRcodeType==1时代表绑定成功,进行页面跳转
    if (QRcodeType === 1) {
      const parser = getHostname()
      if (!parser) { return }
      window.location.href = `/dataVerification/buriedEventDataVerification?businessType=${businessType}&applicationType=${applicationType}&__blmValidation=${email}&urlHost=${parser.hostname}`
    }
  }, [QRcodeType])
  // 获取二维码图片
  const getQRcode = async() => {
    const { email } = getUser()
    const { outputUrl } = generateUrl()
    const params = {
      businessType,
      applicationType,
      platformUserId: email || '', // 埋点平台的邮箱
      testUrlPath: outputUrl, // 用户输入的URL带参数
    }
    // 获取二维码流文件并进行处理
    const res = await generateQrcode(params)
    const imgUrl = URL.createObjectURL(res.data)
    // 创建一个img
    const img = document.createElement('img');
    img.src = imgUrl;
    // 接口返回的是二维码流文件
    img.onload = function() {
      // URL地址输入框禁用 步骤条走到第二步
      setIsDisabled(true)
      setCurrentStep(1)
      // 展示二维码图片
      setGetQRcodeURL(imgUrl)
      // 启动轮询 查询二维码绑定状态
      setIsPolling(true)
    }
    // 接口返回错误信息(非流文件)
    img.onerror = function() {
      message.error('生成二维码失败,请确认输入的URL地址是否正确');
    }
  }
  // 验证输入框URL是否输入正确
  const checkCnData = (rule, value, callback) => {
    const reg = /^(https?):\/\/[^\s\/$.?#].[^\s]*$/i
    if (!value) {
      callback(new Error(Number(applicationType) === 4 ? '请输入页面URL' : '请输入Web URL'))
    } else if (!reg.test(value)) {
      callback(new Error('请输入正确的URL'));
    }
    callback();
  }
  // 生成验证地址
  function generateAddress() {
    form.validateFieldsAndScroll(err => {
      if (!err) {
        generateUrl()
        // 区分模块 管理后台
        if (Number(applicationType) === 3) {
          setIsDisabled(true)
          setCurrentStep(1)
        } else if (Number(applicationType) === 4) {
          // 企业微信 获取二维码 二维码获取成功进入到第二步
          getQRcode()
        }
      }
    })
  }
  // 重新验证
  function revalidation() {
    setIsDisabled(false)
    setCurrentStep(0)
    // 停止轮询
    setIsPolling(false)
  }
  const urlClickEve = () => {
    const { email } = getUser()
    const parser = getHostname()
    if (!parser) { return }
    const params = {
      businessType: Number(businessType),
      applicationType: Number(applicationType),
      userId: email || '',
      platformUserId: email || '',
      urlHost: parser.hostname || '', // 域名
    }
    testBind(params).then(res => {
      if (res?.code === 1) {
        window.open(validateAddress)
        window.location.href = `/dataVerification/buriedEventDataVerification?businessType=${businessType}&applicationType=${applicationType}&__blmValidation=${email}&urlHost=${parser.hostname}`
      } else {
        message.error(res.msg || '验证失败, 请重新验证')
      }
    })
  }

  useEffect(() => {
    // useEffect存在依赖项时: 依赖项更新的时候都会依次执行return内再执行return外的部分
    let pollingInterval
    // 初始化后立即调用一次
    if (isPolling) {
      // 设置轮询定时器，每隔一定时间调用一次fetchData方法
      pollingInterval = setInterval(() => {
        confirmBindEvent();
      }, 1000);
    }
    // 在组件卸载时清除定时器，以防止内存泄漏
    return () => {
      clearInterval(pollingInterval);
    };
  }, [isPolling])
  const jumpEve = () => {
    const { email } = getUser()
    // 验证绑定状态,
    if (QRcodeType === 1) {
      // 绑定成功 跳转到指定页面
      const parser = getHostname()
      if (!parser) { return }
      window.location.href = `/dataVerification/buriedEventDataVerification?businessType=${businessType}&applicationType=${applicationType}&__blmValidation=${email}&urlHost=${parser.hostname}`
    } else {
      message.warning('请扫二维码进行绑定,绑定后可跳转')
    }
  }

  // 点击刷新二维码
  const refreshQRCode = () => {
    getQRcode()
  }
  // 二维码
  const twoDimensionalCode = () => (
      <div style={{ marginTop: '20px', width: '1200px' }}>
        <span className={styles.titleLeft}>移动端请扫码验证:</span>
        <span className={styles.titleRight}>请扫描下方二维码</span>
        <div className={styles.container}>
          <div className={styles.containerPic}>
            <img src={getQRcodeURL} alt="" style={{ width: '100%', height: '100%' }}/>
            {
              QRcodeType === 3 ? (
                <div className={styles.overlay}>
                  二维码失效
                  <Button type="link" icon="redo" onClick={refreshQRCode}>点击刷新</Button>
                </div>
              ) : (null)
            }
          </div>

          <div className={styles.containerText}>
             <div style={{ margin: '12px 0 12px 18px' }}>
              <span>1.若扫码无反应, 请确认输入的URL地址是否正确</span>
             </div>
            <div style={{ margin: '12px 0 12px 18px' }}>
              <span>2.若扫码后页面没有自动跳转,</span>
              <Button
                type="link"
                onClick={jumpEve}
                style={{ padding: '0' }}
              >点击此处跳转</Button>
            </div>
          </div>
        </div>
      </div>
    )

  return (
    <>
      {
        Number(applicationType) === 4 || Number(applicationType) === 3 ? (
          <>
            <Steps direction="vertical" current={currentStep}>
              <Step title={Number(applicationType) === 4 ? '输入页面URL' : '输入WEB URL'} description={
                <Form layout="horizontal" labelAlign="right">
                  <Form.Item label={Number(applicationType) === 4 ? '页面URL' : 'Web URL'} style={{ marginBottom: '0' }}>
                    {getFieldDecorator('webUrl', {
                      rules: [
                        { required: true, validator: checkCnData, trigger: 'blur' },
                        { max: 500, message: '最多可输入500个字符' },
                      ],
                    })(<Input
                      allowClear
                      disabled={ isDisabled }
                      placeholder= "请输入页面URL地址"
                      style={{ width: '500px', border: '1px solid #ccc' }}
                    />)}
                  </Form.Item>
                  {
                    isDisabled ? (
                      <Button
                        onClick={revalidation}
                        style={{ margin: '10px 0' }}
                      >重新验证</Button>
                    ) : (
                      <Button
                        type="primary"
                        onClick={generateAddress}
                        style={{ margin: '10px 0' }}
                      >生成验证地址</Button>
                    )
                  }

                </Form>
              } />

              <Step title="开始验证" description={
                currentStep === 1 ? (
                  <>
                    <div className={styles.titleLeft} style={{ margin: '10px 0' }}>
                      {Number(applicationType) === 4 ? 'PC端请点击下方地址验证:' : '点击下方地址验证:' }
                    </div>
                    <a
                      onClick={urlClickEve}
                    >{validateAddress}</a>
                    {
                      isPolling ? twoDimensionalCode() : (null)
                    }
                  </>
                ) : (<></>)
              } />
            </Steps>
          </>
        ) : (
          <h3 style={{ height: '200px', padding: '80px 0', textAlign: 'center' }}>
            敬请期待
          </h3>
        )
      }
    </>
  )
}

const DataValidation = Form.create()(DataValidationFun);

export default DataValidation;
