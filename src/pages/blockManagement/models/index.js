/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-15 20:16:11
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-27 11:26:14
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/blockManagement/models/index.js
 */
import { notification, message } from 'antd';
import { getListData, getRefListData, getVersionList, add, addBlock, update, updateBlock, delItem, onLine, queryDetail, queryBlockDetail } from '../services';

const UserModel = {
  namespace: 'BlockManagement',
  state: {
    total: 0,
    listData: [],
    pageOptions: [],
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
    *fetchVersion({ payload }, { call, put }) {
        const response = yield call(getVersionList, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
          yield put({
            type: 'saveVersionList',
            payload: Array.isArray(data.items) ? data.items : [],
          });
        } else {
          notification.warn({
            message: '版本号请求失败',
            description: msg,
          });
        }
    },
    *getRefListData({ payload }, { call, put }) {
        const response = yield call(getRefListData, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
          yield put({
            type: 'saveDetailsData',
            payload: Array.isArray(data.items) ? data.items : [],
          });
          yield put({
            type: 'saveDetailTotal',
            payload: data ? data.totalNum : 0,
          });
        } else {
          notification.warn({
            message: '请求列表失败',
            description: msg,
          });
        }
    },
    *add({ payload, callback }, { call, put }) {
        const response = yield call(add, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件创建成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *addBlock({ payload, callback }, { call, put }) {
        const response = yield call(addBlock, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件创建成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *update({ payload, callback }, { call, put }) {
        const response = yield call(update, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件更新成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *updateBlock({ payload, callback }, { call, put }) {
        const response = yield call(updateBlock, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件更新成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *delItem({ payload, callback }, { call, put }) {
        const response = yield call(delItem, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件删除成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *online({ payload, callback }, { call, put }) {
        const response = yield call(onLine, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *queryDetail({ payload, callback }, { call, put }) {
        const response = yield call(queryDetail, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            payload.id 
                ? (callback(data[0]))
                : (callback(data))
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *queryBlockDetail({ payload, callback }, { call, put }) {
        const response = yield call(queryBlockDetail, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            callback(data[0])
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    }
  },
  reducers: {
    saveListData(state, action) {
      return { ...state, listData: action.payload }
    },
    saveTotal(state, action) {
      return { ...state, total: action.payload }
    },
    saveDetailsData(state, action) {
        return { ...state, detailListData: action.payload }
    },
    saveDetailTotal(state, action) {
        return { ...state, detailTotal: action.payload }
    },
    saveVersionList(state, action) {
        return { ...state, versionOptions: action.payload }
    },
    saveDetail(state, action){
        return { ...state, eventDetail: action.payload }
    },
    savePageOptions(state, action) {
        return { ...state, pageOptions: action.payload}
    }
  },
};
export default UserModel;