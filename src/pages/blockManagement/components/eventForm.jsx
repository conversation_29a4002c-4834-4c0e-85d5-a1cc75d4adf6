import React, { useState, useEffect, useImperativeHandle } from 'react'
import { Form, Input, Select } from 'antd'
import { EnumBusinessOptions } from '@/config'

class EventOption extends React.Component {

    constructor(props) {
        super(props)
        this.props = props
        this.state = {
            pageOptions: []
        }
    }

    render () {
        const { form: { getFieldDecorator }, category, action, versionOptions, detail, pageList } = this.props
        const pageOptions = pageList ? pageList : this.state.pageOptions
        return (
            <Form name='pageForm'>
                <Form.Item label="归属业务" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('businessName', {
                            rules: [
                                {required: true, message: '归属业务不能为空'},
                            ],
                            initialValue: detail ? [detail.businessName] : []
                        })(
                            <Select 
                                disabled={action === 'update'} 
                                placeholder="请选择归属业务"
                                onChange={(value) => {this.onBusinessChange(value)}}
                            >
                                {EnumBusinessOptions.map(d => (
                                    <Select.Option value={d.value} key={d.value}>
                                        {d.label}
                                    </Select.Option>
                                ))}
                            </Select>
                        )
                    }
                </Form.Item>
                {
                    category === 'block' && (<Form.Item label="归属页面" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                        {
                            getFieldDecorator('pageNameCn', {
                                rules: [
                                    {required: true, message: '归属页面不能为空'},
                                ],
                                initialValue: detail ? [detail.pageNameCn] : []
                            })(
                                <Select placeholder="请选择归属页面">
                                    {pageOptions.map(d => (
                                        <Select.Option value={d.pageNameCn} key={d.id}>
                                            {d.pageNameCn}
                                        </Select.Option>
                                    ))}
                                </Select>
                            )
                        }
                    </Form.Item>)
                }
                <Form.Item label="生效版本" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('clientVersion', {
                            rules: [
                                {required: true, message: '生效版本不能为空'},
                            ],
                            initialValue: detail ? [detail.clientVersion] : []
                        })(
                            <Select placeholder="请选择生效版本">
                                {versionOptions.map(d => (
                                    <Select.Option value={d.value} key={d.value}>
                                        {d.value}
                                    </Select.Option>
                                ))}
                            </Select>
                        )
                    }
                </Form.Item>
                {
                    category === 'page' ? (<Form.Item label="页面名称" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                        {
                            getFieldDecorator('pageNameCn', {
                                rules: [
                                    {required: true, message: '页面名称不能为空'},
                                    {validator: this.checkCnData, trigger: 'blur'}
                                ],
                                initialValue: detail ? [detail.pageNameCn] : []
                            })(
                                <Input maxLength={10} placeholder="请输入页面中文名称" disabled={action === 'update'} />
                            )
                        }
                    </Form.Item>) : (<Form.Item label="区块名称" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                        {
                            getFieldDecorator('blockName', {
                                rules: [
                                    {required: true, message: '区块名称不能为空'},
                                    {validator: this.checkCnData, trigger: 'blur'}
                                ],
                                initialValue: detail ? [detail.blockName] : []
                            })(
                                <Input maxLength={10} placeholder="请输入区块中文名称" disabled={ action === 'update' } />
                            )
                        }
                    </Form.Item>)
                }
            </Form>
        )
    }

    componentWillMount(){ 
    
    }

    componentWillReceiveProps(nextProps){
    }

    onBusinessChange(value) {
        const { form: { setFields, resetFields }, dispatch } = this.props
        resetFields(['pageNameCn'])
        // 查询页面下的关联区块
        dispatch({
            type: 'BlockManagement/queryDetail',
            payload: {
                businessName: value
            },
            callback: (res) => {
                this.setState({
                    pageOptions: res
                })
            }
        })
    }

    checkCnData(rule, value, callback){
        if (value) {
            if (/[^\u4E00-\u9FA5]/g.test(value)) {
              callback(new Error('只能输入中文!'));
            }
          }
          callback();
    }

    getFieldValue (type) {
        const { form } = this.props;
        let fieldsValue = '';
        if (type === 'page') {
            form.validateFields(async (err, values) => {
                if (err) return 
                fieldsValue = {
                    pageNameCn: !values.pageNameCn
                                ? undefined
                                : (Array.isArray(values.pageNameCn) ? values.pageNameCn[0] : values.pageNameCn),
                    businessName: !values.businessName
                                ? undefined
                                : (Array.isArray(values.businessName) ? values.businessName[0] : values.businessName),
                    clientVersion: !values.clientVersion
                                ? undefined
                                : (Array.isArray(values.clientVersion) ? values.clientVersion[0] : values.clientVersion),
                };
            });
        }
        if (type === 'block') {
            form.validateFields(async (err, values) => {
                if (err) return 
                fieldsValue = {
                    pageNameCn: !values.pageNameCn
                                ? undefined
                                : (Array.isArray(values.pageNameCn) ? values.pageNameCn[0] : values.pageNameCn),
                    blockName: !values.blockName
                                ? undefined
                                : (Array.isArray(values.blockName) ? values.blockName[0] : values.blockName),
                    businessName: !values.businessName
                                ? undefined
                                : (Array.isArray(values.businessName) ? values.businessName[0] : values.businessName),
                    clientVersion: !values.clientVersion
                                ? undefined
                                : (Array.isArray(values.clientVersion) ? values.clientVersion[0] : values.clientVersion),
                };
            });
        }
        return fieldsValue;
    }

    reset(){
        const { form: { resetFields } } = this.props
        resetFields()
    }

}

export default Form.create()(EventOption)
