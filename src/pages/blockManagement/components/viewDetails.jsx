/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-20 14:37:00
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-22 21:02:32
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/blockManagement/components/viewDetails.jsx
 */
import React, { useEffect, useReducer } from 'react'
import { Form, Input, Row, Col } from 'antd'
import styles from './viewDetails.less'
import { connect } from 'dva'
import { EnumBusinessOptions, EnumStatusOptions } from '@/config'

const ViewDetails = (props) => {

    const { dispatch, category, detail, eventDetail } = props

    return (
        <div className='details'>
            {
                category === 'page' 
                        ? (<Row>
                                <Col className='label' span={5}>页面名称</Col>
                                <Col className='content' span={19}>{detail?.pageNameCn}</Col>
                            </Row>)
                        : (<Row>
                                <Col className='label' span={5}>区块名称</Col>
                                <Col className='content' span={19}>{detail?.blockName}</Col>
                            </Row>)   
            }
            {
                category === 'block' && (<Row>
                    <Col className='label' span={5}>归属页面</Col>
                    <Col className='content' span={19}>{detail?.pageNameCn}</Col>
                </Row>)
            }
            <Row>
                <Col className='label' span={5}>归属业务</Col>
                <Col className='content' span={19}>{detail && EnumBusinessOptions.find(v => v.value == detail.businessName).label}</Col>
            </Row>
            <Row>
                <Col className='label' span={5}>生效版本</Col>
                <Col className='content' span={19}>{detail?.clientVersion}</Col>
            </Row>
            {/* <Row>
                <Col className='label' span={24}>取数来源</Col>
                <Col className='content' span={24}>
                取数来源取数来源取数来源取数来源取数来源取数来源取数来源取数来源
                </Col>
            </Row> */}
        </div>
        
    )

}

export default connect(({BlockManagement}) => ({
    eventDetail: BlockManagement.eventDetail
}))(ViewDetails)