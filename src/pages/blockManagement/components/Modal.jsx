/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-17 14:33:09
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-27 20:29:46
 * @Description: 新建页面 Model
 * @FilePath: /leopard-web-eventlog/src/pages/blockManagement/components/Modal.jsx
 */
import { Form, Modal, Input, Select, Button } from "antd";
import React, { useState, useEffect, useImperativeHandle, useRef } from 'react'
import EventForm from "./eventForm";
import ViewDetails from "./viewDetails";

const ModalComponent = React.forwardRef((props, ref) => {
    
    const [ visible, setVisible ] = useState(false)
    const [ category, setCategory ] = useState('')
    const [ action, setAction ] = useState('')
    const [ formRef, setFormRef ] = useState('')
    const [ title, setTitle ] = useState('')
    const [ detail, setDetail ] = useState('')
    const [ versionOptions, setVersionOptions ] = useState([])
    const [ pageOptions, setPageOptions ] = useState([])
    const { dispatch } = props;

    useImperativeHandle(ref, () => ({
        open: (params) => {
            const { category, action, title, data, versionOptions } = params
            setDetail('')
            setPageOptions('')
            formRef && formRef.reset()
            // Model类别 - 页面 / 区块
            setCategory(category)
            // 操作 - 新建 / 编辑 / 详情
            setAction(action)
            setTitle(title)
            versionOptions && setVersionOptions(versionOptions)
            data && setDetail(data)
            if (action === 'update' && category === 'block') {
                dispatch({
                    type: 'BlockManagement/queryDetail',
                    payload: {
                        businessName: data.businessName
                    },
                    callback: (res) => {
                        setPageOptions(res)
                        setVisible(true)
                    }
                })
            } else {
                setVisible(true)
            }
        },
        close: () => {
            setVisible(false)
        }
    }))

    /**
     * 保存
     */
    const onOk = () => {
        const fieldsValue = formRef.getFieldValue(category)
        if (fieldsValue) {
            action === 'add' ? props.onAdd(category, fieldsValue) : props.onUpdate(category, fieldsValue)
        }
    }

    /**
     * 取消
     */
    const onCancel = () => {
        setVisible(false)
    }

    return (
        <Modal 
            wrapClassName="event-modal"
            visible={visible}
            title={title}
            centered={true}
            onCancel={onCancel}
            width={400}
            footer={ action !== 'view' ? [
                <Button onClick={onCancel}>取消</Button>,
                <Button type="primary" onClick={onOk}>
                    保存  
                </Button>        
            ] : [<Button type="primary" onClick={onCancel}>
            确定  
            </Button>]}
    
        >
            {
                action !== 'view' && (<EventForm wrappedComponentRef={(form) => setFormRef(form)}  dispatch={props.dispatch} versionOptions={versionOptions} pageList={pageOptions} category={category} action={action} detail={detail}></EventForm>)
            }
            {
                action === 'view' && (<ViewDetails category={category} detail={detail}></ViewDetails>)
            }
        </Modal>
    )

})

export default ModalComponent
