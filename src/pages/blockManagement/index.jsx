/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-05 17:29:34
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-28 19:49:19
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/blockManagement/index.jsx
 */
import React, { useEffect, useRef, useState } from 'react'
import styles from './style.less'
import { Divider, Form, Select, Input, Button, Table, Pagination, Tooltip, Icon, Modal, Popconfirm } from 'antd'
import { connect } from 'dva'
import { authBtn } from '@/utils/utils';
import { getUser } from '@/utils/user';
import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout'
import { EnumBusinessOptions, EnumStatusOptions } from '@/config'
import ModalComponent from './components/Modal'
import viewDetails from './components/viewDetails';

const BlockManagement = ({ 
    dispatch,
    form,
    total = 0,
    listData = [],
    versionOptions = [],
    loading,
    ssoAuthorityRole: {
        data: { curResources = [] }
    }
}) => {
    const FormItem = Form.Item;
    const { Option } = Select;
    const [ appData, setAppData ] = useState([])
    const [ pfData, setPfData ] = useState([])
    const [ currentPage, setCurrentPage ] = useState(1);
    const [ pageSize, setPageSize ] = useState(10);
    const [ authList, setAuthList ] = useState([]);
    const [ onLineVisible, setOnLineVisible ] = useState(false)
    const [ currentId, setCurrentId ] = useState('')
    const [ category, setCategory ] = useState('')
    const [ currentRow, setCurrentRow ] = useState('')
    const modelComponent = useRef(null)
    const { name: userName } = getUser()

    const formLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    };

    const { getFieldDecorator, resetFields } = form

    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align: 'center',
            render: (_, row, index) => {
                return (index+1) + ((currentPage-1) * pageSize)
            }
        },
        {
            title: '页面名称',
            dataIndex: 'pageNameCn',
            key: 'pageNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面ID',
            dataIndex: 'pageNameEn',
            key: 'pageNameEn',
            align: 'center',
            render: (content, row) => {
                if (!content) return '--'
                if (content.length <= 30) return content;
                return (
                    <Tooltip placement="top" title={content}>
                        {`${content.slice(0, 30)}...`}
                    </Tooltip>
                )
            }   
        },
        {
            title: '归属业务',
            dataIndex: 'businessName',
            key: 'businessName',
            align: 'center',
            render: (_, row) => {
                const value = EnumBusinessOptions.find(v => v.value == _).label
                return value
            }
        },
        {
            title: '生效版本',
            dataIndex: 'clientVersion',
            key: 'clientVersion',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '创建人',
            dataIndex: 'createUser',
            key: 'createUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            } 
        },
        {
            title: '编辑人',
            dataIndex: 'updateUser',
            key: 'updateUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '最后编辑时间',
            dataIndex: 'gmtModified',
            key: 'gmtModified',
            align: 'center',
            render: (_, row) => {
                return moment(_).format('YYYY-MM-DD HH:mm')
            }
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            align: 'center',
            render: (_, row) => {
                const value = EnumStatusOptions.find(v => v.value == _).label
                return (
                    <div>
                        {
                            _ === 0 ? (
                                <Button
                                    type='link'
                                    onClick={()=>{
                                        setCurrentId(row.id)
                                        setCategory('page')
                                        setCurrentRow(row)
                                        setOnLineVisible(true)
                                    }}
                                >
                                    开发中
                                </Button>
                            ) : <span style={{'color': 'chartreuse'}}>已上线</span>
                        }
                    </div>
                )
            }
        },
        {
            title: '操作',
            dataIndex: 'option',
            key: 'option',
            align: 'center',
            render: (_, row) => {
                return (
                    <div>
                        {(isAuthBtn('update') && row.state === 0) ? (
                            <Button 
                                type="link"
                                onClick={() => {
                                    setCurrentId(row.id)
                                    modelComponent.current && modelComponent.current.open({
                                        category: 'page',
                                        action: 'update',
                                        title: '编辑页面',
                                        data: row,
                                        versionOptions: versionOptions
                                    })
                                }}
                            >
                                编辑
                            </Button>
                        ) : null}
                        {(isAuthBtn('del') && row.state === 0) ? (
                            <Popconfirm
                                title="确定删除该页面么?"
                                onConfirm={() => { onDel('page', row) }}
                                onCancel={()=>{}}
                                okText="是"
                                cancelText="否"
                            >
                                <Button type="link">
                                    删除
                                </Button>
                            </Popconfirm>
                        ) : null}
                        {isAuthBtn('view') ? (
                            <Button 
                                type="link"
                                onClick={() => {
                                    viewDetail({
                                        category: 'page',
                                        action: 'view',
                                        title: '页面取数详情',
                                        id: row.id
                                    })
                                }}
                            >
                                查看详情
                            </Button>
                        ) : null}
                    </div>
                  );
            },
        },
    ]
    
    const showTotal = Total => `共${Total}条`;

    /**
     * 二级表格渲染
     * @param {*} row 
     * @param {*} b 
     * @param {*} c 
     * @param {*} d 
     * @returns 
     */
    const expandedRowRender = (row, b, c, d) => {
        if (!row.blockDetails) {
            const currentEle = document.getElementsByClassName('ant-table-expanded-row')
            Array.from(currentEle).forEach( v => {
                const key = v.getAttribute('data-row-key')
                key === `${row.id}-extra-row` && (v.style.display = 'none')
            })
        }
        const data = listData.find(v => v.id === row.id).blockDetails
        const columns = [
            {
                title: '序号',
                dataIndex: 'index',
                key: 'index',
                align: 'center',
                render: (_, row, index) => {
                    return (index+1)
                }
            },
            {
                title: '区块名称',
                dataIndex: 'blockName',
                key: 'blockName',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '归属页面',
                dataIndex: 'pageNameCn',
                key: 'pageNameCn',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '归属业务',
                dataIndex: 'businessName',
                key: 'businessName',
                align: 'center',
                render: (_, row) => {
                    const value = EnumBusinessOptions.find(v => v.value == _).label
                    return value
                }   
            },
            {
                title: '生效版本',
                dataIndex: 'clientVersion',
                key: 'clientVersion',
                align: 'center',
                render: (_, row) => {
                    return _
                }
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '编辑人',
                dataIndex: 'updateUser',
                key: 'updateUser',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '最后编辑时间',
                dataIndex: 'gmtModified',
                key: 'gmtModified',
                align: 'center',
                render: (_, row) => {
                    return moment(_).format('YYYY-MM-DD HH:mm')
                }
            },
            {
                title: '状态',
                dataIndex: 'state',
                key: 'state',
                align: 'center',
                render: (_, row) => {
                    const value = EnumStatusOptions.find(v => v.value == _).label
                    return (
                        <div>
                            {
                                _ === 0 ? (
                                    <Button
                                        type='link'
                                        onClick={()=>{
                                            setCurrentId(row.id)
                                            setCategory('block')
                                            setCurrentRow(row)
                                            setOnLineVisible(true)
                                        }}
                                    >
                                        开发中
                                    </Button>
                                ) : <span style={{'color': 'chartreuse'}}>已上线</span>
                            }
                        </div>
                    )
                }
            },
            {
                title: '操作',
                dataIndex: 'option',
                key: 'option',
                align: 'center',
                render: (_, row) => {
                    return (
                        <div>
                          {(isAuthBtn('update') && row.state === 0) ? (
                                <Button 
                                    type="link"
                                    onClick={() => {
                                        setCurrentId(row.id)
                                        modelComponent.current && modelComponent.current.open({
                                            category: 'block',
                                            action: 'update', 
                                            title: '编辑区块', 
                                            data: row,
                                            versionOptions: versionOptions,
                                        }) 
                                    }}
                                >
                                    编辑
                                </Button>
                          ) : null}
                          {(isAuthBtn('del') && row.state === 0) ? (
                                <Popconfirm
                                    title="确定删除该区块么?"
                                    onConfirm={() => { onDel('block', row) }}
                                    onCancel={()=>{}}
                                    okText="是"
                                    cancelText="否"
                                >
                                    <Button type="link">
                                        删除
                                    </Button>
                                </Popconfirm>
                          ) : null}
                          {isAuthBtn('view') ? (
                                <Button 
                                    type="link"
                                    onClick={() => {
                                        viewDetail({ 
                                            category: 'block', 
                                            action: 'view', 
                                            title: '区块取数详情', 
                                            id: row.id 
                                        })
                                    }}
                                >
                                    查看详情
                                </Button>
                          ) : null}
                        </div>
                      );
                },
            },
        ];
        return <Table 
                    columns={columns} 
                    style={{ margin: '10px' }}
                    bordered
                    rowKey="id" 
                    dataSource={data} 
                    pagination={false} 
                />;

    };

    const onExpand = (a, b) => {
        console.log(a, b)
    }

    /**
     * 列表查询
     * @param {*} value 
     */
    const queryData = value => {
        form.validateFields(async (err, values) => {
            // 页面中文名称、区块中文名称、归属业务、生效版本、状态
            const parmas = {
                pageNameCn:
                    !values.pageNameCn || Array.isArray(values.pageNameCn)
                        ? undefined
                        : values.pageNameCn,
                pageNameEn:
                    !values.pageNameEn || Array.isArray(values.pageNameEn)
                        ? undefined
                        : values.pageNameEn,
                blockName: !values.blockName || Array.isArray(values.blockName) ? undefined : values.blockName,
                businessName:
                    values.businessName === '' || Array.isArray(values.businessName) ? undefined : values.businessName,
                clientVersion:
                    values.clientVersion === '' || Array.isArray(values.clientVersion)
                        ? undefined
                        : values.clientVersion,
                state:
                    values.state === '' || Array.isArray(values.state) ? undefined : values.state,
                pageNum: value.pageNum,
                pageSize: value.pageSize,
            };
            dispatch({
                type: 'BlockManagement/fetchlistData',
                payload: parmas,
            });
        });
    };

    /**
     * 新建
     * @param {object} values 
     */
    const onAdd = (category, values) => {
        const type = category === 'page' ? 'BlockManagement/add' : 'BlockManagement/addBlock'
        dispatch({
            type: type,
            payload: Object.assign(values, { createUser: userName }),
            callback: () => {
                modelComponent.current && modelComponent.current.close()
                setCurrentPage(1)
                const query = {
                  pageNum: 1,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 编辑
     * @param {object} values 
     */
    const onUpdate = (category, values) => {
        const params = {
            id: currentId,
            ...values,
            updateUser: userName,
            operationType: 3
        }
        const type = category === 'page' ? 'BlockManagement/update' : 'BlockManagement/updateBlock'
        dispatch({
            type: type,
            payload: params,
            callback: () => {
                modelComponent.current && modelComponent.current.close()
                const query = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    const viewDetail = (params) => {
        const type = params.category === 'page' ? 'BlockManagement/queryDetail' : 'BlockManagement/queryBlockDetail'
        dispatch({
            type: type,
            payload: { id: params.id },
            callback: (res) => {
                modelComponent.current && modelComponent.current.open({
                    ...params,
                    data: res
                })
            }
        })
    }

    /**
     * 删除
     */
    const onDel = (category, row) => {
        const type = category === 'page' ? 'BlockManagement/update' : 'BlockManagement/updateBlock' 
        dispatch({
            type: type,
            payload: { 
                id: row.id,
                updateUser: userName,
                operationType: 2,
            },
            callback: () => {
                const query = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 埋点上线
     */
    const onLine = () => {
        const type = category === 'page' ? 'BlockManagement/update' : 'BlockManagement/updateBlock' 
        dispatch({
            type: type,
            payload: {
                id: currentRow.id,
                businessName: currentRow.businessName,
                clientVersion: currentRow.clientVersion,
                state: 1,
                updateUser: userName,
                operationType: 1
            },
            callback: () => {
                setOnLineVisible(false)
                const query = {
                    pageNum: currentPage,
                    pageSize: pageSize,
                };
                queryData(query);
            }
        })
    }

    /**
     * 切换页码
     * @param {*} page 
     * @param {*} pageSize 
     */
    const handlePageChange = (page, pageSize) => {
        setCurrentPage(page);
        queryData({ pageNum: page, pageSize });
    };

    /**
     * 切换 pageSize
     * @param {*} current 
     * @param {*} pageSize 
     */
    const handlePageSizeChange = (current, pageSize) => {
        setPageSize(pageSize)
        queryData({ pageNum: currentPage, pageSize });
    };

    /**
     * 按钮权限确认
     * @param {*} key 
     * @returns 
     */
     const isAuthBtn = key => {
        return authList.includes(key);
    };

    useEffect(() => {
        dispatch({
            type: 'BlockManagement/fetchVersion',
            payload: {
                pageSize: 1000,
                isDeleted: 0
            }
        })
    }, [])

    useEffect(() => {
        const params = {
          pageNum: 1,
          pageSize: 10,
        };
        queryData(params);
    }, []);

    useEffect(() => {
        if (Array.isArray(curResources)) {
          curResources.map(c => {
            if (c.resourceKey === 'documents') {
              const authBtnList = authBtn(c.subList, 'blockManagement');
              setAuthList(authBtnList);
            }
          });
        }
    }, [curResources]);

    return (
        <PageHeaderWrapper
            title={false}
            content={
                <div className='wrapper'>
                    <Divider className='divider'></Divider>
                    <Form layout='inline' {...formLayout}>
                        <FormItem label="页面名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameCn', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Input style={{ width: '160px' }} placeholder="请输入页面中文名称" />
                            )}
                        </FormItem>
                        <FormItem label="区块名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('blockName', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入区块中文名称" />)}
                        </FormItem>
                        <FormItem label="页面ID" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameEn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入页面ID" />)}
                        </FormItem>
                        <br />
                        <FormItem label="归属业务" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('businessName', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择归属业务" style={{ minWidth: '160px' }} allowClear>
                                    {EnumBusinessOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.label}
                                        </Option>
                                    ))}
                                </Select>
                            )}
                        </FormItem>
                        <FormItem label="生效版本" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('clientVersion', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择生效版本" style={{ minWidth: '160px' }} allowClear>
                                    {versionOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.value}
                                        </Option>
                                    ))}
                                </Select>
                            )}
                        </FormItem>
                        <FormItem label="&nbsp;&nbsp;&nbsp;&nbsp;状态" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('state', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择状态" style={{ minWidth: '160px' }} allowClear>
                                    {EnumStatusOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.label}
                                        </Option>
                                    ))}
                                </Select>
                            )}
                        </FormItem>
                        <FormItem style={{marginLeft: '40px'}}>
                            <Button
                                type="primary"
                                size="default"
                                onClick={() => {
                                    const params = {
                                        pageNum: 1,
                                        pageSize: pageSize,
                                    };
                                    setCurrentPage(1)
                                    queryData(params);
                                }}
                                loading={loading}
                            >
                                查询
                            </Button>
                        </FormItem>
                        <FormItem>
                            <Button
                                size="default"
                                onClick={() => {
                                    resetFields()
                                }}
                            >
                                重置
                            </Button>
                        </FormItem>
                    </Form>
                    <Divider className='divider'></Divider>
                    {
                        isAuthBtn('addPage') && (<Button 
                            type="primary"
                            onClick={() => {
                                modelComponent.current && modelComponent.current.open({ category: 'page', action: 'add', title: '新建页面', versionOptions: versionOptions })
                            }}
                        >新建页面</Button>)
                    }
                    {
                        isAuthBtn('addBlock') && (<Button 
                            style={{marginLeft: '20px'}} 
                            type="primary"
                            onClick={() => {
                                modelComponent.current && modelComponent.current.open({ category: 'block', action: 'add', title: '新建区块', versionOptions: versionOptions })
                            }}
                        >新建区块</Button>)
                    }
                    <Table
                        columns={columns}
                        expandedRowRender={(record, index, indent, expanded)=>expandedRowRender(record, index, indent, expanded)}
                        expandIcon={(props)=>{
                            if(props.record.blockDetails?.length > 0){
                                if (props.expanded) {
                                    return <a style={{ color: 'rgba(0, 0, 0, 0.65)' }} onClick={e => {
                                        props.onExpand(props.record, e);
                                    }}><Icon type="up" /></a>
                                } else {
                                    return <a style={{ color: 'rgba(0, 0, 0, 0.65)' }} onClick={e => {
                                        props.onExpand(props.record, e);
                                    }}><Icon type="down" /></a>
                                }
                            }else{
                                return <span style={{marginRight:8 }}></span>
                            }
                            }
                        }
                        defaultExpandAllRows={false}
                        onExpand={(expanded, record)=> onExpand(expanded, record)}
                        dataSource={listData}
                        style={{ marginTop: '20px' }}
                        bordered
                        rowKey="id"
                        loading={loading}
                        scroll={{ x: true, scrollToFirstRowOnChange: true }}
                        pagination={false}
                    />
                    <Pagination
                        style={{ float: 'right', marginTop: '20px' }}
                        showQuickJumper
                        showSizeChanger
                        disabled={total === 0}
                        showTotal={showTotal}
                        current={currentPage}
                        onChange={handlePageChange}
                        onShowSizeChange={handlePageSizeChange}
                        defaultPageSize={10}
                        total={total}
                        pageSizeOptions={['10', '20', '50', '100']}
                    />
                    <ModalComponent 
                        ref={modelComponent}
                        dispatch={dispatch}
                        onAdd={onAdd}
                        onUpdate={onUpdate}
                    ></ModalComponent>
                    <Modal
                        wrapClassName="event-modal"
                        width={400}
                        centered={true}
                        visible={onLineVisible}
                        onOk={onLine}
                        onCancel={()=>{ setOnLineVisible(false) }}
                    >
                        <h2 style={{'textAlign': 'center'}}>确定上线吗？</h2>
                        <div style={{'textAlign': 'center'}}>(功能上线后不可撤回，请谨慎操作)</div>
                    </Modal>    
                </div>
            }
        >
        </PageHeaderWrapper>
    )

}

const BlockManagementForm = Form.create()(BlockManagement);

export default connect(({ BlockManagement, loading, ssoAuthorityRole }) => ({
    listData: BlockManagement.listData,
    total: BlockManagement.total,
    versionOptions: BlockManagement.versionOptions,
    loading: loading.effects['BlockManagement/fetchlistData'],
    ssoAuthorityRole,
}))(BlockManagementForm);