/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-15 20:18:12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-22 16:22:27
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/blockManagement/services/index.js
 */
import request from '@/utils/request';

// 埋点码表同步
export async function getListData(pamas) {
  return request('/admin/v1/eventlog/burypoint/pbb/page/query', {
    method: 'POST',
    data: pamas,
  });
}

/**
 * 获取全埋点/手工埋点列表数据
 * @param {*} params 
 * @returns 
 */
export async function getRefListData(params) {
    return request('/admin/v1/eventlog/burypoint/event/queryMappingEventDetails', {
        method: 'POST',
        data: params,
    });
}

/**
 * 获取版本号数据
 * @param {*} params 
 * @returns 
 */
export async function getVersionList(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/version/queryDetail', {
        method: 'POST',
        data: params,
    });
}

/**
 * 新建页面
 * @param {*} params 
 * @returns 
 */
 export async function add(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/page/add', {
        method: 'POST',
        data: params,
    });
}

/**
 * 新建区块
 * @param {*} params 
 * @returns 
 */
 export async function addBlock(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/block/add', {
        method: 'POST',
        data: params,
    });
}

/**
 * 编辑页面
 * @param {*} params 
 * @returns 
 */
export async function update(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/page/update', {
        method: 'POST',
        data: params,
    });
}

/**
 * 编辑区块
 * @param {*} params 
 * @returns 
 */
 export async function updateBlock(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/block/update', {
        method: 'POST',
        data: params,
    });
}

/**
 * 删除条目
 */
export async function delItem(params) {
    return request('/admin/v1/eventlog/burypoint/event/del', {
        method: 'POST',
        data: params
    })
}

/**
 * 埋点上线
 * @param {*} params 
 * @returns 
 */
 export async function onLine(params) {
    return request('/admin/v1/eventlog/burypoint/event/addMapping', {
        method: 'POST',
        data: params
    })
}

/**
 * 获取页面详情
 * @param {*} params 
 * @returns 
 */
 export async function queryDetail(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/page/queryDetail', {
        method: 'POST',
        data: params
    })
}

/**
 * 获取区块详情
 * @param {*} params 
 * @returns 
 */
 export async function queryBlockDetail(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/block/queryDetail', {
        method: 'POST',
        data: params
    })
}