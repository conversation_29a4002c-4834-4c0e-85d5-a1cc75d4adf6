/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-10 11:49:12
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-12 16:22:07
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/allStopwatch/services/index.js
 */
import request from '@/utils/request';

// 埋点码表同步
export async function getListData(pamas) {
  return request('/admin/v1/eventlog/burypoint/event/queryEventDetails', {
    method: 'POST',
    data: pamas,
  });
}

// 枚举值码表同步
export async function getEnumParamData(params) {
  return request('/admin/v1/eventlog/sync/list/enumparam', {
    params,
  });
}

// 获取用户应用接口
export async function getUserTerminal(params) {
  return request('/admin/v1/eventlog/auth/terminal/list/get', {
    params,
  });
}
