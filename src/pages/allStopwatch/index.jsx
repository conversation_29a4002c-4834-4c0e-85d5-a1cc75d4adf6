/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-05 17:29:34
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-18 20:20:30
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/allStopwatch/index.jsx
 */
import React, { useEffect, useState } from 'react'
import styles from './style.less'
import { Divider, Form, Select, Input, Button, Table, Pagination, Tooltip } from 'antd'
import { connect } from 'dva'
import { PageHeaderWrapper } from '@ant-design/pro-layout'
import { EnumApplicationOptions, EnmuClientTypeOptions } from '@/config'

const AllStopComponent = ({ 
    dispatch,
    form,
    total = 0,
    listData = [],
    loading,
    ssoAuthorityRole: {
        data: { curResources = [] }
    }
}) => {
    const FormItem = Form.Item;
    const { Option } = Select;
    const [ appData, setAppData ] = useState([])
    const [ pfData, setPfData ] = useState([])
    const [currentPage, setCurrentPage] = useState(1);
    const [ pageSize, setPageSize ] = useState(10);

    const formLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    };

    const { getFieldDecorator, resetFields } = form

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            align: 'center',
        },
        {
            title: '埋点中文名称',
            dataIndex: 'eventNameCn',
            key: 'eventNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '埋点英文名称',
            dataIndex: 'eventNameEn',
            key: 'eventNameEn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面中文名称',
            dataIndex: 'pageNameCn',
            key: 'pageNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面英文名称',
            dataIndex: 'pageNameEn',
            key: 'pageNameEn',
            align: 'center',
            render: (content, row) => {
                if (!content) return '--'
                if (content.length <= 30) return content;
                return (
                    <Tooltip placement="top" title={content}>
                        {`${content.slice(0, 30)}...`}
                    </Tooltip>
                )
            } 
        },
        {
            title: '应用',
            dataIndex: 'application',
            key: 'application',
            align: 'center',
            render: (_, row) => {
                const value = EnumApplicationOptions.find(v => v.value == _).label
                return value
            }
        },
        {
            title: '客户端类型',
            dataIndex: 'appSystem',
            key: 'appSystem',
            align: 'center',
            render: (_, row) => {
                const value = EnmuClientTypeOptions.find(v => v.value == _).label
                return value
            }
        },
    ]
    const showTotal = Total => `共${Total}条`;

    const queryData = value => {
        form.validateFields(async (err, values) => {
          const parmas = {
            appSystem:
                !values.appSystem || Array.isArray(values.appSystem)
                    ? undefined
                    : Number(values.appSystem),
            application: !values.application || Array.isArray(values.application) ? undefined : Number(values.application),
            eventNameCn:
                values.eventNameCn === '' || Array.isArray(values.eventNameCn) ? undefined : values.eventNameCn,
            eventNameEn:
                values.eventNameEn === '' || Array.isArray(values.eventNameEn)
                    ? undefined
                    : values.eventNameEn,
            pageNameCn:
                values.pageNameCn === '' || Array.isArray(values.pageNameCn) ? undefined : values.pageNameCn,
            pageNameEn: 
                values.pageNameEn === '' || Array.isArray(values.pageNameEn) ? undefined : values.pageNameEn,
            pageNum: value.pageNum,
            pageSize: value.pageSize,
          };
          dispatch({
            type: 'allstopwatch/fetchlistData',
            payload: parmas,
          });
        });
    };

    /**
     * 切换页码
     * @param {*} page 
     * @param {*} pageSize 
     */
    const handlePageChange = (page, pageSize) => {
        setCurrentPage(page);
        queryData({ pageNum: page, pageSize });
    };

    /**
     * 切换 pageSize
     * @param {*} current 
     * @param {*} pageSize 
     */
    const handlePageSizeChange = (current, pageSize) => {
        setPageSize(pageSize)
        queryData({ pageNum: currentPage, pageSize });
    };

    useEffect(() => {
        const params = {
          pageNum: 1,
          pageSize: 10,
        };
        queryData(params);
    }, []);

    return (
        <PageHeaderWrapper
            title={false}
            content={
                <div className='wrapper'>
                    <Divider className='divider'></Divider>
                    <Form layout='inline' {...formLayout}>
                        <FormItem label="应用">
                            {getFieldDecorator('application', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择应用" style={{ minWidth: '160px' }} allowClear>
                                    {EnumApplicationOptions.map(a => (
                                        <Option value={a.value} key={a.value}>
                                            {a.label}
                                        </Option>
                                    ))}
                                </Select>,
                            )}
                        </FormItem>
                        <FormItem label="客户端类型" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('appSystem', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择客户端类型" style={{ minWidth: '160px' }} allowClear>
                                    {EnmuClientTypeOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.label}
                                        </Option>
                                    ))}
                                </Select>,
                            )}
                        </FormItem>
                        <FormItem label="埋点中文名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('eventNameCn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入埋点中文名称" />)}
                        </FormItem>
                        <br />
                        <FormItem label="埋点英文名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('eventNameEn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入埋点英文名称" />)}
                        </FormItem>
                        <FormItem label="页面中文名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameCn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入页面中文名称" />)}
                        </FormItem>
                        <FormItem label="页面英文名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameEn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入页面英文名称" />)}
                        </FormItem>
                        <FormItem>
                            <Button
                                type="primary"
                                size="default"
                                onClick={() => {
                                    const params = {
                                        pageNum: currentPage,
                                        pageSize: pageSize,
                                    };
                                    queryData(params);
                                }}
                                loading={loading}
                            >
                                查询
                            </Button>
                        </FormItem>
                        <FormItem>
                            <Button
                                size="default"
                                onClick={() => {
                                    resetFields()
                                }}
                            >
                                重置
                            </Button>
                        </FormItem>
                    </Form>
                    <Table
                        columns={columns}
                        dataSource={listData}
                        style={{ marginTop: '20px' }}
                        bordered
                        rowKey="id"
                        loading={loading}
                        scroll={{ x: true, scrollToFirstRowOnChange: true }}
                        pagination={false}
                    />
                    <Pagination
                        style={{ float: 'right', marginTop: '20px' }}
                        showQuickJumper
                        showSizeChanger
                        disabled={total === 0}
                        showTotal={showTotal}
                        current={currentPage}
                        onChange={handlePageChange}
                        onShowSizeChange={handlePageSizeChange}
                        defaultPageSize={10}
                        total={total}
                        pageSizeOptions={['10', '20', '50', '100']}
                    />
                </div>
            }
        >
        </PageHeaderWrapper>
    )

}

const AllStopComponentForm = Form.create()(AllStopComponent);

export default connect(({ allstopwatch, loading, ssoAuthorityRole }) => ({
    listData: allstopwatch.listData,
    total: allstopwatch.total,
    loading: loading.effects['allstopwatch/fetchlistData'],
    ssoAuthorityRole,
}))(AllStopComponentForm);