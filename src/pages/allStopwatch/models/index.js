/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-10 11:47:28
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-10 11:48:30
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/allStopwatch/models/index.js
 */
import { notification, message } from 'antd';
import { getListData } from '../services';

const UserModel = {
  namespace: 'allstopwatch',
  state: {
    total: 0,
    listData: [],
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
  },
  reducers: {
    saveListData(state, action) {
      return { ...state, listData: action.payload }
    },
    saveTotal(state, action) {
      return { ...state, total: action.payload }
    },
  },
};
export default UserModel;
