import request from '@/utils/request';

// 获取人群信息/admin/v1/tracking/event/queryUserInfo
export async function queryUserInfo(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/queryUserInfo', {
    method: 'post',
    data: params,
  });
}

// 页面列表 /admin/v1/eventlog/tracking/event/page/query/details/page
export async function pageDetailsList(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/page/query/details/page', {
    method: 'post',
    data: params,
  });
}

// 新增页面 /admin/v1/eventlog/tracking/event/page/add
export async function pageAddApi(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/page/add', {
    method: 'post',
    data: params,
  });
}
// 编辑页面 /admin/v1/eventlog/tracking/event/page/update
export async function pageEditApi(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/page/update', {
    method: 'post',
    data: params,
  });
}
// 页面详情 /admin/v1/eventlog/tracking/event/page/query/detail
export async function queryDetail(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/page/query/detail', {
    method: 'post',
    data: params,
  });
}


// 校验页面名称 /admin/v1/eventlog/tracking/event/page/exists/name
export async function existsName(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/page/exists/name', {
    method: 'post',
    data: params,
  });
}
// 上传图片公共接口
export async function pageUpload(params = {}) {
  return request('/admin/v1/eventlog/upload', {
    method: 'post',
    data: params,
  });
}
// 上传图片 /admin/v1/eventlog/getPresignedPubUrl
export async function getPresignedPubUrl(params = {}) {
  return request('/admin/v1/eventlog/getPresignedPubUrl', {
    method: 'post',
    data: params,
  });
}
