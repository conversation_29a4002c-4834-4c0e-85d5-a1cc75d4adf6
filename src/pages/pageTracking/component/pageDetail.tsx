import React, { useEffect, useState } from 'react';
import { Form, Input } from '@blmcp/ui';
import { Button, Modal, Select, message, Table, Tooltip, Tag } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import UploadPicture from '@/components/UploadImg/uploadPicture';
import moment from 'moment';
import { queryUserInfo, pageAddApi, queryDetail, pageEditApi } from '../services';
import { getUrlKey } from '@/utils/utils';
import BizPlatTip from '@/components/BizPlatTip';

const PageDetailMeth = () => {
  const [form] = Form.useForm();
  // const { getFieldDecorator } = form
  const { TextArea } = Input;
  const formItemLayout = { labelCol: { span: 4 }, wrapperCol: { span: 18 } };
  const { Option } = Select;
  const { pageStatus, businessType, applicationType, pageId = '' } = getUrlKey(
    window.location.href,
  );
  // 开发者信息
  const [devUserList, setDevUserList] = useState({ data: [], fetching: false });
  // 测试者信息
  const [testUserList, setTestUserList] = useState({ data: [], fetching: false });
  const [showModal, setShowModal] = useState(false);

  const triggerConditionEnum = [
    { key: 'pv', value: '页面浏览事件' },
    { key: 'click', value: '模块点击事件' },
    { key: 'exposure', value: '模块曝光事件' },
    { key: 'popupShow', value: '弹窗显示事件' },
  ];
  const columns = [
    {
      title: '事件名称',
      dataIndex: 'eventName',
      key: 'eventName',
      width: 120,
      render: (text, record) => (
        <a
          onClick={() =>
            window.open(
              `/buryingPointNew/eventDetail?businessType=${record.businessType}&applicationType=${record.applicationType}&pageStatus=detail&&eventId=${record.id}`,
            )
          }
        >
          {record.eventName}
        </a>
      ),
    },
    {
      title: '事件标识(eventId)',
      dataIndex: 'eventUniqueId',
      key: 'eventUniqueId',
      render: (text, record) => (
        <div>
          <span>{record.eventUniqueId}</span>
          <Button type="link" onClick={() => copyPageId2(record.eventUniqueId)}>
            复制
          </Button>
        </div>
      ),
    },
    {
      title: '页面标识(pageId)',
      dataIndex: 'pageUniqueId',
      key: 'pageUniqueId',
      render: (text, record) => copyText(text, record),
    },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      width: 150,
      render: row => triggerConditionEnum.find(item => item.key === row)?.value || '',
    },
    {
      title: '事件截图',
      dataIndex: 'eventType',
      key: 'eventType',
      width: 90,
      render: (text, record) => (
        <UploadPicture
          isEditImg={false}
          imgWidth="30px"
          imgHeight="30px"
          getUploadPic
          pictureUrl={record.picture}
        ></UploadPicture>
      ),
    },
    {
      title: '创建人',
      dataIndex: 'createUserName',
      key: 'createUserName',
      width: 100,
    },
    {
      title: '自定义参数',
      dataIndex: 'priAttributeParamVO',
      key: 'priAttributeParamVO',
      ellipsis: true,
      render: (text, record) => showTagsList(record.priAttributeParamVO),
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      key: 'gmtCreate',
      width: 200,
      render: (text, record) => (
        <span>{moment(record.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
    {
      title: '更新人',
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      width: 100,
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      key: 'gmtModified',
      width: 200,
      render: (text, record) => (
        <span>{moment(record.gmtModified).format('YYYY-MM-DD HH:mm:ss')}</span>
      ),
    },
  ];
  const [dataList, setDataList] = useState([]);
  // 自定义正则表达式校验规则
  const validateInput = (rule, value, callback) => {
    const regex = /^[\u4e00-\u9fa5a-zA-Z0-9()（）]/;
    if (value && !regex.test(value)) {
      callback('仅支持输入汉字、字母、数字、括号等');
    } else if (value && value.length > 20) {
      callback('最多只能输入20个字符');
    } else {
      callback();
    }
  };

  useEffect(() => {
    // 初次进入非新增, 获取详情数据进行回显
    if (pageStatus !== 'add' && pageId) {
      queryDetail({
        id: pageId,
      }).then(res => {
        console.log('接口返回详情:', res);
        if (res && res.code === 1) {
          form.setFieldsValue({
            pageName: res.data.pageName,
            url: res.data.url,
            picture: res.data.picture,
            remarks: res.data.remarks,
            pageUniqueId: res.data.pageUniqueId,
          });
          setDataList(res.data.trackingEventInfos);
        }
      });
    }
  }, []);

  function copyText(text, record) {
    const { pageUniqueId } = record;
    return (
      <div>
        <span>{pageUniqueId}</span>
        <Button type="link" onClick={() => copyPageId2(pageUniqueId)}>
          复制
        </Button>
      </div>
    );
  }

  function copyPageId2(val) {
    if (window.navigator.clipboard) {
      navigator.clipboard.writeText(val);
      message.success('复制成功');
    }
  }

  function showTagsList(val) {
    const tagArr = val.map(i => i.customizeKey) || [];
    const tagArrText = tagArr.join(',');
    return (
      <Tooltip placement="topLeft" title={tagArrText}>
        {tagArr.length ? tagArr.map(x => <Tag color="blue">{x}</Tag>) : <span>--</span>}
      </Tooltip>
    );
  }

  // 开发者
  function fetchDevUser(val) {
    const params = { keyWord: val || '' };
    // 调获取人群列表的接口信息
    queryUserInfo(params).then(res => {
      if (res && res.code === 1) {
        setDevUserList({ data: res.data, fetching: true });
      } else {
        setDevUserList({ data: [], fetching: false });
      }
    });
  }

  function devUserChange(val) {
    // 点击总的清空按钮
    if (!val) {
      setDevUserList({ data: [], fetching: false });
    }
  }

  // 测试者
  function fetchTestUser(val) {
    const params = { keyWord: val || '' };
    // 调获取人群列表的接口信息
    queryUserInfo(params).then(res => {
      if (res && res.code === 1) {
        setTestUserList({ data: res.data, fetching: true });
      } else {
        setTestUserList({ data: [], fetching: false });
      }
    });
  }

  function testUserChange(val) {
    if (!val) {
      setTestUserList({ data: [], fetching: false });
    }
  }

  function getUploadPic(val) {
    console.log('图片上传后的值', val);
    form.setFieldsValue({
      picture: val,
    });
  }

  // 创建
  function createPage() {
    // 做校验
    form
      .validateFields()
      .then(formValue => {
        if (pageStatus === 'add') {
          setShowModal(true);
        } else if (pageStatus === 'edit') {
          const formParams = { ...form.getFieldsValue() };
          const params = {
            businessType,
            applicationType,
            id: pageId || '',
            pageName: formParams.pageName || '',
            url: formParams.url || '',
            picture: formParams.picture || '',
            remarks: formParams.remarks || '',
          };
          pageEditApi(params).then(res => {
            if (res && res.code === 1) {
              message.success('编辑成功');
              // 跳转回列表页
              window.location.href = '/buryingPointNew/pageTracking';
            } else if (res && res.code === 8070) {
              message.error(res.msg || '页面名称重复,请重新输入');
            } else {
              message.error('保存失败');
            }
          });
        }
      })
      .catch(err => {
        console.log(err, 'err', form.getFieldsError());
      });
  }

  // 保存
  function handleOk() {
    form
      .validateFields()
      .then(formValue => {
        const params = {
          businessType,
          applicationType,
          pageName: formValue.pageName,
          developUserId: formValue?.developUserInfo?.key ?? '',
          developUserName: formValue?.developUserInfo?.label ?? '',
          testUserId: formValue?.testUserInfo?.key ?? '',
          testUserName: formValue?.testUserInfo?.label ?? '',
          url: formValue?.url ?? '',
          picture: formValue?.picture ?? '',
          remarks: formValue?.remarks ?? '',
        };
        pageAddApi(params).then(res => {
          if (res && res.code === 1) {
            setShowModal(false);
            // 跳转回列表页
            window.location.href = '/buryingPointNew/pageTracking';
          } else if (res && res.code === 8070) {
            message.error(res.msg || '页面名称重复,请重新输入');
          } else {
            message.error('创建失败');
          }
        });
      })
      .catch(err => {
        console.log(err, 'err', form.getFieldsError());
      });
  }

  // 取消
  function handleCancel() {
    setShowModal(false);
  }

  function copyPageId() {
    const { pageUniqueId } = form.getFieldsValue();
    if (window.navigator.clipboard) {
      navigator.clipboard.writeText(pageUniqueId);
      message.success('复制成功');
    }
  }

  // 验证输入框URL是否输入正确
  const checkCnData = (rule, value, callback) => {
    const reg = /^(https?):\/\/[^\s\/$.?#].[^\s]*$/i;
    if (value && !reg.test(value)) {
      callback(new Error('请输入正确的URL'));
    }
    callback();
  };

  return (
    <PageHeaderWrapper
      title={false}
      content={
        <>
          <BizPlatTip businessType={businessType} applicationType={applicationType}></BizPlatTip>
          <div
            style={{
              backgroundColor: '#F7F9FD',
              margin: '10px 0',
              padding: '6px 0',
              fontWeight: 700,
            }}
          >
            <div
              style={{
                float: 'left',
                marginRight: '10px',
                backgroundColor: '#2E7EFF',
                width: '3px',
                height: '18px',
                borderRadius: '5px',
              }}
            ></div>
            页面基本信息
          </div>

          <Form form={form} layout="horizontal" labelAlign="right" {...formItemLayout}>
            <Form.Item label={<span className="requiredCheck">页面名称</span>}>
              <Form.Item
                name="pageName"
                rules={[
                  { required: true, message: '请输入页面中文名称' },
                  { validator: validateInput, trigger: 'blur' },
                ]}
                noStyle
              >
                <Input
                  allowClear
                  disabled={pageStatus === 'detail'}
                  placeholder="请输入页面名称"
                  style={{ width: '500px' }}
                />
              </Form.Item>
              {pageStatus !== 'detail' ? (
                <span style={{ margin: '0 20px', color: '#ccc' }}>示例: 司机信息管理页面</span>
              ) : null}
            </Form.Item>
            {pageStatus === 'detail' ? (
              <Form.Item label="页面标识(pageId)" name="pageUniqueId">
                <span style={{ color: 'orange' }}>{form.getFieldValue('pageUniqueId')}</span>
                <Button type="link" onClick={() => copyPageId()} style={{ margin: '0 10px' }}>
                  复制
                </Button>
              </Form.Item>
            ) : (
              <></>
            )}
            <Form.Item
              label="页面URL"
              name="url"
              rules={[
                { validator: checkCnData, trigger: 'blur' },
                { max: 500, message: '最多可输入500个字符' },
              ]}
            >
              {pageStatus === 'detail' ? (
                <span>{form.getFieldValue('url')}</span>
              ) : (
                <Input
                  allowClear
                  disabled={pageStatus === 'detail'}
                  placeholder="请输入页面URL,最多可输入500个字符"
                  style={{ width: '500px' }}
                />
              )}
            </Form.Item>
            <Form.Item
              label="页面截图"
              name="picture"
              rules={[{ required: true, message: '请上传页面截图' }]}
            >
              <UploadPicture
                isEditImg={pageStatus !== 'detail'}
                pictureUrl={form.getFieldValue('picture')}
                getUploadPic={getUploadPic}
                maskText="预览"
              ></UploadPicture>
            </Form.Item>
            <Form.Item
              label="备注"
              name="remarks"
              rules={[{ max: 200, message: '最多可输入200个字符' }]}
            >
              <TextArea
                disabled={pageStatus === 'detail'}
                autoSize={{ minRows: 5 }}
                placeholder="请输入备注信息, 最多可输入200个字符"
                style={{ width: '500px' }}
              />
            </Form.Item>
          </Form>

          {pageStatus === 'detail' ? (
            <div>
              <div
                style={{
                  backgroundColor: '#F7F9FD',
                  margin: '10px 0',
                  padding: '6px 0',
                  fontWeight: 700,
                }}
              >
                <div
                  style={{
                    float: 'left',
                    marginRight: '10px',
                    backgroundColor: '#2E7EFF',
                    width: '3px',
                    height: '18px',
                    borderRadius: '5px',
                  }}
                ></div>
                包含事件
              </div>
              <Table
                columns={columns}
                dataSource={dataList}
                scroll={{ x: 1300 }}
                pagination={false}
                rowKey="id"
              />
            </div>
          ) : (
            <></>
          )}

          <div style={{ textAlign: 'center' }}>
            <Button href="/buryingPointNew/pageTracking" style={{ margin: '10px' }}>
              {pageStatus === 'detail' ? '关闭' : '取消'}
            </Button>
            {pageStatus !== 'detail' ? (
              <Button type="primary" style={{ margin: '10px' }} onClick={createPage}>
                {pageStatus === 'add' ? '创建' : '确定'}
              </Button>
            ) : (
              <></>
            )}
          </div>

          {showModal ? (
            <Modal visible={showModal} onOk={handleOk} onCancel={handleCancel}>
              <p>{`系统为您创建了该页面的浏览事件: ${form.getFieldValue(
                'pageName',
              )}浏览,请完善以下信息`}</p>
              <Form form={form} layout="horizontal" labelAlign="right" {...formItemLayout}>
                <Form.Item
                  label="开发者:"
                  name="developUserInfo"
                  rules={[{ required: true, message: '请输入开发者姓名' }]}
                >
                  <Select
                    allowClear
                    showSearch
                    labelInValue
                    placeholder="请输入开发者姓名"
                    filterOption={false}
                    onSearch={fetchDevUser}
                    onChange={devUserChange}
                    style={{ width: '200px' }}
                  >
                    {devUserList.data.length &&
                      devUserList.data.map(d => <Option key={d.id}>{d.name}</Option>)}
                  </Select>
                </Form.Item>
                <Form.Item
                  label="测试者:"
                  name="testUserInfo"
                  rules={[{ required: true, message: '请输入测试者姓名' }]}
                >
                  <Select
                    allowClear
                    showSearch
                    labelInValue
                    placeholder="请输入测试者姓名"
                    filterOption={false}
                    onSearch={fetchTestUser}
                    onChange={testUserChange}
                    style={{ width: '200px' }}
                  >
                    {testUserList.data.length &&
                      testUserList.data.map(d => <Option key={d.id}>{d.name}</Option>)}
                  </Select>
                </Form.Item>
              </Form>
              <div></div>
            </Modal>
          ) : (
            <></>
          )}
        </>
      }
    />
  );
};

export default PageDetailMeth;
