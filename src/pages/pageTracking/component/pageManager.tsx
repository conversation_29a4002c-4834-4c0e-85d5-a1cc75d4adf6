import React, { useEffect, useState } from 'react';
import { Form, Button, Table, Input, Select, Tooltip, Image } from '@blmcp/ui';
import { Pagination } from 'antd'
import { DownOutlined, EyeOutlined } from '@ant-design/icons';
import moment from 'moment';
import { queryUserInfo, pageDetailsList } from '../services/index'
import PSB from '@/styles/pagingSutionBottom.less'

const PageManager = ({ applicationType }) => {
  const [form] = Form.useForm();
  const businessType = JSON.parse(sessionStorage.getItem('businessType'))
  const { Option } = Select;
  const [pageTotal, setPageTotal] = useState(0)
  // 创建人信息
  const [createUserList, setCreateUserList] = useState({ data: [] })
  // 更新人信息
  const [updateUserList, setUpdateUserList] = useState({ data: [] })
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSizeData, setPageSizeData] = useState(10)
  // 更多按钮图标切换
  const [expand, setExpand] = useState(false);
  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };
  // 表格可滚动高度
  const [tableHeight, setTableHeight] = useState(300);
  // table框中的明细
  const columns = [
    {
      title: '页面ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      width: 100,
    },
    {
      title: '页面名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      // width: 100,
    },
    {
      title: '页面标识(pageId)',
      dataIndex: 'pageUniqueId',
      key: 'pageUniqueId',
      align: 'center',
      // width: 200,
      render: (text, record) => (
        <span>{record.pageUniqueId}</span>
      ),
    },
    {
      title: '页面URL',
      dataIndex: 'url',
      key: 'url',
      align: 'center',
      ellipsis: true,
      // width: 200,
      render: (text, record) => (
        <Tooltip placement="topLeft" title={text}>
          <span>{record.url}</span>
        </Tooltip>
      ),
    },
    {
      title: '页面截图',
      dataIndex: 'picture',
      key: 'picture',
      align: 'center',
      width: 100,
      render: (text, record) => (
        <Image
          width={30}
          height={30}
          src={record.picture}
          preview={{ mask: <EyeOutlined rev="horizontal"/> }}
        />
      ),
    },
    {
      title: '事件个数',
      dataIndex: 'eventCnt',
      key: 'eventCnt',
      align: 'center',
      width: 100,
    },
    {
      title: '创建人/创建时间',
      dataIndex: 'createUserName',
      key: 'createUserName',
      align: 'left',
      // width: 200,
      render: (text, record) => (
        <div>
          <span>{record.createUserName}</span><br/>
          <span>{moment(record.gmtCreate).format('YYYY-MM-DD HH:mm:ss') }</span>
        </div>
      ),
    },
    {
      title: '更新人/更新时间',
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      align: 'left',
      // width: 200,
      render: (text, record) => (
        <div>
          <span>{record.updateUserName}</span><br/>
          <span>{moment(record.gmtModified).format('YYYY-MM-DD HH:mm:ss')}</span>
        </div>
      ),
    },
    {
      key: 'action',
      title: '操作',
      fixed: 'right',
      width: 260,
      render: (text, record) => (
        <span>
          <Button
            type="link"
            onClick= {() => {
              window.open(`/buryingPointNew/pageDetail?pageStatus=detail&businessType=${businessType}&applicationType=${applicationType}&pageId=${record.id}`)
            } }
          >查看</Button>
          <Button
            type="link"
            href={`/buryingPointNew/pageDetail?pageStatus=edit&businessType=${businessType}&applicationType=${applicationType}&pageId=${record.id}`}
          >编辑</Button>
          <Button
            type="link" href={`/buryingPointNew/eventDetail?businessType=${businessType}&applicationType=${applicationType}&pageStatus=add&pageId=${record.id}`}
          >新增事件</Button>
      </span>
      ),
    },
  ]
  const [dataList, setDataList] = useState([])


  // 搜索
  const searchQuery = (pageNum = 1, pageSize = 10) => {
    form.validateFields().then(formValue => {
      // 组装查询条件
      const params = {
        ...form.getFieldsValue(),
        pageNum,
        pageSize,
        businessType,
        applicationType,
      }
      pageDetailsList(params).then(res => {
        if (res.code === 1 && res.data) {
          setDataList(res.data?.items)
          setPageTotal(res.data?.totalNum)
          setCurrentPage(pageNum)
          setPageSizeData(pageSize)
        }
      })
    }).catch(err => {
      console.log(err, 'err', form.getFieldsError());
    });
  }
  // 重 置
  function handleReset() {
    // 重置表单筛选项
    form.resetFields()
    // 重置表单下拉框中的下拉数据
    setCreateUserList({ data: [] })
    setUpdateUserList({ data: [] })
    // 重置完成,查询数据
    searchQuery()
  }
  // 搜索创建人/更新人
  function fetchCreateUser(value = '', isCreate = true) {
    const params = { keyWord: value }
    // 调获取人群列表的接口信息
    queryUserInfo(params).then(res => {
      if (res?.code === 1 && res?.data) {
        if (isCreate) {
          setCreateUserList({ data: res.data })
        } else {
          setUpdateUserList({ data: res.data })
        }
      }
    })
  }
  // 创建人值变更
  function createUserChange(val) {
    // 点击总的清空按钮
    if (!val) {
      setCreateUserList({ data: [] })
    }
  }
  function updateUserChange(val) {
    if (!val) {
      setUpdateUserList({ data: [] })
    }
  }
  // 分页改变事件
  function paginationChang(num, size) {
    // 分页改变重新调查询接口传新的页面和条数
    searchQuery(num, size)
  }
  function onShowSizeChange(num, size) {
    // 分页改变重新调查询接口传新的页面和条数
    searchQuery(1, size)
  }

  // 计算表格动态可滚动高度
  const calculateTableHeight = () => {
    // 整体高度
    const content = document.querySelector('.pageTracking-content');
    // form表单高度
    const contentForm = document.querySelector(`.pageTracking-content_form${applicationType}`);
    // 分页器高度
    const contentPagination = document.querySelector('.pageTracking-content_pagination');
    // 表格头部高度
    const containerTableThead = document.querySelector('.ant-table-thead');

    // 表格可滚动高度 = 整体高度 - form表单高度 - 分页器高度 - table表格的头部高度
    const height =
      content?.getBoundingClientRect()?.height -
      contentForm?.getBoundingClientRect()?.height -
      contentPagination?.getBoundingClientRect()?.height -
      containerTableThead?.getBoundingClientRect()?.height;
    setTableHeight(height);
  };

  useEffect(() => {
    handleReset()

    // 在加载和窗口大小改变时重新计算表格高度
    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);
    // form表单的高度变化时重新计算表格高度  获取目标元素
    const containerForm = document.querySelector(`.pageTracking-content_form${applicationType}`);
    // 创建 MutationObserver 对象
    const observer = new MutationObserver(mutationsList => {
      mutationsList.forEach(item => {
        if (item.type === 'attributes' && item.attributeName === 'style') {
          // 当元素的 style 属性发生变化时会被触发
          calculateTableHeight();
        }
      })
    });
    // 配置观察选项
    const config = { attributes: true, attributeFilter: ['style'], subtree: true };
    // 开始监听
    observer.observe(containerForm, config);
    return () => {
      window.removeEventListener('resize', calculateTableHeight);
      observer.disconnect()
    }
  }, []);
  const validatorNumber = (rule, value, callback) => {
    const regexPattern = /^[0-9]+$/;
    if (value && !regexPattern.test(value)) {
      callback('仅允许输入数字');
    } else {
      callback();
    }
  };


  return (
    <div className={`${PSB.content} pageTracking-content`}>
      <div className={`${PSB.content_form} pageTracking-content_form${applicationType}`}>
        <Form form={form} layout="inline" labelAlign="right" {...formItemLayout}>
          <Form.Item
            label="页面ID"
            name="id"
            className={PSB.formItemCss}
            rules={[
              { required: false, validator: validatorNumber },
            ]}
          >
            <Input placeholder="请输入页面ID" allowClear/>
          </Form.Item>
          <Form.Item label="页面名称" name="pageName" className={PSB.formItemCss}>
            <Input placeholder="请输入页面名称" allowClear/>
          </Form.Item>
          <Form.Item label="页面标识" name="pageUniqueId" className={PSB.formItemCss}>
            <Input placeholder="请输入页面标识" allowClear/>
          </Form.Item>
          <Form.Item
            label="创建人"
            name="createUserId"
            className={expand ? PSB.formItemExpand : PSB.formItemUnExpand}
          >
            <Select
              showSearch
              allowClear
              filterOption={false}
              placeholder="请输入创建人姓名"
              onSearch={value => fetchCreateUser(value, true)}
              onChange={createUserChange}
              popupClassName="emptySelectContentText"
              notFoundContent="请输入关键字选择"
            >
              {createUserList?.data.map(item => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label="更新人"
            name="updateUserId"
            className={expand ? PSB.formItemExpand : PSB.formItemUnExpand}
          >
            <Select
              showSearch
              allowClear
              filterOption={false}
              placeholder="请输入更新人姓名"
              onSearch={value => fetchCreateUser(value, false)}
              onChange={updateUserChange}
              popupClassName="emptySelectContentText"
              notFoundContent="请输入关键字选择"
            >
              {updateUserList?.data.map(item => (
                <Option value={item.id} key={item.id}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <div style={{ textAlign: 'right' }}>
            <a
              style={{ fontSize: 12 }}
              onClick={() => {
                setExpand(!expand);
              }}
            >
              <DownOutlined rev="horizontal" rotate={expand ? 180 : 0} /> {expand ? '收起' : '更多'}
            </a>
            <Button style={{ margin: '0 10px' }} onClick={handleReset}>重 置</Button>
            <Button type="primary" onClick={() => searchQuery()}>搜 索</Button>
            <Button
              type="primary"
              href={`/buryingPointNew/pageDetail?pageStatus=add&businessType=${businessType}&applicationType=${applicationType}`}
              style={{ color: '#fff', backgroundColor: '#1890ff' }}
            >新增页面</Button>
          </div>
        </Form>
      </div>
      <div className={`${PSB.content_table} pageTracking-content_table`}>
        {/* 列表明细 */}
        <Table columns={columns} dataSource={dataList} pagination={false} scroll={{ y: tableHeight, x: '120%' }}/>
      </div>
      <div className={`${PSB.content_pagination} pageTracking-content_pagination`}>
        <Pagination
          showSizeChanger
          showQuickJumper
          current={currentPage}
          pageSize={pageSizeData}
          defaultCurrent={1}
          total={pageTotal}
          showTotal={total => `共 ${total} 条`}
          onChange={paginationChang}
          onShowSizeChange={onShowSizeChange}
          style={{ float: 'right', margin: '16px 0' }}
        />
      </div>

    </div>

  )
}

export default PageManager;
