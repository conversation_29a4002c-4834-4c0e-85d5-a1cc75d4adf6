// 埋点管理(新)--页面
import React from 'react';
import { Tabs } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { connect } from 'dva';
import RelevanceComponentForm from './component/pageManager';
import PSB from '@/styles/pagingSutionBottom.less';

const PageTracking = props => {
  const { TabPane } = Tabs;
  const { platformList } = props;

  return (
    <PageHeaderWrapper
      title={false}
      className={`${PSB.pageHeaderWrapperStyle} pageNew`}
      content={
        <Tabs defaultActiveKey="3" className={PSB.tabBox}>
          {platformList.map(item => (
            <TabPane tab={item.applicationDesc} key={item.applicationType}>
              <RelevanceComponentForm applicationType={item.applicationType} />
            </TabPane>
          ))}
        </Tabs>
      }
    />
  );
};

export default connect(({ user }) => ({
  platformList: user.platformList,
}))(PageTracking);
