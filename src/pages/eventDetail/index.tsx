import React from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { getUrlKey } from '@/utils/utils';
import styles from './components/index.less'
import EventEdit from './components/eventEdit';
import EventViewDetails from './components/eventViewDetails'


const EventDetail = () => {
  const { businessType, applicationType, pageStatus, eventId = '', pageId = '' } = getUrlKey(
    window.location.href,
  );
  return (
    <PageHeaderWrapper
      title={false}
      className={styles.pageHeaderWrapperStyle}
      content={
        <div className={styles.countBox}>
          {
            pageStatus === 'detail' ? (
              // 查看事件详情
              <EventViewDetails
                businessType={businessType}
                applicationType={applicationType}
                eventId={eventId}
                pageId={pageId}
              ></EventViewDetails>
            ) : (
              // 新增事件/编辑事件
              <EventEdit
                businessType={businessType}
                applicationType={applicationType}
                pageStatus={pageStatus}
                eventId={eventId}
                pageId={pageId}
              ></EventEdit>
            )
          }
        </div>
      }
    ></PageHeaderWrapper>
  )
}

export default EventDetail
