import React, { useEffect, useState, useRef } from 'react';
import { Form, Button, Input, Select, message, Popover, Tooltip, Modal } from '@blmcp/ui';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import { router } from 'umi';
import { connect } from 'dva';
import PriAttributeParams from './priAttributeParams';
import {
  queryDetailsPage,
  queryUserInfo,
  queryAddEvent,
  queryEventDetailById,
  queryEditEvent,
  updateStatus,
} from '../../../services/tracking';
import UploadPicture from '@/components/UploadImg/uploadPicture';
import BizPlatTip from '@/components/BizPlatTip';
import styles from '@/pages/eventDetail/components/index.less';

const { Option } = Select;
const { TextArea } = Input;

const EventEdit = ({ user, businessType, applicationType, pageStatus, eventId, pageId }) => {
  const [form] = Form.useForm();
  const eventTypeForm = Form.useWatch('eventType', form);
  const relatePagesForm = Form.useWatch('relatePages', form);
  const childFormRef = useRef(null);
  const [dataParams, setDataParams] = useState([]);
  // 关联页面下拉框数据
  const [eventPage, setEventPage] = useState({ data: [], fetching: true });
  // 审批人
  const [approveUserIdData, setApproveUserIdData] = useState([]);
  // 开发人下拉框数据
  const [developUserIdData, setDevelopUserIdData] = useState({ data: [], fetching: true });
  // 开发人选中item
  const [developUserIdItem, setDevelopUserIdItem] = useState({});
  // 测试者下拉框数据
  const [testUserIdData, setTestUserIdData] = useState({ data: [], fetching: true });
  // 测试人选中item
  const [testUserIdItem, setTestUserIdItem] = useState({});
  const [priAttributeParamVOInit, setPriAttributeParamVOInit] = useState([]);
  // 事件页面唯一ID
  const [uniqueId, setUniqueId] = useState({ eventUniqueId: '', pageUniqueId: '' });
  // 审核事件详情弹窗
  const [reviewDetailModal, setReviewDetailModal] = useState(false);
  // 事件详情页面创建人姓名
  const [trackingPageBasicInfoPageCreate, setTrackingPageBasicInfoPageCreate] = useState({
    id: -1,
    status: '',
    name: '',
  });
  // 审核详情状态不符
  const [isReviewStatus, setIsReviewStatus] = useState(true);

  const formItemLayout = { labelCol: { span: 4 }, wrapperCol: { span: 18 } };
  const handleChildValueChange = value => {
    setDataParams(value);
  };
  const triggerConditionEnum = [
    { key: 'pv', value: '浏览事件触发条件' },
    { key: 'click', value: '用户点击一次' },
    { key: 'exposure', value: '该区域被曝光一次' },
    // { key: 'popupShow', value: '弹层展示触发条件' },
  ];

  useEffect(() => {
    const eventTypeValue = form.getFieldValue('eventType');
    form.setFieldsValue({ triggerCondition: eventTypeValue });
  }, [eventTypeForm]);
  // 初次进来调用事件详情函数
  useEffect(() => {
    if (eventId?.length) {
      const searchParams = {
        trackingType: 1,
        businessType,
        applicationType,
        eventId: Number(eventId),
      };
      queryEventDetailById(searchParams).then(res => {
        if (res?.code === 1 && res?.data) {
          const {
            eventName,
            eventType,
            picture,
            approveUserId,
            approveUserName,
            developUserId,
            developUserName,
            testUserId,
            testUserName,
            remarks,
            priAttributeParamVO,
            relatePages,
            trackingPageBasicInfoVO,
            eventUniqueId,
            status,
          } = res?.data;
          setEventPage({
            data: [
              {
                id: trackingPageBasicInfoVO[0]?.id,
                pageName: trackingPageBasicInfoVO[0]?.pageName,
              },
            ],
            fetching: true,
          });
          setApproveUserIdData([{ createUserId: approveUserId, createUserName: approveUserName }]);
          setDevelopUserIdData({
            data: [{ id: developUserId, name: developUserName }],
            fetching: true,
          });
          setTestUserIdData({ data: [{ id: testUserId, name: testUserName }], fetching: true });
          setDevelopUserIdItem({ children: developUserName, id: developUserId });
          setTestUserIdItem({ children: testUserName, id: testUserId });
          priAttributeParamVO &&
            priAttributeParamVO.forEach((item, index) => {
              item.key = index;
            });
          setDataParams(priAttributeParamVO ?? []);
          setPriAttributeParamVOInit(priAttributeParamVO ?? []);
          setUniqueId({
            eventUniqueId: eventUniqueId,
            pageUniqueId: trackingPageBasicInfoVO[0]?.pageUniqueId,
          });
          const allData = {
            eventName,
            eventType,
            picture,
            approveUserId,
            developUserId,
            testUserId,
            remarks,
            relatePages: trackingPageBasicInfoVO[0]?.id,
          };
          form.setFieldsValue(allData);
          form.setFieldsValue({ eventName: res?.data?.eventName });
          setTrackingPageBasicInfoPageCreate({
            id: Number(trackingPageBasicInfoVO && trackingPageBasicInfoVO[0]?.createUserId),
            status: status,
            name: trackingPageBasicInfoVO && trackingPageBasicInfoVO[0]?.createUserName,
          });
        }
      });
    }
  }, []);
  // 从页面点击新建事件
  useEffect(() => {
    if (pageStatus === 'add' && pageId) {
      // 从页面新增事件过来 调接口用于回显关联页面
      const searchParams = {
        businessType,
        applicationType,
        trackingType: 1,
        pageNum: 1,
        pageSize: 100,
        id: Number(pageId), // 跟据id查询时用keyWord字段
      };
      queryDetailsPage(searchParams).then(res => {
        if (res?.code === 1 && res?.data) {
          // 获取关联页面list
          setEventPage({ data: res.data?.items, fetching: !!res.data?.items.length });
          // 审批人列表
          const { items } = res.data;
          setApproveUserIdData([
            { createUserId: items[0].createUserId, createUserName: items[0].createUserName },
          ]);
          // 审批人
          form.setFieldsValue({ approveUserId: items[0].createUserId });
          // 选中对应的关联页面数据
          form.setFieldsValue({ relatePages: Number(pageId) });
        }
      });
    }
  }, [pageId]);
  // redux user数据更新
  useEffect(() => {
    const currentUserID = user.currentUserInfo?.id;
    // 若审核详情页面创建人与审核人不一致，或该事件状态不为待审核状态，弹窗自动跳出
    if (
      (currentUserID || currentUserID === 0) &&
      Number(trackingPageBasicInfoPageCreate.id) >= 0 &&
      pageStatus === 'review'
    ) {
      if (
        Number(trackingPageBasicInfoPageCreate.id) !== Number(user.currentUserInfo?.id) ||
        Number(trackingPageBasicInfoPageCreate.status) !== 1
      ) {
        setReviewDetailModal(true);
        setIsReviewStatus(false);
      }
    }
  }, [user.currentUserInfo, trackingPageBasicInfoPageCreate]);
  // 点击取消按钮
  const handleClickClose = () => {
    // pageId存在证明是从页面管理过来的，点击取消时返回页面管理页面；否则返回事件配置页面
    if (pageId || pageId === 0) {
      router.push({ pathname: '/buryingPointNew/pageTracking' });
    } else {
      router.push({ pathname: '/buryingPointNew/eventTracking' });
    }
  };
  // 保存按钮点击函数
  const handleClickSave = () => {
    // 关联页面是一个数组
    childFormRef.current.validateFields().then(value => {
      form
        .validateFields()
        .then(formValue => {
          const relatePagesValue = form.getFieldValue('relatePages');
          const searchParams = {
            ...form.getFieldsValue(),
            trackingType: 1,
            businessType,
            applicationType,
            priAttributeParamDTO: dataParams,
            createUserId: user.currentUserInfo.id,
            createUserName: user.currentUserInfo.name,
            // 审批人名
            approveUserName: approveUserIdData[0]?.createUserName,
            // 开发人
            developUserName: developUserIdItem?.children,
            // 测试人
            testUserName: testUserIdItem?.children,
            // 关联页面变为数组
            relatePages: relatePagesValue ? [relatePagesValue] : [],
            eventId: Number(eventId),
          };
          const saveApi = pageStatus === 'add' ? queryAddEvent : queryEditEvent;
          saveApi(searchParams)
            .then(res => {
              if (res?.code === 1) {
                message.success('保存成功');
                router.push({ pathname: '/buryingPointNew/eventTracking' });
              } else {
                message.error(res?.msg);
              }
            })
            .catch(() => {
              message.error('保存失败');
            });
        })
        .catch(err => {
          console.log(err, 'err', form.getFieldsError());
        });
    });
  };
  // 页面下拉数据
  // 关联页面搜索函数
  const handleChangeEventPageId = (value = '') => {
    // pageName
    const searchParams = {
      businessType,
      applicationType,
      trackingType: 1,
      pageNum: 1,
      pageSize: 50,
      pageName: value,
    };
    queryDetailsPage(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        setEventPage({ data: res.data?.items, fetching: !!res.data?.items.length });
      }
    });
  };
  // 创建人更新人搜索
  const handleChangeUser = (value = '', isDevelop = true) => {
    // pageName
    const searchParams = {
      keyWord: value,
    };
    queryUserInfo(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        if (isDevelop) {
          setDevelopUserIdData({ data: res.data, fetching: !res.data.length });
        } else {
          setTestUserIdData({ data: res.data, fetching: !res.data.length });
        }
      }
    });
  };
  const handlePagesChange = (value, item) => {
    const approveUserIdDataAll = [];
    const approveUserIdDataItem = eventPage.data.find(item => item.id === value);
    approveUserIdDataAll.push(approveUserIdDataItem);
    setApproveUserIdData(approveUserIdDataAll);
  };
  // 用户选择信息
  const handleUserIdChange = (value, item, isDevelop) => {
    if (isDevelop) {
      setDevelopUserIdItem(item?.props);
    } else {
      setTestUserIdItem(item?.props);
    }
  };
  // 状态流转
  const handleStatusChange = (eventId, status) => {
    const searchParams = {
      trackingType: 1,
      businessType,
      applicationType,
      eventId,
      status,
    };
    updateStatus(searchParams).then(res => {
      if (res?.code === 1 && res.data) {
        message.success('状态流转成功');
      }
    });
  };
  // 自定义正则表达式校验规则
  const validateInput = (rule, value, callback) => {
    const regex = /^[\u4e00-\u9fa5a-zA-Z0-9()（）]/;
    if (value && !regex.test(value)) {
      callback('仅支持输入汉字、字母、数字、括号等');
    } else if (value && value.length > 20) {
      callback('最多只能输入20个字符');
    } else {
      callback();
    }
  };
  // 自定义正则表达式校验规则
  const validateInputRemark = (rule, value, callback) => {
    if (value && value.length > 200) {
      callback('最多只能输入200个字符');
    } else {
      callback();
    }
  };
  const getUploadPic = val => {
    form.setFieldsValue({
      picture: val,
    });
  };

  // 关联页面改变重置审批人信息
  useEffect(() => {
    form.setFieldsValue({ approveUserId: approveUserIdData[0]?.createUserId });
  }, [relatePagesForm]);
  // 审核详情二次确认弹窗-返回
  const handleReviewBack = () => {
    console.log('审核二次确认返回了');
    router.push({ pathname: '/buryingPointNew/reviewEventTracking' });
  };
  return (
    <>
      <BizPlatTip businessType={businessType} applicationType={applicationType}></BizPlatTip>
      <Form form={form} layout="horizontal" {...formItemLayout} labelAlign="right">
        {pageId !== '' ? (
          <div>
            关联页面:
            <span style={{ margin: '0 10px' }}>
              {eventPage?.data.find(item => item.id === Number(pageId))?.pageName ?? '--'}
            </span>
          </div>
        ) : null}
        <div className={styles.splitBar}>
          <div className={styles.blueStrip}></div>
          基础信息
        </div>
        <Form.Item label={<span className="requiredCheck">事件名称</span>}>
          <Form.Item
            noStyle
            name="eventName"
            rules={[{ required: true, message: '请输入事件名称' }, { validator: validateInput }]}
          >
            <Input
              disabled={['review'].includes(pageStatus) || form.getFieldValue('eventType') === 'pv'}
              allowClear
              placeholder="请输入事件名称"
              style={{ width: '500px' }}
            />
          </Form.Item>
          <span style={{ margin: '0 20px', color: 'rgb(144, 147, 153)' }}>
            示例: 新增司机按钮点击
          </span>
        </Form.Item>
        {pageId !== '' ? (
          <>
            <Form.Item label={<span className="requiredCheck">事件类型</span>}>
              <Form.Item
                noStyle
                name="eventType"
                rules={[{ required: true, message: '请选择事件类型' }]}
              >
                <Select
                  placeholder="请选择事件类型"
                  disabled={['edit', 'review'].includes(pageStatus)}
                  style={{ width: '300px' }}
                  allowClear
                >
                  <Option value="pv" disabled style={{ width: '100%' }}>
                    <Tooltip
                      placement="topLeft"
                      title="系统已自动创建页面浏览事件，请到对应页面下查看"
                    >
                      <div>页面浏览事件</div>
                    </Tooltip>
                  </Option>
                  <Option value="click">模块点击事件</Option>
                  <Option value="exposure">模块曝光事件</Option>
                  {/*<Option value="popupShow">弹层事件</Option>*/}
                </Select>
              </Form.Item>
              <span style={{ margin: '0 5px 0 20px', color: 'rgb(144, 147, 153)' }}>触发条件:</span>
              <span>{triggerConditionEnum.find(i => i.key === eventTypeForm)?.value ?? '--'}</span>
            </Form.Item>
          </>
        ) : (
          <>
            <Form.Item label={<span className="requiredCheck">关联页面</span>}>
              <Form.Item
                noStyle
                name="relatePages"
                rules={[{ required: true, message: '请选择关联页面' }]}
              >
                <Select
                  showSearch
                  placeholder="请选择页面"
                  onFocus={handleChangeEventPageId}
                  onSearch={value => handleChangeEventPageId(value)}
                  onChange={(value, item) => handlePagesChange(value, item)}
                  filterOption={false}
                  disabled={
                    ['review'].includes(pageStatus) || form.getFieldValue('eventType') === 'pv'
                  }
                  style={{ width: '500px' }}
                  popupClassName="emptySelectContentText"
                  notFoundContent="请输入关键字选择"
                >
                  {eventPage?.data.map(item => (
                    <Option value={item.id} key={item.id}>
                      {item.pageName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <span style={{ margin: '0 20px', color: 'rgb(144, 147, 153)' }}>
                未找到页面，去
                <a
                  href={`/buryingPointNew/pageDetail?pageStatus=add&businessType=${businessType}&applicationType=${applicationType}`}
                >
                  新建页面
                </a>
              </span>
            </Form.Item>
            <Form.Item
              label="事件类型"
              name="eventType"
              rules={[{ required: true, message: '请选择事件类型' }]}
            >
              <Select
                placeholder="请选择事件类型"
                disabled={['edit', 'review'].includes(pageStatus)}
                style={{ width: '500px' }}
                allowClear
              >
                <Option value="pv" disabled style={{ width: '100%' }}>
                  <Tooltip
                    placement="topLeft"
                    title="系统已自动创建页面浏览事件，请到对应页面下查看"
                  >
                    <div>页面浏览事件</div>
                  </Tooltip>
                </Option>
                <Option value="click">模块点击事件</Option>
                <Option value="exposure">模块曝光事件</Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="触发条件"
              name="triggerCondition"
              rules={[{ required: true, message: '请选择触发条件' }]}
            >
              <Select placeholder="请选择触发条件" disabled style={{ width: '500px' }}>
                {triggerConditionEnum.map(item => (
                  <Option value={item.key} key={item.key}>
                    {item.value}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </>
        )}
        <Form.Item
          label="事件截图"
          name="picture"
          rules={[{ required: true, message: '请上传截图' }]}
        >
          <UploadPicture
            isEditImg={
              !(['review'].includes(pageStatus) || form.getFieldValue('eventType') === 'pv')
            }
            pictureUrl={form.getFieldValue('picture')}
            getUploadPic={getUploadPic}
            maskText="预览"
          ></UploadPicture>
        </Form.Item>
        <Form.Item
          label="审批人"
          name="approveUserId"
          rules={[{ required: true, message: '请选择关联页面' }]}
        >
          <Select placeholder="页面创建人姓名" disabled style={{ width: '500px' }}>
            {approveUserIdData.map(item => (
              <Option value={item.createUserId} key={item.createUserId}>
                {item.createUserName}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="开发者"
          name="developUserId"
          rules={[{ required: true, message: '请输入开发者姓名' }]}
        >
          <Select
            showSearch
            placeholder="请输入开发者姓名"
            onSearch={value => handleChangeUser(value, true)}
            onChange={(value, item) => handleUserIdChange(value, item, true)}
            filterOption={false}
            disabled={['review'].includes(pageStatus)}
            style={{ width: '500px' }}
            allowClear
            popupClassName="emptySelectContentText"
            notFoundContent="请输入关键字选择"
          >
            {developUserIdData?.data.map(item => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="测试者"
          name="testUserId"
          rules={[{ required: true, message: '请输入测试者姓名' }]}
        >
          <Select
            showSearch
            placeholder="请输入测试者姓名"
            onSearch={value => handleChangeUser(value, false)}
            onChange={(value, item) => handleUserIdChange(value, item, false)}
            filterOption={false}
            disabled={['review'].includes(pageStatus)}
            style={{ width: '500px' }}
            allowClear
            popupClassName="emptySelectContentText"
            notFoundContent="请输入关键字选择"
          >
            {testUserIdData?.data.map(item => (
              <Option value={item.id} key={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="备注"
          name="remarks"
          rules={[
            { required: true, message: '请输入备注信息' },
            { validator: validateInputRemark },
          ]}
        >
          <TextArea
            placeholder="请输入备注信息"
            disabled={['review'].includes(pageStatus)}
            rows={3}
            style={{ width: '500px' }}
          />
        </Form.Item>
      </Form>
      <div className={styles.splitBar}>
        <div className={styles.blueStrip}></div>
        自定义参数
        <Popover
          content={'公共属性中已包含用户ID和租户ID，并匹配了对应的账号和租户名称，无需自定义属性'}
          placement="right"
        >
          <QuestionCircleOutlined rev="horizontal" style={{ marginLeft: 12 }} />
        </Popover>
      </div>
      <PriAttributeParams
        dataParams={dataParams}
        onParamsValueChange={handleChildValueChange}
        pageStatus={pageStatus}
        ref={childFormRef}
        priAttributeParamVO={priAttributeParamVOInit}
      />
      {pageStatus !== 'review' ? (
        <div style={{ textAlign: 'center', marginTop: 12 }}>
          <Button type="dashed" onClick={handleClickClose}>
            取消
          </Button>
          <Button type="primary" style={{ marginLeft: 12 }} onClick={handleClickSave}>
            确定
          </Button>
        </div>
      ) : (
        <div style={{ textAlign: 'center', marginTop: 12 }}>
          <Button href="/buryingPointNew/eventTracking">取消</Button>
          {isReviewStatus && (
            <>
              <Button
                style={{ marginLeft: 12, color: 'rgba(255, 68, 51, 1)' }}
                href="/buryingPointNew/eventTracking"
                onClick={() => handleStatusChange(eventId, 2)}
              >
                <CloseCircleOutlined rev="horizontal" />
                审核驳回
              </Button>
              <Button
                style={{
                  marginLeft: 12,
                  backgroundColor: 'rgba(18, 178, 72, 1)',
                  color: 'white',
                }}
                href="/buryingPointNew/eventTracking"
                onClick={() => handleStatusChange(eventId, 3)}
              >
                <CheckCircleOutlined rev="horizontal" />
                审核通过
              </Button>
            </>
          )}
        </div>
      )}
      <Modal
        visible={reviewDetailModal}
        closable={false}
        footer={[<Button onClick={() => handleReviewBack()}>返回待审核事件列表</Button>]}
      >
        当前事件审批人或状态已变更
      </Modal>
    </>
  );
};

// const EventEditForm = Form.create()(EventEdit);

export default connect(({ user }) => ({
  user,
}))(EventEdit);
