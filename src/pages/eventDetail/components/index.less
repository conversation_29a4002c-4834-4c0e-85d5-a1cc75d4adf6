// 页面整体样式
.pageHeaderWrapperStyle {
  background-color: #F7F8FA !important;
}
.countBox {
  min-height: calc(100vh - 125px);
  padding: 8px 16px;
  border-radius: 8px;
  background-color: #fff;
}
// 分隔条
.splitBar {
  margin: 16px 0;
  padding: 6px 0;
  font-weight: 700;
  background-color: #F7F9FD;
  .blueStrip {
    float: left;
    margin-right: 10px;
    background-color: #2E7EFF;
    width: 3px;
    height: 18px;
    border-radius: 5px;
  }
}
.tableDom {
  //.ant-table .ant-table-thead > tr > th {
  //  background-color: #979899 !important;
  //  font-size: 14px !important;
  //  font-weight: 700 !important;
  //}

}


.editable-row .ant-form-explain {
    position: absolute;
    font-size: 12px;
    margin-top: -4px;
  }
.buttonDashed{
    height: 32px;
    margin: 12px 0;
    color: rgba(0, 0, 0, 0.9);
    font-size: 13px;
    line-height: 32px;
    text-align: center;
    background: rgba(109, 109, 109, 0.03);
    border: 1px dashed rgba(37, 52, 79, 0.12);
    border-radius: 4px
}
.tableColumn{
    margin-bottom: auto;
    .ant-form-item{
        margin-bottom: 0;
    }
}
