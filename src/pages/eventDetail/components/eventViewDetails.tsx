import React, { useEffect, useState } from 'react';
import { Descriptions, Table, Button, Image } from '@blmcp/ui';
import { CopyOutlined, EyeOutlined } from '@ant-design/icons';
import { message } from 'antd';
import styles from './index.less';
import { queryEventDetailById } from '@/services/tracking';
import BizPlatTip from '@/components/BizPlatTip';

const EventViewDetails = ({ businessType, applicationType, eventId, pageId }) => {
  const [descriptionsList, setDescriptionsList] = useState({});
  const [pageUniqueInfo, setPageUniqueInfo] = useState({});

  // 唯一ID复制
  const copyPageId = (copyId = '') => {
    if (window.navigator.clipboard) {
      if (copyId !== '') {
        navigator.clipboard.writeText(copyId);
        message.success('复制成功');
      }
    }
  };

  const triggerConditionEnum = [
    { key: 'pv', event: '页面浏览事件', condition: '浏览事件触发条件' },
    { key: 'click', event: '模块点击事件', condition: '用户点击一次' },
    { key: 'exposure', event: '模块曝光事件', condition: '该区域被曝光一次' },
  ];
  const items = [
    {
      label: '事件名称',
      children: descriptionsList.eventName,
    },
    {
      label: '事件标识',
      children: (
        <>
          <span>{descriptionsList?.eventUniqueId}</span>
          <Button
            type="link"
            icon={<CopyOutlined rev="horizontal" />}
            onClick={() => copyPageId(descriptionsList?.eventUniqueId)}
          />
        </>
      ),
    },
    {
      label: '关联页面',
      children: pageUniqueInfo?.pageName,
    },
    {
      label: '页面标识',
      children: (
        <>
          <span>{pageUniqueInfo?.pageUniqueId}</span>
          <Button
            type="link"
            icon={<CopyOutlined rev="horizontal" />}
            onClick={() => copyPageId(pageUniqueInfo?.pageUniqueId)}
          />
        </>
      ),
    },

    {
      label: '创建人',
      children: descriptionsList.createUserName,
    },
    {
      label: '审批人',
      children: descriptionsList.approveUserName,
    },
    {
      label: '开发者',
      children: descriptionsList.developUserName,
    },
    {
      label: '测试者',
      children: descriptionsList.testUserName,
    },

    {
      label: '事件截图',
      children: (
        <Image
          preview={{
            mask: (
              <span>
                <EyeOutlined rev="horizontal" />
                预览
              </span>
            ),
          }}
          width="64px"
          height="48px"
          src={descriptionsList.picture}
        />
      ),
    },
    {
      label: '事件类型',
      children: descriptionsList.eventType
        ? triggerConditionEnum.find(i => i.key === descriptionsList.eventType).event
        : '--',
    },
    {
      label: '备注',
      span: 2,
      children: descriptionsList.remarks,
    },
  ];
  const tableColumns = [
    {
      title: '属性中文名',
      dataIndex: 'fieldName',
      key: 'fieldName',
    },
    {
      title: '属性名',
      dataIndex: 'customizeKey',
      key: 'customizeKey',
    },
    {
      title: '数据类型',
      dataIndex: 'fieldTypeOrg',
      key: 'fieldTypeOrg',
      render: text => {
        switch (text) {
          case 1:
            return '字符串';
          case 2:
            return '数值';
          case 3:
            return '枚举';
          default:
            return '--';
        }
      },
    },
    {
      title: '值(枚举类型必填)',
      dataIndex: 'remarks',
      key: 'remarks',
    },
    {
      title: '示例',
      dataIndex: 'fieldTypeOrg',
      key: 'id',
      render: text => {
        switch (text) {
          case 1:
            return '非必填，如：筛选项中的订单ID';
          case 2:
            return '非必填，如：筛选项中的订单金额';
          case 3:
            return '必填，字符串，如：man:男,women:女';
          default:
            return '--';
        }
      },
    },
  ];
  const [tableData, setTableData] = useState([]);
  useEffect(() => {
    // 初次进来调用事件详情函数
    const searchParams = {
      trackingType: 1, // 埋点类型: 1-事件, 2-页面
      businessType, // 业务类型: 1-网约车, 2-代驾
      applicationType,
      eventId, // 事件id
    };
    queryEventDetailById(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        setDescriptionsList(res.data);
        // 更新自定义参数
        setTableData(res.data.priAttributeParamVO);
        if (Array.isArray(res.data?.trackingPageBasicInfoVO) && res.data.relatePages) {
          const itemInfo = res.data.trackingPageBasicInfoVO.find(
            i => i.id === Number(res.data.relatePages),
          );
          // 获取关联页面数据
          setPageUniqueInfo(itemInfo);
        }
      }
    });
  }, []);

  return (
    <>
      <BizPlatTip businessType={businessType} applicationType={applicationType}></BizPlatTip>
      <div className={styles.splitBar}>
        <div className={styles.blueStrip}></div>
        基础信息
      </div>
      <Descriptions
        title=""
        bordered
        size="small"
        column={4}
        items={items}
        style={{ marginBottom: '24px' }}
      />

      <div className={styles.splitBar}>
        <div className={styles.blueStrip}></div>
        自定义参数
      </div>
      <Table
        columns={tableColumns}
        dataSource={tableData}
        pagination={false}
        className={styles.tableDom}
      />
      <div style={{ textAlign: 'center', marginTop: 12 }}>
        <Button type="dashed" href="/buryingPointNew/eventTracking">
          关闭
        </Button>
      </div>
    </>
  );
};

export default EventViewDetails;
