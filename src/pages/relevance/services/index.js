/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-10 21:31:24
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-12 17:11:55
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/relevance/services/index.js
 */
import request from '@/utils/request';

/**
 * 获取列表数据
 * @param {*} pamas 
 * @returns 
 */
export async function getListData(pamas) {
    return request('/admin/v1/eventlog/burypoint/event/query', {
        method: 'POST',
        data: pamas,
    });
}

/**
 * 获取全埋点/手工埋点列表数据
 * @param {*} params 
 * @returns 
 */
export async function getRefListData(params) {
    return request('/admin/v1/eventlog/burypoint/event/queryMappingEventDetails', {
        method: 'POST',
        data: params,
    });
}

/**
 * 新建事件
 * @param {*} params 
 * @returns 
 */
export async function add(params) {
    return request('/admin/v1/eventlog/burypoint/event/add', {
        method: 'POST',
        data: params,
    });
}

/**
 * 编辑事件
 * @param {*} params 
 * @returns 
 */
export async function update(params) {
    return request('/admin/v1/eventlog/burypoint/event/update', {
        method: 'POST',
        data: params,
    });
}

/**
 * 删除条目
 */
export async function delItem(params) {
    return request('/admin/v1/eventlog/burypoint/event/del', {
        method: 'POST',
        data: params
    })
}

/**
 * 添加关联
 * @param {*} params 
 * @returns 
 */
export async function addConnect(params) {
    return request('/admin/v1/eventlog/burypoint/event/addMapping', {
        method: 'POST',
        data: params
    })
}

/**
 * 解除关联
 * @param {*} params 
 * @returns 
 */
export async function removeRef(params) {
    return request('/admin/v1/eventlog/burypoint/event/removeRel', {
        method: 'POST',
        data: params
    })
}