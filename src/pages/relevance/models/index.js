/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-10 11:47:28
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-17 18:46:14
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/relevance/models/index.js
 */
import { notification, message } from 'antd';
import { getListData, getRefListData, add, update, delItem, addConnect, removeRef } from '../services';

const UserModel = {
  namespace: 'relevance',
  state: {
    total: 0,
    listData: [],
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
    *getRefListData({ payload }, { call, put }) {
        const response = yield call(getRefListData, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
          yield put({
            type: 'saveDetailsData',
            payload: Array.isArray(data.items) ? data.items : [],
          });
          yield put({
            type: 'saveDetailTotal',
            payload: data ? data.totalNum : 0,
          });
        } else {
          notification.warn({
            message: '请求列表失败',
            description: msg,
          });
        }
      },
    *add({ payload, callback }, { call, put }) {
        const response = yield call(add, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件创建成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *update({ payload, callback }, { call, put }) {
        const response = yield call(update, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件更新成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *delItem({ payload, callback }, { call, put }) {
        const response = yield call(delItem, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件删除成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *addConnect({ payload, callback }, { call, put }) {
        const response = yield call(addConnect, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *removeRef({ payload, callback }, { call, put }) {
        const response = yield call(removeRef, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: msg,
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    }
  },
  reducers: {
    saveListData(state, action) {
        return { ...state, listData: action.payload }
    },
    saveDetailsData(state, action) {
        return { ...state, detailListData: action.payload }
    },
    saveTotal(state, action) {
        return { ...state, total: action.payload }
    },
    saveDetailTotal(state, action) {
        return { ...state, detailTotal: action.payload }
    },
  },
};
export default UserModel;
