import { connect } from "dva"
import { Divider, Form, Select, Input, Button, Table, Pagination, Popconfirm, Badge, Dropdown, Menu, Icon, message, Tooltip } from 'antd'
import { PageHeaderWrapper } from '@ant-design/pro-layout'
import { useEffect, useRef, useState } from "react";
import ModelComponent from './component/model'
import ConnModelComponent from './component/connectModel'
import { authBtn } from '@/utils/utils';
import { getUser } from '@/utils/user';
import { EnumSourceOptions } from '@/config/index'
import moment from 'moment';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-10 21:17:26
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-12 17:12:30
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/relevance/index.jsx
 */
const RelevanceComponent = ({ 
    dispatch,
    form,
    total = 0,
    listData = [],
    loading,
    ssoAuthorityRole: {
        data: { curResources = [] }
    }
}) => {
    const FormItem = Form.Item;
    const { Option } = Select;
    const [currentPage, setCurrentPage] = useState(1);
    const [ pageSize, setPageSize ] = useState(10);
    const [authList, setAuthList] = useState([]);
    const [ currentId, setCurrentId ] = useState('');
    const demoDrawerRef = useRef()
    const connDrawerRef = useRef()
    const { name: userName } = getUser()

    const formLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    };

    const { getFieldDecorator, resetFields } = form

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            align: 'center',
        },
        {
            title: '事件名称',
            dataIndex: 'eventNameCn',
            key: 'eventNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面中文名称',
            dataIndex: 'pageNameCn',
            key: 'pageNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面英文名称',
            dataIndex: 'pageNameEn',
            key: 'pageNameEn',
            align: 'center',
            render: (content, row) => {
                if (!content) return '--'
                if (content.length <= 30) return content;
                return (
                    <Tooltip placement="top" title={content}>
                        {`${content.slice(0, 30)}...`}
                    </Tooltip>
                )
            }   
        },
        {
            title: '事件类型',
            dataIndex: 'eventType',
            key: 'eventType',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
            render: (content, row) => {
                if (!content || content.length <= 15) return content;
                return (
                    <Tooltip placement="top" title={content}>
                        {`${content.slice(0, 15)}...`}
                    </Tooltip>
                )
            }    
        },
        {
            title: '创建人',
            dataIndex: 'createUser',
            key: 'createUser',
            align: 'center',
        },
        {
            title: '编辑人',
            dataIndex: 'updateUser',
            key: 'updateUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '最后编辑时间',
            dataIndex: 'gmtModified',
            key: 'gmtModified',
            align: 'center',
            render: (_, row) => {
                return moment(_).format('YYYY-MM-DD HH:mm:ss')
            }
        },
        {
            title: '操作',
            dataIndex: 'option',
            key: 'option',
            align: 'center',
            render: (_, row) => {
                return (
                    <div>
                      {isAuthBtn('connect') ? (
                        <Button
                          type="link"
                          onClick={() => connect(row)}
                        >
                            关联
                        </Button>
                      ) : null}
                      {isAuthBtn('update') ? (
                        <Button 
                            type="link" 
                            onClick={() => update(row)}>
                            编辑
                        </Button>
                      ) : null}
                      {isAuthBtn('delete') ? (
                        <Popconfirm
                            title="确定删除该条目么?"
                            onConfirm={() => { delItem(row) }}
                            onCancel={()=>{}}
                            okText="是"
                            cancelText="否"
                        >
                        <Button type="link">
                            删除
                        </Button>
                        </Popconfirm>
                      ) : null}
                    </div>
                  );
            },
        },
    ]

    const showTotal = Total => `共${Total}条`;

    /**
     * 列表数据查询
     * @param {*} value 
     */
    const queryData = value => {
        form.validateFields(async (err, values) => {
          const parmas = {
            eventNameCn:
                !values.eventNameCn || Array.isArray(values.eventNameCn)
                    ? undefined
                    : values.eventNameCn,
            pageNameCn: !values.pageNameCn || Array.isArray(values.pageNameCn) ? undefined : values.pageNameCn,
            pageNameEn:
                values.pageNameEn === '' || Array.isArray(values.pageNameEn) ? undefined : values.pageNameEn,
            updateUser:
                values.updateUser === '' || Array.isArray(values.updateUser)
                    ? undefined
                    : values.updateUser,
            pageNum: value.pageNum,
            pageSize: value.pageSize,
          };
          dispatch({
            type: 'relevance/fetchlistData',
            payload: parmas,
          });
        });
    };

    /**
     * 切换页码
     * @param {*} page 
     * @param {*} pageSize 
     */
     const handlePageChange = (page, pageSize) => {
        setCurrentPage(page);
        queryData({ pageNum: page, pageSize });
    };

    /**
     * 切换 pageSize
     * @param {*} current 
     * @param {*} pageSize 
     */
    const handlePageSizeChange = (current, pageSize) => {
        setPageSize(pageSize)
        queryData({ pageNum: currentPage, pageSize });
    };

    /**
     * 按钮权限确认
     * @param {*} key 
     * @returns 
     */
    const isAuthBtn = key => {
        return authList.includes(key);
    };

    /**
     * 触发关联事件
     * @param {*} row 
     */
    const connect = (row) => {
        setCurrentId(row.id)
        connDrawerRef.current && connDrawerRef.current.open();
    }

    /**
     * 编辑事件
     * @param {*} row 
     */
    const update = (row) => {
        setCurrentId(row.id)
        demoDrawerRef.current && demoDrawerRef.current.open({ type: 'update', ...row });
    }

    /**
     * 新建事件函数
     */
    const onAdd = (params) => {
        dispatch({
            type: 'relevance/add',
            payload: params,
            callback: () => {
                setCurrentPage(1)
                const newData = {
                  pageNum: 1,
                  pageSize: pageSize,
                };
                queryData(newData);
                demoDrawerRef.current && demoDrawerRef.current.close();
            }
        });
    }

    /**
     * 编辑事件函数
     */
     const onUpdate = (params) => {
        dispatch({
            type: 'relevance/update',
            payload: { ...params, id: currentId },
            callback: () => {
                const newData = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(newData);
                demoDrawerRef.current && demoDrawerRef.current.close();
            },
        });
    }

    /**
     * 删除条目函数
     * @param {*} row 
     */
    const delItem = (row) => {
        const params = {
            id: row.id,
            // TODO: 添加当前登录用户
            updateUser: userName
        }
        dispatch({
            type: 'relevance/delItem',
            payload: params,
            callback: () => {
                const newData = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(newData);
            }
        });
    }

    /**
     * 添加关联函数
     * @param {*} params 
     */
    const onAddConnect = (params, cb) => {
        dispatch({
            type: 'relevance/addConnect',
            payload: { ...params, eventId: currentId },
            callback: () => {
                cb();
                const newData = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(newData);
            }
        });
    }

    /**
     * 解除关联函数
     * @param {*} id 
     */
    const removeRef = (row) => {
        const params = {
            id: row.relId,
            updateUser: userName
        }
        dispatch({
            type: 'relevance/removeRef',
            payload: params,
            callback: () => {
                const newData = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(newData);
            }
        });
    }

    const expandedRowRender = (row, b, c, d) => {
        if (!row.eventDetails) {
            const currentEle = document.getElementsByClassName('ant-table-expanded-row')
            Array.from(currentEle).forEach( v => {
                const key = v.getAttribute('data-row-key')
                key === `${row.id}-extra-row` && (v.style.display = 'none')
            })
        }
        const data = listData.find(v => v.id === row.id).eventDetails
        const columns = [
            {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
                align: 'center',
            },
            {
                title: '埋点中文名称',
                dataIndex: 'eventNameCn',
                key: 'eventNameCn',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '埋点英文名称',
                dataIndex: 'eventNameEn',
                key: 'eventNameEn',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '页面中文名称',
                dataIndex: 'pageNameCn',
                key: 'pageNameCn',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '页面英文名称',
                dataIndex: 'pageNameEn',
                key: 'pageNameEn',
                align: 'center',
                render: (content, row) => {
                    if (!content) return '--'
                    if (content.length <= 30) return content;
                    return (
                        <Tooltip placement="top" title={content}>
                            {`${content.slice(0, 30)}...`}
                        </Tooltip>
                    )
                }   
            },
            {
                title: '事件来源',
                dataIndex: 'source',
                key: 'source',
                align: 'center',
                render: (_, row) => {
                    const value = EnumSourceOptions.find(v => v.value == _).label
                    return value
                }
            },
            {
                title: '操作',
                dataIndex: 'option',
                key: 'option',
                align: 'center',
                render: (_, row) => {
                    return (
                        <div>
                          {isAuthBtn('connect') ? (
                            <Popconfirm
                                title="确定解除关联么?"
                                onConfirm={() => { removeRef(row) }}
                                onCancel={()=>{}}
                                okText="是"
                                cancelText="否"
                            >
                                <Button type="link">
                                    解除关联
                                </Button>
                            </Popconfirm>
                          ) : null}
                        </div>
                      );
                },
            },
        ];
        return <Table 
                    columns={columns} 
                    style={{ margin: '10px' }}
                    bordered
                    rowKey="id" 
                    dataSource={data} 
                    pagination={false} 
                />;

    };

    const onExpand = (a, b) => {
        console.log(a, b)
    }

    useEffect(() => {
        const params = {
          pageNum: 1,
          pageSize: 10,
        };
        queryData(params);
    }, []);

    useEffect(() => {
        if (Array.isArray(curResources)) {
          curResources.map(c => {
            if (c.resourceKey === 'documents') {
              const authBtnList = authBtn(c.subList, 'relevance');
              setAuthList(authBtnList);
            }
          });
        }
      }, [curResources]);

    return (
        <PageHeaderWrapper
            title={false}
            content={
                <div className='wrapper'>
                    <Divider className='divider'></Divider>
                    <Form layout='inline' {...formLayout}>
                        <FormItem label="事件名称" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
                            {getFieldDecorator('eventNameCn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入事件名称" />)}
                        </FormItem>
                        <FormItem label="页面中文名称" labelCol={{ span: 11 }} wrapperCol={{ span: 13 }}>
                            {getFieldDecorator('pageNameCn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入页面中文名称" />)}
                        </FormItem>
                        <br />
                        <FormItem label="页面英文名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameEn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入页面英文名称" />)}
                        </FormItem>
                        <FormItem label="编辑人" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
                            {getFieldDecorator('updateUser', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入编辑人" />)}
                        </FormItem>
                        <FormItem style={{ marginLeft: '30px' }}>
                            <Button
                                type="primary"
                                size="default"
                                onClick={() => {
                                const params = {
                                    pageNum: currentPage,
                                    pageSize: pageSize,
                                };
                                queryData(params);
                                }}
                                loading={loading}
                            >
                                查询
                            </Button>
                        </FormItem>
                        <FormItem>
                            <Button
                                size="default"
                                onClick={() => {
                                    resetFields()
                                }}
                            >
                                重置
                            </Button>
                        </FormItem>
                        <FormItem>
                            <Button
                                type="primary"
                                size="default"
                                onClick={() => {
                                    demoDrawerRef.current && demoDrawerRef.current.open({ type: 'add' });
                                }}
                                loading={loading}
                            >
                                新建
                            </Button>
                        </FormItem>
                    </Form>
                    <Table
                        columns={columns}
                        expandedRowRender={(record, index, indent, expanded)=>expandedRowRender(record, index, indent, expanded)}
                        expandIcon={(props)=>{
                            if(props.record.eventDetails?.length > 0){
                                if (props.expanded) {
                                    return <a style={{ color: 'rgba(0, 0, 0, 0.65)' }} onClick={e => {
                                        props.onExpand(props.record, e);
                                    }}><Icon type="up" /></a>
                                } else {
                                    return <a style={{ color: 'rgba(0, 0, 0, 0.65)' }} onClick={e => {
                                        props.onExpand(props.record, e);
                                    }}><Icon type="down" /></a>
                                }
                            }else{
                                return <span style={{marginRight:8 }}></span>
                            }
                            }
                        }
                        defaultExpandAllRows={false}
                        onExpand={(expanded, record)=> onExpand(expanded, record)}
                        dataSource={listData}
                        style={{ marginTop: '20px' }}
                        bordered
                        rowKey="id"
                        loading={loading}
                        scroll={{ x: true, scrollToFirstRowOnChange: true }}
                        pagination={false}
                    />
                    <Pagination
                        style={{ float: 'right', marginTop: '20px' }}
                        showQuickJumper
                        showSizeChanger
                        disabled={total === 0}
                        showTotal={showTotal}
                        current={currentPage}
                        onChange={handlePageChange}
                        onShowSizeChange={handlePageSizeChange}
                        defaultPageSize={10}
                        total={total}
                        pageSizeOptions={['10', '20', '50', '100']}
                    />
                </div>
            }
        >
            <ModelComponent
                onClose={(type)=>{
                    
                }}
                onAdd={onAdd}
                onUpdate={onUpdate}
                ref={demoDrawerRef}
            >
            </ModelComponent>
            <ConnModelComponent
                onClose={()=>{
                    console.log('close')
                }}
                onAddConnect={onAddConnect}
                ref={connDrawerRef}
            >
            </ConnModelComponent>
        </PageHeaderWrapper>
    )

}

const RelevanceComponentForm = Form.create()(RelevanceComponent);

export default connect(({ relevance, loading, ssoAuthorityRole }) => ({
    listData: relevance.listData,
    total: relevance.total,
    loading: loading.effects['relevance/fetchlistData'],
    ssoAuthorityRole,
}))(RelevanceComponentForm);