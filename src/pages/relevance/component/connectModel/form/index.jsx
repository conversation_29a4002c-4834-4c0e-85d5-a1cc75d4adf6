/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-11 17:43:30
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-18 20:18:11
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/relevance/component/connectModel/form/index.jsx
 */
import { connect } from "dva"
import { Drawer, Form, Input, Button, Select, Row, Table, Pagination, notification, Tooltip } from "antd"
import React, { useEffect, useImperativeHandle, useReducer, useState } from "react"
import { EnumSourceOptions } from '../../../../../config/index'
import { getUser } from '@/utils/user';

const { Item : FormItem, useForm } = Form;
const { TextArea } = Input;

const Component = (props) => {

    const { form, dispatch, loading, detailListData = [], detailTotal, visible } = props;
    const { getFieldDecorator, resetFields, setFields } = form;
    const [currentPage, setCurrentPage] = useState(1);
    const [ pageSize, setPageSize ] = useState(10);
    const [ rowKeys, setRowKeys ] = useState([])
    const [ sourceVal, setSourceVal ] = useState('1')
    const [ignored, forceUpdate] = useReducer(x => x + 1, 0);

    const { name: userName } = getUser()

    const onDrawerClose = () => {
        props.onDrawerClose && props.onDrawerClose()
    }

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            key: 'id',
            align: 'center',
        },
        {
            title: '埋点中文名称',
            dataIndex: 'eventNameCn',
            key: 'eventNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '埋点英文名称',
            dataIndex: 'eventNameEn',
            key: 'eventNameEn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面中文名称',
            dataIndex: 'pageNameCn',
            key: 'pageNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面英文名称',
            dataIndex: 'pageNameEn',
            key: 'pageNameEn',
            align: 'center',
            render: (content, row) => {
                if (!content) return '--'
                if (content.length <= 30) return content;
                return (
                    <Tooltip placement="top" title={content}>
                        {`${content.slice(0, 30)}...`}
                    </Tooltip>
                )
            }   
        },
        {
            title: '埋点来源',
            dataIndex: 'source',
            key: 'source',
            align: 'center',
            render: (_, row) => {
                const value = EnumSourceOptions.find(v => v.value == _).label
                return value
            }
        },
        {
            title: '操作系统',
            dataIndex: 'os',
            key: 'os',
            align: 'center',
        },
    ]

    const showTotal = Total => `共${Total}条`;

    const handlePageChange = (page, pageSize) => {
        setCurrentPage(page);
        queryData({ pageNum: page, pageSize });
    };

    const handlePageSizeChange = (current, pageSize) => {
        setPageSize(pageSize)
        queryData({ pageNum: currentPage, pageSize });
    };

    /**
     * 列表查询
     * @param {*} value 
     */
    const queryData = value => {
        form.validateFields(async (err, values) => {
            const parmas = {
                source: value.source ? value.source : (!values.source
                    ? undefined
                    : Number(values.source)),
                eventNameCn: !values.eventNameCn || Array.isArray(values.eventNameCn) ? undefined : values.eventNameCn,
                eventNameEn:
                    values.eventNameEn === '' || Array.isArray(values.eventNameEn) ? undefined : values.eventNameEn,
                pageNameCn:
                    values.pageNameCn === '' || Array.isArray(values.pageNameCn)
                        ? undefined
                        : values.pageNameCn,
                pageNameEn:
                    values.pageNameEn === '' || Array.isArray(values.pageNameEn)
                        ? undefined
                        : values.pageNameEn,        
                pageNum: value.pageNum,
                pageSize: value.pageSize,
            };
            dispatch({
                type: 'relevance/getRefListData',
                payload: parmas,
            });
        });
    };

    const rowSelection = {
        onChange: (selectedRowKeys, selectedRows) => {
            setRowKeys(selectedRowKeys)
            forceUpdate()
            console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        },
        getCheckboxProps: record => ({
            disabled: record.name === 'Disabled User', // Column configuration not to be checked
            name: record.name,
        }),
        selectedRowKeys: rowKeys
    };

    /**
     * 添加关联
     */
    const addConnection = () => {
        if (rowKeys.length === 0) {
            notification.warn({
                message: '警告',
                description: '至少选择一条埋点数据',
            });
            return
        }
        const source = detailListData ? detailListData[0].source : '1'
        const params = {
            eventDetailIds: rowKeys,
            // TODO: 添加当前登录用户信息
            createUser: userName
        }
        props.onConnect && props.onConnect(params, () => {
            
            // 清空选中项id
            setRowKeys([])
            notification.success({
                message: '成功',
                description: Number(source) === 1 
                                ?   '全埋点关联成功'
                                :   '手工埋点关联成功',
            });
            queryData({
                pageNum: currentPage,
                pageSize: pageSize,
                source: source
            })
        })
    }

    /**
     * 埋点来源变更
     */
    const resetSearch = () => {
        resetFields()
        setFields({
            source: { value: sourceVal },
        })
    }

    useEffect(() => {
        if (visible === false) return
        setCurrentPage(1)
        // 清空查询条件
        resetFields()
        const params = {
            pageNum: 1,
            pageSize: 10,
            source: 1
        };
        queryData(params);
        // 清空选中项id
        setRowKeys([])
    }, [visible])

    return (
        <div>
            <Form name="eventForm" layout='inline'>
                <FormItem label="埋点来源" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                    {getFieldDecorator('source', {
                        rules: [],
                        initialValue: ['1'],
                    })(
                    <Select onChange={(value, option)=>{setSourceVal(value)}} placeholder="请选择埋点来源" style={{ width: '240px' }}>
                        {EnumSourceOptions.map(a => (
                            <Option value={a.value} key={a.value}>
                                {a.label}
                            </Option>
                        ))}
                    </Select>,
                    )}
                </FormItem>
                <br />
                <FormItem label="埋点中文名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                    {getFieldDecorator('eventNameCn', {
                        rules: [],
                        initialValue: [],
                    })(<Input style={{ width: '240px' }} placeholder="请输入埋点中文名称" />)}
                </FormItem>
                <FormItem label="埋点英文名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                    {getFieldDecorator('eventNameEn', {
                        rules: [],
                        initialValue: [],
                    })(<Input style={{ width: '240px' }} placeholder="请输入埋点英文名称" />)}
                </FormItem>
                <br />
                <FormItem label="页面中文名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                    {getFieldDecorator('pageNameCn', {
                        rules: [],
                        initialValue: [],
                    })(<Input style={{ width: '240px' }} placeholder="请输入页面中文名称" />)}
                </FormItem>
                <FormItem label="页面英文名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                    {getFieldDecorator('pageNameEn', {
                        rules: [],
                        initialValue: [],
                    })(<Input style={{ width: '240px' }} placeholder="请输入页面英文名称" />)}
                </FormItem>
                <Row type="flex" style={{ marginTop: '10px' }}>
                    <Button 
                        type="primary" 
                        onClick={() => {
                            const params = {
                                pageNum: currentPage,
                                pageSize: pageSize,
                            };
                            queryData(params);
                        }}
                    >
                        查询
                    </Button>
                    <Button style={{ marginLeft: '20px' }} onClick={resetSearch}>重置</Button>
                    <Button type="primary" style={{ marginLeft: '20px' }} onClick={addConnection}>添加关联</Button>
                </Row>
            </Form>
            <Table
                style={{ marginTop: '20px' }}
                columns={columns}
                rowKey="id"
                loading={loading}
                pagination={false}
                rowSelection={rowSelection}
                dataSource={detailListData}
                scroll={{ x: true, scrollToFirstRowOnChange: true }}
            ></Table>
            <Pagination
                style={{ float: 'right', marginTop: '20px' }}
                showQuickJumper
                showSizeChanger
                disabled={detailTotal === 0}
                showTotal={showTotal}
                current={currentPage}
                onChange={handlePageChange}
                onShowSizeChange={handlePageSizeChange}
                defaultPageSize={10}
                total={detailTotal}
            />
        </div>  
    )

}

const ComponentForm = Form.create()(Component)

export default connect(({ relevance, loading }) => ({
    detailListData: relevance.detailListData,
    detailTotal: relevance.detailTotal,
    loading: loading.effects['relevance/getRefListData'],
}))(ComponentForm)