/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-11 17:43:30
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-16 18:54:11
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/relevance/component/model/form/index.jsx
 */
import { connect } from "dva"
import { Drawer, Form, Input, Button, Select, Row } from "antd"
import React, { useEffect, useImperativeHandle, useState } from "react"
import { EnumEventTypeOptions } from '../../../../../config/index'
import { getUser } from '@/utils/user';
import { string } from "yargs";

const { Item : FormItem, useForm } = Form;
const { TextArea } = Input;

const FormComponent = (props) => {

    const { form, initData, type } = props;
    const { getFieldDecorator, setFieldsValue, setFields } = form;

    const onDrawerClose = () => {}

    const { name: userName } = getUser()

    /**
     * 确定事件
     */
    const onOk = () => {
        form.validateFields(async (err, values) => {
            if (err) return 
            const parmas = {
                eventNameCn:
                    !values.eventNameCn || Array.isArray(values.eventNameCn)
                        ? undefined
                        : values.eventNameCn,
                pageNameCn: !values.pageNameCn || Array.isArray(values.pageNameCn) ? undefined : values.pageNameCn,
                pageNameEn:
                    values.pageNameEn === '' || Array.isArray(values.pageNameEn) ? undefined : values.pageNameEn,
                eventType:
                    values.eventType === '' || Array.isArray(values.eventType) ? undefined : values.eventType,
                remark:
                    values.remark === '' || Array.isArray(values.remark) ? undefined : values.remark,
                createUser:
                    values.createUser === '' || Array.isArray(values.createUser)
                        ? undefined
                        : values.createUser,
                updateUser: userName
            };
            type === 'add'
                ?   (props.onAdd && props.onAdd(parmas))
                :   (props.onUpdate && props.onUpdate(parmas))
          });
    }

    const onChange = (e) => {
        console.log(e, 'e')
    }

    const checkCnData = (rule, value, callback) => {
        if (value) {
            if (/[^\u4E00-\u9FA5]/g.test(value)) {
              callback(new Error('只能输入中文!'));
            }
          }
          callback();
    }
    const checkEnData = (rule, value, callback) => {
        if (value) {
            if (/[^a-zA-Z\-_:\/.]/g.test(value)) {
              callback(new Error('只能输入英文字符串、"-"、"_"、":"、"/"、"."!'));
            }
          }
          callback();
    }

    useEffect(() => {
        if (initData) {
            const { 
                eventNameCn,
                pageNameCn, 
                pageNameEn, 
                eventType, 
                createUser,
                updateUser,
                remark
            } = initData
            setFields({
                eventNameCn: { value: eventNameCn },
                pageNameCn: { value: pageNameCn },
                pageNameEn: { value: pageNameEn },
                eventType: { value: eventType },
                createUser: { value: createUser ? createUser : userName },
                updateUser: { value: updateUser },
                remark: { value: remark }
            })
        }
    }, [initData])

    return (
        <Form name="eventForm">
            <FormItem required label="事件名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('eventNameCn', {
                    rules: [
                        { required: true, message: '事件名称不能为空' }
                    ],
                    initialValue: [],
                })(<Input maxLength={30} style={{ width: '260px' }} placeholder="请输入事件名称" />)}
            </FormItem>
            <FormItem required label="页面中文名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('pageNameCn', {
                    rules: [
                        { required: true, message: '页面中文名称不能为空' },
                        { validator: checkCnData, trigger: 'blur' }
                    ],
                    initialValue: [],
                })(<Input maxLength={30} style={{ width: '260px' }} placeholder="请输入页面中文名称" />)}
            </FormItem>
            <FormItem required label="页面英文名称" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('pageNameEn', {
                    rules: [
                        { required: true, message: '页面英文名称不能为空' },
                        { validator: checkEnData, trigger: 'blur' }
                    ],
                    initialValue: [],
                    // getValueFromEvent: (event) => {
                    //     return event.target.value.replace(/[^a-zA-Z-_]+/ig, '')
                    // }
                })(<Input maxLength={100} style={{ width: '260px' }} placeholder="请输入页面英文名称" />)}
            </FormItem>
            <FormItem required label="事件类型" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('eventType', {
                    rules: [
                        { required: true, message: '事件类型不能为空' }
                    ],
                    initialValue: [],
                })(
                <Select placeholder="请选择事件类型" style={{ width: '260px' }} allowClear>
                    {EnumEventTypeOptions.map(a => (
                        <Option value={a.value} key={a.value}>
                            {a.label}
                        </Option>
                    ))}
                </Select>,
                )}
            </FormItem>
            {
                type === 'add' 
                    ?   (<FormItem label="创建人" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                            {getFieldDecorator('createUser', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '260px' }} disabled={true} />)}
                        </FormItem>)
                    :   (<FormItem label="编辑人" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                            {getFieldDecorator('updateUser', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '260px' }} disabled={true} />)}
                        </FormItem>)
            }
            <FormItem label="备注" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
                {getFieldDecorator('remark', {
                    rules: [],
                    initialValue: [],
                })(<TextArea maxLength={200} style={{ width: '260px' }} placeholder="请输入备注" autoSize={{ minRows: 4, maxRows: 10 }} />)}
            </FormItem>
            <Row type="flex" justify="end" style={{ marginTop: '150px' }}>
                <Button onClick={()=>{ props.onClose && props.onClose() }}>取消</Button>
                <Button type="primary" style={{ marginLeft: '20px' }} onClick={onOk}>确定</Button>
            </Row>
        </Form>
    )

}

const FormComponentType = Form.create()(FormComponent)

export default FormComponentType