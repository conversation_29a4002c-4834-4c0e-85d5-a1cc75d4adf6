import { connect } from "dva"
import { Drawer, Form, Input, Button, Select, Row } from "antd"
import React, { useImperativeHandle, useState } from "react"
import { EnumEventTypeOptions } from '../../../../config/index'
import FormComponentType from './form/index'

const { Item : FormItem, useForm } = Form;
const { TextArea } = Input;

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-11 00:03:54
 * @LastEditors: <PERSON><PERSON> Wei
 * @LastEditTime: 2022-05-12 17:40:53
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/relevance/component/model/index.jsx
 */
const ModelComponent = React.forwardRef((props, ref) => {
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [ initData, setInitData ] = useState();
    const [ type, setType ] = useState('add')

    useImperativeHandle(ref,()=>({
        isVisible: ()=>{
            return drawerVisible
        },
        open : (data)=>{
            if(data) {
                const { type } = data;
                setType(type);
                delete data.type;
                setInitData(data);
            }
            setDrawerVisible(true)
        },
        close: () => {
            setDrawerVisible(false);
        },
        success: ()=>{
            setDrawerVisible(false);
            // formInstance.resetFields();
        }
    }))

    const onDrawerClose = () => {
        setDrawerVisible(false);
    }

    const onAdd = (params) => { props.onAdd && props.onAdd(params) }
    const onUpdate = (params) => { props.onUpdate && props.onUpdate(params) }

    return (
        <Drawer visible={drawerVisible} forceRender={true} onClose={onDrawerClose} width="33vw">
            <p style={{ fontSize: '30px', fontWeight: 'bold', lineHeight: '30px' }}>{type === 'add' ? '新建事件' : '编辑事件'}</p>
            <FormComponentType type={type} initData={initData} onAdd={onAdd} onUpdate={onUpdate} onClose={onDrawerClose}></FormComponentType>
        </Drawer>
    )

})

export default ModelComponent