/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-15 20:35:35
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-28 11:09:03
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/models/index.js
 */ 
import { notification, message } from 'antd';
import { getListData, getRefListData, getVersionList, add, update, updatePage, delItem, addConnect, onLine, queryDetail, queryBlockList, queryPageList } from '../services';

const UserModel = {
  namespace: 'eventManagement',
  state: {
    total: 0,
    listData: [],
	pageOptions: [],
	blockOptions: []
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
    *getRefListData({ payload }, { call, put }) {
        const response = yield call(getRefListData, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
          yield put({
            type: 'saveDetailsData',
            payload: Array.isArray(data.items) ? data.items : [],
          });
          yield put({
            type: 'saveDetailTotal',
            payload: data ? data.totalNum : 0,
          });
        } else {
          notification.warn({
            message: '请求列表失败',
            description: msg,
          });
        }
    },
    *fetchVersion({ payload }, { call, put }) {
      const response = yield call(getVersionList, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveVersionList',
          payload: Array.isArray(data.items) ? data.items : [],
        });
      } else {
        notification.warn({
          message: '版本号请求失败',
          description: msg,
        });
      }
    },
    *add({ payload, callback }, { call, put }) {
        const response = yield call(add, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: '事件创建成功！',
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *update({ payload, callback }, { call, put }) {
        const response = yield call(update, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            notification.success({
                message: '成功',
                description: msg,
            });
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *updatePage({ payload, callback }, { call, put }) {
      console.log('updatePage')
      const response = yield call(updatePage, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
          notification.success({
              message: '成功',
              description: msg,
          });
          callback()
      } else {
          notification.warn({
              message: '失败',
              description: msg,
          });
      }
    },
    *addConnect({ payload, callback }, { call, put }) {
        const response = yield call(addConnect, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
            callback()
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *queryDetail({ payload, callback }, { call, put }) {
        const response = yield call(queryDetail, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
           callback(data[0])
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
    *queryPageList({ payload, callback }, { call, put }) {
      const response = yield call(queryPageList, payload)
        if (!response) return;
        const { code, data, msg } = response;
        if (code === 1) {
          callback(data)
        } else {
            notification.warn({
                message: '失败',
                description: msg,
            });
        }
    },
	*queryBlockList({ payload, callback }, { call, put }) {
		const response = yield call(queryBlockList, payload)
		  if (!response) return;
		  const { code, data, msg } = response;
		  if (code === 1) {
        callback(data)
		  } else {
			  notification.warn({
				  message: '失败',
				  description: msg,
			  });
		  }
	  }
  },
  reducers: {
    saveListData(state, action) {
      	return { ...state, listData: action.payload }
    },
    saveTotal(state, action) {
      	return { ...state, total: action.payload }
    },
    saveDetailsData(state, action) {
        return { ...state, detailListData: action.payload }
    },
    saveDetailTotal(state, action) {
        return { ...state, detailTotal: action.payload }
    },
    saveVersionList(state, action) {
        return { ...state, versionOptions: action.payload }
    },
    saveDetail(state, action){
        return { ...state, eventDetail: action.payload }
    },
    savePageOptions(state, action) {
      	return { ...state, pageOptions: action.payload}
    },
	saveBlockOptions(state, action) {
		console.log(action.payload, 'block')
		return { ...state, blockOptions: action.payload}
  	}
  },
};
export default UserModel;