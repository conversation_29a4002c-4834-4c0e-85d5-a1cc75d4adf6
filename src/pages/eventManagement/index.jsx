/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-05 17:29:34
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-28 19:49:58
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/index.jsx
 */
import React, { useEffect, useState, useRef } from 'react'
import styles from './style.less'
import { Divider, Form, Select, Input, Button, Table, Pagination, Tooltip , Icon, Modal, Popconfirm } from 'antd'
import { connect } from 'dva'
import { authBtn } from '@/utils/utils';
import { getUser } from '@/utils/user';
import moment from 'moment';
import { PageHeaderWrapper } from '@ant-design/pro-layout'
import { EnumApplicationOptions, EnmuClientTypeOptions, EnumBusinessOptions, EnumStatusOptions } from '@/config'
import ModalComponent from './components/Modal'
import ConnModelComponent from './components/connectModel/index'
import viewDetails from '../blockManagement/components/viewDetails';

const eventManagement = ({ 
    dispatch,
    form,
    total = 0,
    listData = [],
    versionOptions = [],
    blockOptions = [],
    loading,
    ssoAuthorityRole: {
        data: { curResources = [] }
    }
}) => {
    const FormItem = Form.Item;
    const { Option } = Select;
    const [ appData, setAppData ] = useState([])
    const [ pfData, setPfData ] = useState([])
    const [ currentPage, setCurrentPage ] = useState(1);
    const [ pageSize, setPageSize ] = useState(10);
    const [ authList, setAuthList ] = useState([]);
    const [ currentId, setCurrentId ] = useState('');
    const [ onLineVisible, setOnLineVisible ] = useState(false)
    const [ currentRow, setCurrentRow ] = useState('')
    const modelComponent = useRef(null)
    const connDrawerRef = useRef(null)
    const { name: userName } = getUser()

    const formLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
    };

    const { getFieldDecorator, resetFields } = form

    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            key: 'index',
            align: 'center',
            render: (_, row, index) => {
                return (index+1) + ((currentPage-1) * pageSize)
            }
        },
        {
            title: '来源页面',
            dataIndex: 'pageNameCn',
            key: 'pageNameCn',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '页面ID',
            dataIndex: 'pageNameEn',
            key: 'pageNameEn',
            align: 'center',
            render: (content, row) => {
                if (!content) return '--'
                if (content.length <= 30) return content;
                return (
                    <Tooltip placement="top" title={content}>
                        {`${content.slice(0, 30)}...`}
                    </Tooltip>
                )
            }   
        },
        {
            title: '归属业务',
            dataIndex: 'businessName',
            key: 'businessName',
            align: 'center',
            render: (_, row) => {
                const value = EnumBusinessOptions.find(v => v.value == _).label
                return value
            }
        },
        {
            title: '生效版本',
            dataIndex: 'clientVersion',
            key: 'clientVersion',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '创建人',
            dataIndex: 'createUser',
            key: 'createUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            } 
        },
        {
            title: '编辑人',
            dataIndex: 'updateUser',
            key: 'updateUser',
            align: 'center',
            render: (_, row) => {
                if (!_) return '--'
                return _
            }
        },
        {
            title: '最后编辑时间',
            dataIndex: 'gmtModified',
            key: 'gmtModified',
            align: 'center',
            render: (_, row) => {
                return moment(_).format('YYYY-MM-DD HH:mm')
            }
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            align: 'center',
            render: (_, row) => {
                return (
                    <div>
                        {
                            _ === 0 ? (
                                <Button
                                    type='link'
                                    onClick={()=>{
                                        setCurrentRow(row)
                                        setOnLineVisible(true)
                                    }}
                                >
                                    开发中
                                </Button>
                            ) : <span style={{'color': 'chartreuse'}}>已上线</span>
                        }
                    </div>
                )
            }
        }
    ]

    /**
     * 二级表格渲染
     * @param {*} row 
     * @param {*} b 
     * @param {*} c 
     * @param {*} d 
     * @returns 
     */
    const expandedRowRender = (row, b, c, d) => {
        if (!row.btnDetails) {
            const currentEle = document.getElementsByClassName('ant-table-expanded-row')
            Array.from(currentEle).forEach( v => {
                const key = v.getAttribute('data-row-key')
                key === `${row.id}-extra-row` && (v.style.display = 'none')
            })
        }
        const data = listData.find(v => v.id === row.id).btnDetails
        const columns = [
            {
                title: '序号',
                dataIndex: 'index',
                key: 'index',
                align: 'center',
                render: (_, row, index) => {
                    return (index+1)
                }
            },
            {
                title: '按钮名称',
                dataIndex: 'btnName',
                key: 'btnName',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '按钮ID',
                dataIndex: 'btnId',
                key: 'btnId',
                align: 'center',
                render: (content, row) => {
                    if (!content) return '--'
                    if (content.length <= 30) return content;
                    return (
                        <Tooltip placement="top" title={content}>
                            {`${content.slice(0, 30)}...`}
                        </Tooltip>
                    )
                }   
            },
            {
                title: '来源区块',
                dataIndex: 'blockName',
                key: 'blockName',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '归属业务',
                dataIndex: 'businessName',
                key: 'businessName',
                align: 'center',
                render: (_, row) => {
                    const value = EnumBusinessOptions.find(v => v.value == _).label
                    return value
                }   
            },
            {
                title: '生效版本',
                dataIndex: 'clientVersion',
                key: 'clientVersion',
                align: 'center'
            },
            {
                title: '创建人',
                dataIndex: 'createUser',
                key: 'createUser',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '编辑人',
                dataIndex: 'updateUser',
                key: 'updateUser',
                align: 'center',
                render: (_, row) => {
                    if (!_) return '--'
                    return _
                }
            },
            {
                title: '最后编辑时间',
                dataIndex: 'gmtModified',
                key: 'gmtModified',
                align: 'center',
                render: (_, row) => {
                    return moment(_).format('YYYY-MM-DD HH:mm')
                }
            },
            {
                title: '状态',
                dataIndex: 'state',
                key: 'state',
                align: 'center',
                render: (_, row) => {
                    return (
                        <div>
                            {
                                _ === 0 ? (
                                    <Button
                                        type='link'
                                        onClick={()=>{
                                            setCurrentRow(row)
                                            setOnLineVisible(true)
                                        }}
                                    >
                                        开发中
                                    </Button>
                                ) : <span style={{'color': 'chartreuse'}}>已上线</span>
                            }
                        </div>
                    )
                }
            },
            {
                title: '操作',
                dataIndex: 'option',
                key: 'option',
                align: 'center',
                render: (_, row) => {
                    return (
                        <div>
                          {(isAuthBtn('update') && row.state === 0) ? (
                                <Button 
                                    type="link"
                                    onClick={() => {
                                        setCurrentId(row.id)
                                        modelComponent.current && modelComponent.current.open({ category: 'block', action: 'update', data: row, versionOptions: versionOptions, blockOptions: blockOptions }) 
                                    }}
                                >
                                    编辑
                                </Button>
                          ) : null}
                          {isAuthBtn('copy') ? (
                                <Button 
                                    type="link"
                                    onClick={() => {
                                        setCurrentId(row.id)
                                        modelComponent.current && modelComponent.current.open({ category: 'block', action: 'copy', data: row, versionOptions: versionOptions, blockOptions: blockOptions }) 
                                    }}
                                >
                                    复制
                                </Button>
                          ) : null}
                          {(isAuthBtn('del') && row.state === 0) ? (
                                <Popconfirm
                                    title="确定删除该按钮么?"
                                    onConfirm={() => { onDel(row) }}
                                    onCancel={()=>{}}
                                    okText="是"
                                    cancelText="否"
                                >
                                    <Button 
                                        type="link"
                                    >
                                        删除
                                    </Button>
                                </Popconfirm>
                          ) : null}
                          {isAuthBtn('view') ? (
                                <Button 
                                    type="link"
                                    onClick={() => {
                                        setCurrentId(row.id)
                                        viewDetail({
                                            category: 'block', 
                                            action: 'view', 
                                            id: row.id
                                        })
                                    }}
                                >
                                    查看详情
                                </Button>
                          ) : null}
                        </div>
                      );
                },
            },
        ];
        return <Table 
                    columns={columns} 
                    style={{ margin: '10px' }}
                    bordered
                    rowKey="id" 
                    dataSource={data} 
                    pagination={false} 
                />;

    };

    const onExpand = (a, b) => {
        console.log(a, b)
    }

    const showTotal = Total => `共${Total}条`;

    /**
     * 列表查询
     * @param {*} value 
     */
    const queryData = value => {
        form.validateFields(async (err, values) => {
          const parmas = {
            pageNameCn:
                !values.pageNameCn || Array.isArray(values.pageNameCn)
                    ? undefined
                    : values.pageNameCn,
            pageNameEn:
                !values.pageNameEn || Array.isArray(values.pageNameEn)
                    ? undefined
                    : values.pageNameEn,
            blockName: !values.blockName || Array.isArray(values.blockName) ? undefined : values.blockName,
            btnName:
                values.btnName === '' || Array.isArray(values.btnName) ? undefined : values.btnName,
            btnId:
                values.btnId === '' || Array.isArray(values.btnId)
                    ? undefined
                    : values.btnId,
            businessName:
                values.businessName === '' || Array.isArray(values.businessName) ? undefined : values.businessName,
            clientVersion: 
                values.clientVersion === '' || Array.isArray(values.clientVersion) ? undefined : values.clientVersion,
            state: 
                values.state === '' || Array.isArray(values.state) ? undefined : values.state,
            pageNum: value.pageNum,
            pageSize: value.pageSize,
          };
          dispatch({
            type: 'eventManagement/fetchlistData',
            payload: parmas,
          });
        });
    };

    const viewDetail = (params) => {
        dispatch({
            type: 'eventManagement/queryDetail',
            payload: { id: params.id },
            callback: (res) => {
                modelComponent.current && modelComponent.current.open({
                    ...params,
                    data: res
                })
            }
        })
    }

    /**
     * 新建
     * @param {object} values 
     */
    const onAdd = (values) => {
        dispatch({
            type: 'eventManagement/add',
            payload: Object.assign(values, { createUser: userName }),
            callback: () => {
                modelComponent.current && modelComponent.current.close()
                setCurrentPage(1);
                const query = {
                  pageNum: 1,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 编辑
     * @param {object} values 
     */
    const onUpdate = (values) => {
        const params = {
            id: currentId,
            ...values,
            updateUser: userName,
            operationType: 3
        }
        dispatch({
            type: 'eventManagement/update',
            payload: params,
            callback: () => {
                modelComponent.current && modelComponent.current.close()
                const query = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 删除
     */
    const onDel = (row) => {
        dispatch({
            type: 'eventManagement/update',
            payload: {
                id: row.id,
                businessName: row.businessName,
                clientVersion: row.clientVersion,
                updateUser: userName,
                operationType: 2
            },
            callback: () => {
                modelComponent.current && modelComponent.current.close()
                const query = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 埋点上线
     */
    const onLine = () => {
        const type = currentRow.btnName ? 'eventManagement/update' : 'eventManagement/updatePage'
        dispatch({
            type: type,
            payload: {
                id: currentRow.id,
                businessName: currentRow.businessName,
                clientVersion: currentRow.clientVersion,
                state: 1,
                updateUser: userName,
                operationType: 1
            },
            callback: () => {
                setOnLineVisible(false)
                const query = {
                  pageNum: currentPage,
                  pageSize: pageSize,
                };
                queryData(query);
            }
        });
    }

    /**
     * 触发关联事件
     */
    const connect = (row) => {
        setCurrentId(row.id)
        modelComponent.current && modelComponent.current.close();
        connDrawerRef.current && connDrawerRef.current.open({id: row.id});
    }

    /**
     * 添加关联
     */
    const onConnect = (params, cb) => {
        dispatch({
            type: 'eventManagement/addConnect',
            payload: { ...params, btnId: currentId },
            callback: () => {
                cb()
            }
        });
    }

    /**
     * 切换页码
     * @param {*} page 
     * @param {*} pageSize 
     */
    const handlePageChange = (page, pageSize) => {
        setCurrentPage(page);
        queryData({ pageNum: page, pageSize });
    };

    /**
     * 切换 pageSize
     * @param {*} current 
     * @param {*} pageSize 
     */
    const handlePageSizeChange = (current, pageSize) => {
        setPageSize(pageSize)
        queryData({ pageNum: currentPage, pageSize });
    };

    /**
     * 按钮权限确认
     * @param {*} key 
     * @returns 
     */
    const isAuthBtn = key => {
        return authList.includes(key);
    };

    useEffect(() => {
        dispatch({
            type: 'eventManagement/fetchVersion',
            payload: {
                pageSize: 1000,
                isDeleted: 0
            }
        });
    }, [])

    useEffect(() => {
        const params = {
          pageNum: 1,
          pageSize: 10,
        };
        queryData(params);
    }, []);

    useEffect(() => {
        if (Array.isArray(curResources)) {
          curResources.map(c => {
            if (c.resourceKey === 'documents') {
              const authBtnList = authBtn(c.subList, 'eventManagement');
              setAuthList(authBtnList);
            }
          });
        }
    }, [curResources]);

    return (
        <PageHeaderWrapper
            title={false}
            content={
                <div className='wrapper'>
                    <Divider className='divider'></Divider>
                    <Form layout='inline' {...formLayout}>
                        <FormItem label="页面名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameCn', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Input style={{ width: '160px' }} placeholder="请输入中文名称（path name）" />
                            )}
                        </FormItem>
                        <FormItem label="区块名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('blockName', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入event id" />)}
                        </FormItem>
                        <FormItem label="按钮名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('btnName', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入event id" />)}
                        </FormItem>
                        <FormItem label="页面ID" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('pageNameEn', {
                                rules: [],
                                initialValue: [],
                            })(<Input style={{ width: '160px' }} placeholder="请输入path url" />)}
                        </FormItem>
                        <br />
                        <FormItem label="归属业务" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('businessName', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择" style={{ minWidth: '160px' }} allowClear>
                                    {EnumBusinessOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.label}
                                        </Option>
                                    ))}
                                </Select>
                            )}
                        </FormItem>
                        <FormItem label="生效版本" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('clientVersion', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择" style={{ minWidth: '160px' }} allowClear>
                                    {versionOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.value}
                                        </Option>
                                    ))}
                                </Select>
                            )}
                        </FormItem>
                        <FormItem label="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;状态" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
                            {getFieldDecorator('state', {
                                rules: [],
                                initialValue: [],
                            })(
                                <Select placeholder="请选择状态" style={{ minWidth: '160px' }} allowClear>
                                    {EnumStatusOptions.map(d => (
                                        <Option value={d.value} key={d.value}>
                                            {d.label}
                                        </Option>
                                    ))}
                                </Select>
                            )}
                        </FormItem>
                        <FormItem style={{marginLeft: '40px'}}>
                            <Button
                                type="primary"
                                size="default"
                                onClick={() => {
                                    const params = {
                                        pageNum: 1,
                                        pageSize: pageSize,
                                    };
                                    setCurrentPage(1)
                                    queryData(params);
                                }}
                                loading={loading}
                            >
                                查询
                            </Button>
                        </FormItem>
                        <FormItem>
                            <Button
                                size="default"
                                onClick={() => {
                                    resetFields()
                                }}
                            >
                                重置
                            </Button>
                        </FormItem>
                    </Form>
                    <Divider className='divider'></Divider>
                    {
                        isAuthBtn('add') && (<Button 
                            type="primary"
                            onClick={() => {
                                modelComponent.current && modelComponent.current.open({ action: 'add', versionOptions: versionOptions, blockOptions: blockOptions })
                            }}
                        >新建按钮</Button>)
                    }
                    <Table
                        columns={columns}
                        expandedRowRender={(record, index, indent, expanded)=>expandedRowRender(record, index, indent, expanded)}
                        expandIcon={(props)=>{
                            if(props.record.btnDetails?.length > 0){
                                if (props.expanded) {
                                    return <a style={{ color: 'rgba(0, 0, 0, 0.65)' }} onClick={e => {
                                        props.onExpand(props.record, e);
                                    }}><Icon type="up" /></a>
                                } else {
                                    return <a style={{ color: 'rgba(0, 0, 0, 0.65)' }} onClick={e => {
                                        props.onExpand(props.record, e);
                                    }}><Icon type="down" /></a>
                                }
                            }else{
                                return <span style={{marginRight:8 }}></span>
                            }
                            }
                        }
                        defaultExpandAllRows={false}
                        dataSource={listData}
                        style={{ marginTop: '20px' }}
                        bordered
                        rowKey="id"
                        loading={loading}
                        scroll={{ x: true, scrollToFirstRowOnChange: true }}
                        pagination={false}
                    />
                    <Pagination
                        style={{ float: 'right', marginTop: '20px' }}
                        showQuickJumper
                        showSizeChanger
                        disabled={total === 0}
                        showTotal={showTotal}
                        current={currentPage}
                        onChange={handlePageChange}
                        onShowSizeChange={handlePageSizeChange}
                        defaultPageSize={10}
                        total={total}
                        pageSizeOptions={['10', '20', '50', '100']}
                    />
                    <ModalComponent ref={modelComponent} dispatch={dispatch} onAdd={onAdd} onUpdate={onUpdate} connect={connect}></ModalComponent>
                    <ConnModelComponent
                        onClose={()=>{
                            console.log('close')
                        }}
                        onConnect={onConnect}
                        ref={connDrawerRef}
                    >
                    </ConnModelComponent>    
                    <Modal
                        wrapClassName="event-modal"
                        visible={onLineVisible}
                        onOk={onLine}
                        onCancel={()=>{ setOnLineVisible(false) }}
                    >
                        <h2 style={{'textAlign': 'center'}}>确定上线吗？</h2>
                        <div style={{'textAlign': 'center'}}>(功能上线后不可撤回，请谨慎操作)</div>
                    </Modal>
                </div>
            }
        >
        </PageHeaderWrapper>
    )

}

const eventManagementForm = Form.create()(eventManagement);

export default connect(({ eventManagement, BlockManagement, loading, ssoAuthorityRole }) => ({
    listData: eventManagement.listData,
    total: eventManagement.total,
    versionOptions: eventManagement.versionOptions,
    blockOptions: eventManagement.blockOptions,
    loading: loading.effects['eventManagement/fetchlistData'],
    ssoAuthorityRole,
}))(eventManagementForm);