/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-20 16:35:29
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-28 14:57:49
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/components/eventForm.jsx
 */
import React, { useState, useEffect, useImperativeHandle } from 'react'
import { Form, Input, Select } from 'antd'
import { EnumBusinessOptions } from '@/config'

class EventOption extends React.Component {

    constructor(props) {
        super(props)
        this.props = props
        this.state = {
            blockOptions: props.blockList ? props.blockList : [],
            pageOptions: props.pageList ? props.pageList : [],
            businessName: props.detail ? props.detail.businessName : ''
        }
    }

    render () {
        const { form: { getFieldDecorator }, versionOptions, action } = this.props
        const { pageOptions, blockOptions } = this.state
        return (
            <Form name='pageForm'>
                <Form.Item label="归属业务" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('businessName', {
                            rules: [
                                {required: true, message: '归属业务不能为空'},
                            ],
                            initialValue: []
                        })(
                            <Select
                                disabled={action === 'update' || action === 'copy'} 
                                placeholder="请选择归属业务"
                                onChange={(value) => { this.onBusinessChange(value) }}
                            >
                                {EnumBusinessOptions.map(d => (
                                    <Option value={d.value} key={d.value}>
                                        {d.label}
                                    </Option>
                                ))}
                            </Select>
                        )
                    }
                </Form.Item>
                <Form.Item label="归属页面" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('pageNameCn', {
                            rules: [
                                {required: true, message: '归属页面不能为空'},
                            ],
                            initialValue: []
                        })(
                            <Select 
                                placeholder="请选择归属页面"
                                onChange={(value)=>{ this.onPageChange(value) }}
                            >
                                {pageOptions.map(d => (
                                    <Option value={d.pageNameCn} key={d.id}>
                                        {d.pageNameCn}
                                    </Option>
                                ))}
                            </Select>
                        )
                    }
                </Form.Item>
                <Form.Item label="归属区块" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('blockName', {
                            rules: [],
                            initialValue: []
                        })(
                            <Select placeholder="请选择归属区块">
                                {blockOptions.map(d => (
                                    <Option value={d.blockName} key={d.id}>
                                        {d.blockName}
                                    </Option>
                                ))}
                            </Select>
                        )
                    }
                </Form.Item>
                <Form.Item label="生效版本" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('clientVersion', {
                            rules: [
                                {required: true, message: '生效版本不能为空'},
                            ],
                            initialValue: []
                        })(
                            <Select placeholder="请选择生效版本">
                                {versionOptions.map(d => (
                                    <Option value={d.value} key={d.value}>
                                        {d.value}
                                    </Option>
                                ))}
                            </Select>
                        )
                    }
                </Form.Item>
                <Form.Item label="按钮名称" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('btnName', {
                            rules: [
                                {required: true, message: '按钮名称不能为空'},
                                {validator: this.checkCnData, trigger: 'blur'}
                            ],
                            initialValue: []
                        })(
                            <Input disabled={action === 'update' || action === 'copy'} maxLength={10} placeholder="请输入按钮名称" />
                        )
                    }
                </Form.Item>
                <Form.Item label="按钮ID" labelCol={{ span: 5 }} wrapperCol={{ span: 18 }}>
                    {
                        getFieldDecorator('btnId', {
                            rules: [
                                {required: true, message: '按钮ID不能为空'},
                                {validator: this.checkBtnId, trigger: 'blur'}
                            ],
                            initialValue: []
                        })(
                            <Input disabled={action === 'update' || action === 'copy'} placeholder="请输入按钮ID" />
                        )
                    }
                </Form.Item>
            </Form>
        )
    }

    componentDidMount(){ 
        const { form: { setFields }, action, detail } = this.props
        if (action !== 'add') {
            setFields({
                businessName: { value: detail.businessName },
                pageNameCn: { value: detail.pageNameCn },
                blockName: { value: detail.blockName },
                clientVersion: { value: detail.clientVersion },
                btnName: { value: detail.btnName },
                btnId: { value: detail.btnId },
            })
        }
    }

    onBusinessChange(value) {
        const { form: { setFields, resetFields }, dispatch } = this.props
        resetFields(['pageNameCn', 'blockName'])
        // 查询业务下的关联页面
        dispatch({
            type: 'eventManagement/queryPageList',
            payload: {
                businessName: value
            },
            callback: (res) => {
                this.setState({
                    pageOptions: res,
                    businessName: value,
                    blockOptions: []
                })
            }
        })
    }

    onPageChange(value, action, detail) {
        const { form: { setFields, resetFields }, dispatch } = this.props
        resetFields(['blockName'])
        // 查询页面下的关联区块
        dispatch({
            type: 'eventManagement/queryBlockList',
            payload: {
                pageNameCn: value,
                businessName: this.state.businessName
            },
            callback: (res) => {
                this.setState({
                    blockOptions: res
                })
            }
        })
    }

    checkBtnId(rule, value, callback) {
        if (value) {
            if (/\s/g.test(value)) {
              callback(new Error('按钮ID不能包含空格！'));
            }
        }
        callback();
    }

    checkCnData(rule, value, callback){
        if (value) {
            if (/[^\u4E00-\u9FA5]/g.test(value)) {
              callback(new Error('只能输入中文!'));
            }
          }
          callback();
    }

    getFieldValue () {
        const { form } = this.props;
        let fieldsValue = '';
        form.validateFields(async (err, values) => {
            if (err) return 
            fieldsValue = {
                pageNameCn: !values.pageNameCn
                            ? undefined
                            : (Array.isArray(values.pageNameCn) ? values.pageNameCn[0] : values.pageNameCn),
                blockName: !values.blockName
                            ? undefined
                            : (Array.isArray(values.blockName) ? values.blockName[0] : values.blockName),
                btnName: !values.btnName
                            ? undefined
                            : (Array.isArray(values.btnName) ? values.btnName[0] : values.btnName),
                btnId: !values.btnId
                            ? undefined
                            : (Array.isArray(values.btnId) ? values.btnId[0] : values.btnId),
                businessName: !values.businessName
                            ? undefined
                            : (Array.isArray(values.businessName) ? values.businessName[0] : values.businessName),
                clientVersion: !values.clientVersion
                            ? undefined
                            : (Array.isArray(values.clientVersion) ? values.clientVersion[0] : values.clientVersion),
            };
        });
        return fieldsValue;
    }

    reset(){
        const { form: { resetFields } } = this.props
        resetFields()
    }
}

export default Form.create()(EventOption)
