/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-05-11 17:26:24
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-27 18:04:54
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/components/connectModel/index.jsx
 */
import { connect } from "dva"
import { Drawer, Form, Input, Button, Select, Row } from "antd"
import React, { useImperativeHandle, useState } from "react"
import { EnumEventTypeOptions } from '../../../../config/index'
import ComponentForm from './form/index'
import styles from './style.less'

const { Item : FormItem } = Form;
const { TextArea } = Input;

const ConnModelComponent = React.forwardRef((props, ref) => {
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [eventID, setEventID] = useState('')

    useImperativeHandle(ref,()=>({
        isVisible: ()=>{
            return drawerVisible
        },
        open : (data)=>{
            if(data) {
                const { id } = data;
                setEventID(id)
                // formInstance.setFieldsValue({
                //     name,
                //     description,
                //     cover,
                //     index
                // })
            }
            setDrawerVisible(true)
        },
        close: () => {
            setDrawerVisible(false);
        },
        success: ()=>{
            setDrawerVisible(false);
            // formInstance.resetFields();
        }
    }))

    const onDrawerClose = () => {
        setDrawerVisible(false);
        props.onClose && props.onClose()
    }

    const onOk = () => {
    }

    /**
     * 添加关联
     */
    const onConnect = (params, cb) => {
        props.onConnect && props.onConnect(params, cb)
    }

    return (
        <Drawer visible={drawerVisible} forceRender={true} onClose={onDrawerClose} width="80vw" style={{ paddingBottom: '10px' }}>
            <p style={{ fontSize: '30px', fontWeight: 'bold', lineHeight: '30px' }}>埋点关联</p>
            <ComponentForm visible={drawerVisible} eventID={eventID} onDrawerClose={onDrawerClose} onConnect={onConnect}></ComponentForm>
        </Drawer>
    )

})

export default ConnModelComponent