/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-20 16:27:15
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-27 11:07:59
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/components/viewDetails.jsx
 */
import React, { useEffect } from 'react'
import { Form, Input, Row, Col, Button } from 'antd'
import { connect } from 'dva'
import styles from './viewDetails.less'
import { EnumBusinessOptions, EnumStatusOptions } from '@/config'

const ViewDetails = (props) => {

    const { category, detail } = props

    return (
        <div className='details'>
            <Row>
                <Col className='label' span={4}>归属页面</Col>
                <Col className='content' span={20}>{detail.pageNameCn}</Col>
            </Row>
            <Row>
                <Col className='label' span={4}>归属业务</Col>
                <Col className='content' span={20}>{EnumBusinessOptions.find(v => v.value == detail.businessName).label}</Col>
            </Row>
            <Row>
                <Col className='label' span={4}>归属区块</Col>
                <Col className='content' span={20}>{detail.blockName}</Col>
            </Row>
            <Row>
                <Col className='label' span={4}>生效版本</Col>
                <Col className='content' span={20}>{detail.clientVersion}</Col>
            </Row>
            <Row>
                <Col className='label' span={4}>按钮名称</Col>
                <Col className='content' span={20}>{detail.btnName}</Col>
            </Row>
            <Row>
                <Col className='label' span={4}>按钮ID</Col>
                <Col className='content' span={20}>{detail.btnId}</Col>
            </Row>
            <Row>
                <Col className='label' span={4}>取数来源</Col>
                <Col className='content' span={20}>
                    <Button
                        type='primary'
                        size='small'
                        onClick={() => {
                            props.connect && props.connect(detail)
                        }}
                    >添加关联</Button>
                </Col>
            </Row>
            <Row>
                {detail.relPages && detail.relPages.autoEventList.map(item => {
                    return (
                        <Col className='data-source' span={24}>{`全埋点：${item.pageNameEn}`}</Col>
                    )
                })}
                {detail.relPages && detail.relPages.handEventList.map(item => {
                    return (
                        <Col className='data-source' span={24}>{`手动埋点：${item.pageNameEn}`}</Col>
                    )
                })}
            </Row>
        </div>
        
    )

}

export default connect(({eventManagement}) => ({
    eventDetail: eventManagement.eventDetail
}))(ViewDetails)