/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-20 16:26:25
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-28 14:44:33
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/components/Modal.jsx
 */
import { Form, Modal, Input, Select } from "antd";
import React, { useState, useEffect, useImperativeHandle, useRef } from 'react'
import EventOptionForm from './eventForm'
import ViewDetails from "./viewDetails";

const ModalComponent = React.forwardRef((props, ref) => {
    
    const [ visible, setVisible ] = useState(false)
    const [ action, setAction ] = useState('')
    const [ detail, setDetail ] = useState('')
    const [ formRef, setFormRef ] = useState('')
    const [ versionOptions, setVersionOptions ] = useState([])
    const [ pageOptions, setPageOptions ] = useState([])
    const [ blockOptions, setBlockOptions ] = useState([])
    const { dispatch } = props

    const titleObj = {
        'add': '新建按钮',
        'update': '编辑按钮',
        'copy': '请选择复制后的归属区块',
        'view': '按钮详情'
    }

    useImperativeHandle(ref, () => ({
        open: (params) => {
            const { action, data, versionOptions, blockOptions } = params
            setDetail('')
            setBlockOptions('')
            // 操作 - 新建 / 编辑 / 复制 / 详情
            setAction(action)
            formRef && formRef.reset()
            versionOptions && setVersionOptions(versionOptions)
            data && setDetail(data)
            if (action === 'update' || action === 'copy') {
                dispatch({
                    type: 'eventManagement/queryPageList',
                    payload: {
                        businessName: data.businessName
                    },
                    callback: (res) => {
                        setPageOptions(res)
                        dispatch({
                            type: 'eventManagement/queryBlockList',
                            payload: {
                                pageNameCn: data.pageNameCn,
                                businessName: data.businessName
                            },
                            callback: (res) => {
                                setBlockOptions(res)
                                setVisible(true)
                            }
                        })
                    }
                })
            } else {
                setVisible(true)
            }
            
        },
        close: () => {
            setVisible(false)
        }
    }))

    /**
     * 保存
     */
    const onOk = () => {
        if (action === 'view') { 
            setVisible(false)
            return;
        }
        const fieldsValue = formRef.getFieldValue()
        if (fieldsValue) {
            (action === 'add' || action === 'copy') ? props.onAdd(fieldsValue) : props.onUpdate(fieldsValue)
        }
    }

    /**
     * 取消
     */
    const onCancel = () => {
        setVisible(false)
    }

    return (
        <Modal 
            visible={visible}
            title={titleObj[action]}
            centered={true}
            destroyOnClose={true}
            okText="保存"
            onOk={onOk}
            onCancel={onCancel}
        >
            {
                action === 'view' 
                    ? (<ViewDetails detail={detail} connect={props.connect}></ViewDetails>)
                    : (<EventOptionForm wrappedComponentRef={(form) => setFormRef(form)} dispatch={props.dispatch} action={action} detail={detail} versionOptions={versionOptions} pageList={pageOptions} blockList={blockOptions}></EventOptionForm>)
            }
        </Modal>
    )

})

export default ModalComponent
