/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-15 20:35:39
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-28 11:05:27
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/pages/eventManagement/services/index.js
 */
import request from '@/utils/request';

// 列表查询
export async function getListData(pamas) {
  return request('/admin/v1/eventlog/burypoint/pbb/btn/query', {
    method: 'POST',
    data: pamas,
  });
}

/**
 * 获取全埋点/手工埋点列表数据
 * @param {*} params 
 * @returns 
 */
export async function getRefListData(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/btn/queryMappingEventDetails', {
        method: 'POST',
        data: params,
    });
}

/**
 * 获取版本号数据
 * @param {*} params 
 * @returns 
 */
 export async function getVersionList(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/version/queryDetail', {
        method: 'POST',
        data: params,
    });
}

/**
 * 新建按钮
 * @param {*} params 
 * @returns 
 */
 export async function add(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/btn/add', {
        method: 'POST',
        data: params,
    });
}

/**
 * 编辑按钮
 * @param {*} params 
 * @returns 
 */
export async function update(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/btn/update', {
        method: 'POST',
        data: params,
    });
}

/**
 * 页面更新
 * @param {*} params 
 * @returns 
 */
 export async function updatePage(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/page/update', {
        method: 'POST',
        data: params,
    });
}

/**
 * 添加关联
 * @param {*} params 
 * @returns 
 */
export async function addConnect(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/btn/bindEventDetailAndBtn', {
        method: 'POST',
        data: params
    })
}

/**
 * 查询按钮详情
 * @param {*} params 
 * @returns 
 */
 export async function queryDetail(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/btn/queryDetail', {
        method: 'POST',
        data: params
    })
}

/**
 * 获取页面list
 * @param {*} params 
 * @returns 
 */
export async function queryPageList(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/page/queryDetail', {
        method: 'POST',
        data: params
    })
}

/**
 * 获取区块list
 * @param {*} params 
 * @returns 
 */
 export async function queryBlockList(params) {
    return request('/admin/v1/eventlog/burypoint/pbb/block/queryDetail', {
        method: 'POST',
        data: params
    })
}