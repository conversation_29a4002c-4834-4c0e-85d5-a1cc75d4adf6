import request from '@/utils/request';

// 获取业务组列表
export async function groupParamList(data) {
  return request('/admin/v1/eventlog/tracking/event/board/analyse/eventProperty/groupParamList', {
    method: 'POST',
    data,
  });
}

// 事件分析-查询事件列表
export async function eventListByStatus(data) {
  return request(
    '/admin/v1/eventlog/tracking/event/board/analyse/eventProperty/searchPageEventList',
    {
      method: 'POST',
      data,
    },
  );
}

// 事件分析-聚合列表
export async function aggListParams(data) {
  return request('/admin/v1/eventlog/tracking/event/board/analyse/eventProperty/aggList', {
    method: 'POST',
    data,
  });
}

// 事件属性查询接口
export async function eventParamList(data) {
  return request('/admin/v1/eventlog/tracking/event/board/analyse/eventProperty/eventParamList', {
    method: 'POST',
    data,
  });
}

// 查询参数枚举值
export async function searchParamValueList(data) {
  return request(
    '/admin/v1/eventlog/tracking/event/board/analyse/eventProperty/searchParamValueList',
    {
      method: 'POST',
      data,
    },
  );
}

// 事件分析提交
export async function dataSubmit(data) {
  return request('/admin/v1/eventlog/tracking/event/board/analyse/data/submit', {
    method: 'POST',
    data,
  });
}

// 事件分析结果内容
export async function dataReport(data) {
  return request('/admin/v1/eventlog/tracking/event/board/analyse/data/report', {
    method: 'POST',
    data,
  });
}
