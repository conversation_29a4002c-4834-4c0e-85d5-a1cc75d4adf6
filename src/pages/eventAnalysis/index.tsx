import React from 'react';
import { Tabs } from 'antd';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import styles from './components/index.less';
import EventAnalysisNew from '@/pages/eventAnalysis/components/eventAnalysisNew';

const { TabPane } = Tabs;
const EventAnalysis = ({ platformList }) => (
  <PageHeaderWrapper
    title={false}
    className={styles.pageHeader}
    content={
      // <Card style={{ padding: 16 }}>
      <div className={styles.pageCard}>
        <Tabs destroyInactiveTabPane={true}>
          {platformList.map(item => (
            <TabPane tab={item.applicationDesc} key={item.applicationType}>
              <EventAnalysisNew applicationType={item.applicationType} />
            </TabPane>
          ))}
        </Tabs>
      </div>
    }
  ></PageHeaderWrapper>
);

export default connect(({ user }) => ({
  platformList: user.platformList,
}))(EventAnalysis);
