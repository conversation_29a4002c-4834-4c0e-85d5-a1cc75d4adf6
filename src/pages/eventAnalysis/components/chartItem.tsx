import React, { useEffect, useRef, useState } from 'react';
import { Column, Line } from '@antv/g2plot';
import { Select } from '@blmcp/ui';
import { Tooltip } from 'antd';
import { BarChartOutlined, LineChartOutlined } from '@ant-design/icons';
import '@blmcp/ui/dist/lib/index.css';
import styles from './index.less';

const ChartItem = ({ data }) => {
  // const data = {
  //   sortBy: ['租户1', '租户3', '租户2'],
  //   isGroup: true,
  //   byEvents: [
  //     {
  //       index: 'A',
  //       eventName: '获取验证码点击',
  //     },
  //     {
  //       index: 'B',
  //       eventName: '获取验证码曝光',
  //     },
  //     {
  //       index: 'C',
  //       eventName: '事件C事件C事件C事件C事件C事件C事件C事件C事件C事件C事件C事件C',
  //     },
  //   ],
  //   analyseData: [
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码点击',
  //       title: '租户1',
  //       value: 10,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码点击',
  //       title: '租户2',
  //       value: 20,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码曝光',
  //       title: '租户1',
  //       value: 10,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码曝光',
  //       title: '租户2',
  //       value: 20,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码点击',
  //       title: '租户3',
  //       value: 35,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码点击',
  //       title: '租户4',
  //       value: 45,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码曝光',
  //       title: '租户3',
  //       value: 35,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '获取验证码曝光',
  //       title: '租户4',
  //       value: 45,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码曝光',
  //       title: '租户1',
  //       value: 15,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码曝光',
  //       title: '租户2',
  //       value: 25,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码点击',
  //       title: '租户1',
  //       value: 13,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码点击',
  //       title: '租户2',
  //       value: 23,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码曝光',
  //       title: '租户3',
  //       value: 33,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码曝光',
  //       title: '租户4',
  //       value: 43,
  //       index: 'B',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码点击',
  //       title: '租户3',
  //       value: 30,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-07',
  //       eventName: '获取验证码点击',
  //       title: '租户4',
  //       value: 40,
  //       index: 'A',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '事件C',
  //       title: '租户1',
  //       value: 10,
  //       index: 'C',
  //     },
  //     {
  //       dateTime: '2023-03-06',
  //       eventName: '事件C',
  //       title: '租户2',
  //       value: 20,
  //       index: 'C',
  //     },
  //   ],
  // };
  // 特殊处理折现图数据
  const processData = dataItem => {
    console.log(dataItem, 'dataItem');
    if (dataItem) {
      dataItem.forEach(item => {
        item.series = `${item.index}${item.eventName} - ${item.title}`;
        item.seriesEvent = `${item.index}${item.eventName}`;
      });
      return dataItem;
    }
  };
  // 当前图表类型
  const [chartType, setChartType] = useState(true);
  const [eventNum, setEventNum] = useState(10);
  const [chartData, setChartData] = useState(processData(data?.analyseData));
  // 柱状图事件图例
  const [eventLegend, setEventLegend] = useState(
    data.byEvents.map(item => `${item.index}${item.eventName}`),
  );
  // 柱状图事件项选择状态
  const [eventLegendStatus, setEventLegendStatus] = useState([{ event: '', status: true }]);
  // 选项条数筛选保存一份未根据事件分组数据(用于折线图)
  const [chartDataNum, setChartDataNum] = useState(processData(data?.analyseData));

  const barChartRef = useRef(null);
  const lineChartRef = useRef(null);

  // 数据筛选前n项
  const handleEventChange = e => {
    setEventNum(e);
  };

  // 图表类型转换
  const handleChartType = status => {
    setChartType(status);
  };
  // 前n项数据变更，重新处理数据源
  useEffect(() => {
    const sortByFilter = data?.sortBy.slice(0, eventNum);
    const filteredData = sortByFilter.reduce((acc, title) => {
      const matchingItems = data.analyseData.filter(entry => entry.title === title);
      acc.push(...matchingItems);
      return acc;
    }, []);
    setChartData(processData(filteredData));
    setChartDataNum(processData(filteredData));
  }, [eventNum]);

  useEffect(() => {
    if (barChartRef.current === null) {
      // 当前鼠标移入的分层项
      let hoverData = '';
      const barColumn = new Column('barChartColumn', {
        data: chartData,
        xField: 'dateTime',
        yField: 'value',
        isGroup: true,
        isStack: true,
        seriesField: data?.isGroup ? 'title' : 'seriesEvent',
        groupField: 'seriesEvent',
        interactions: [{ type: 'element-highlight' }],
        tooltip: {
          domStyles: {
            'g2-tooltip-marker': {
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              display: 'inline-block',
              marginRight: '8px',
            },
            'g2-tooltip-value': {
              display: 'inline-block',
              float: 'right',
              marginLeft: '30px',
            },
          },
          customContent: (title, items) => {
            // 过滤和处理数据
            const customContent = `
                <div class="g2-tooltip-title">${title}</div>
                <ul class="g2-tooltip-list">
                  ${[hoverData?.data]
                    .map(
                      item => `<li class="g2-tooltip-list-item">
                                    <span class="g2-tooltip-marker" style="background: ${hoverData.color}"></span>
                                    <span class="g2-tooltip-name">${hoverData?.data.series}</span>
                                    <span class="g2-tooltip-value">${hoverData?.data.value}</span>
                              </li>`,
                    )
                    .join('')}
                </ul>
            `;

            return customContent; // 返回自定义的 tooltip 内容
          },
          shared: false,
          enterable: true,
        },
        legend: {
          layout: data?.isGroup ? 'vertical' : 'horizontal',
          position: data?.isGroup ? 'right' : 'top-left',
          itemHeight: 16, // 设置图例项的高度
        },
      });
      // 监听鼠标移入的分层事件
      barColumn.on('element:mouseover', evt => {
        hoverData = evt.data;
      });

      barColumn.render();
      barChartRef.current = barColumn;
    }
    if (lineChartRef.current === null) {
      const lineColumn = new Line('lineChartColumn', {
        data: chartDataNum,
        xField: 'dateTime',
        yField: 'value',
        // isStack: true,
        seriesField: data?.isGroup ? 'series' : 'seriesEvent',
        smooth: true,
        tooltip: {
          formatter: datum => ({
            name: data?.isGroup ? datum.series : datum.seriesEvent,
            value: datum.value,
          }),
          domStyles: {
            'g2-tooltip': {
              maxHeight: '330px',
              overflowY: 'auto',
            },
          },
          enterable: true,
        },
        legend: {
          layout: 'horizontal',
          position: 'top-left',
          itemHeight: 16, // 设置图例项的高度
        },
      });
      lineColumn.render();
      lineChartRef.current = lineColumn;
    }
    return () => {
      if (barChartRef.current) {
        barChartRef.current.destroy();
      }
      if (lineChartRef.current) {
        lineChartRef.current.destroy();
      }
    };
  }, []);
  useEffect(() => {
    const newBarColumn = barChartRef.current;
    const newLineColumn = lineChartRef.current;
    newBarColumn.changeData(chartData);
    newBarColumn.render();
    newLineColumn.changeData(chartDataNum);
    newLineColumn.render();
  }, [chartData, chartDataNum]);
  useEffect(() => {
    // 维护当前柱状图事件选中状态
    const eventLegendStatusInit = data?.byEvents.map(item => ({
      event: `${item.index}${item.eventName}`,
      status: true,
    }));
    setEventLegendStatus(eventLegendStatusInit);
  }, [eventNum]);

  // 柱状图事件图例选择
  const handleEventSelected = selectedEvent => {
    // 当前事件选中状态
    const eventStatus = eventLegendStatus.find(obj => obj.event === selectedEvent)?.status;
    // 维护当前所有事件选中状态
    const newEventLegendStatus = eventLegendStatus.map(obj => {
      if (obj.event === selectedEvent) {
        return { ...obj, status: !obj.status };
      }
      return obj;
    });
    setEventLegendStatus(newEventLegendStatus);
    // 维护当前选中事件数组
    let newEventLegend = [];
    // 当前事件从选中-〉未选中 ,从选中数组中删除该项
    if (eventStatus) {
      newEventLegend = eventLegend.filter(item => item !== selectedEvent);
    } else {
      // 当前事件从未选中-> 选中，在选中数组中新增
      newEventLegend = [...eventLegend, selectedEvent];
    }
    setEventLegend(newEventLegend);
    // 根据当前选中事件过滤对应事件数据
    const filteredEventSelectedData = chartDataNum.filter(item =>
      newEventLegend.includes(`${item.index}${item.eventName}`),
    );
    setChartData(filteredEventSelectedData);
  };

  return (
    <>
      <div className={styles.chartSelect}>
        {data?.isGroup && data?.analyseData?.length >= 10 && (
          <Select
            className={styles.selectItem}
            defaultValue={eventNum}
            onChange={e => handleEventChange(e)}
            options={[
              { value: 10, label: '前10项' },
              { value: 20, label: '前20项' },
              { value: 50, label: '前50项' },
            ]}
          ></Select>
        )}
        <div className={styles.radioItem}>
          <span
            className={styles.radioSelected}
            style={{
              background: chartType ? '#FFFFFF' : '#EAEAEA',
              color: chartType ? '#303133' : '#606266',
            }}
            onClick={() => handleChartType(true)}
          >
            <LineChartOutlined rev="horizontal" />
            折线图
          </span>
          <span
            className={styles.radioSelected}
            style={{
              background: chartType ? '#EAEAEA' : '#FFFFFF',
              color: chartType ? '#606266' : '#303133',
              marginLeft: 2,
            }}
            onClick={() => handleChartType(false)}
          >
            <BarChartOutlined rev="horizontal" />
            柱状图
          </span>
        </div>
      </div>
      {!chartType && data?.isGroup && (
        <div className={styles.barEventLegend}>
          {data.byEvents.map(event => (
            <div
              className={styles.barEventLegendItem}
              onClick={() => handleEventSelected(`${event.index}${event.eventName}`)}
              style={{
                color: eventLegendStatus.find(
                  obj => obj.event === `${event.index}${event.eventName}`,
                )?.status
                  ? '#606266'
                  : 'rgba(0, 0, 0, 0.5)',
              }}
            >
              <span
                className={styles.serialNumberLegend}
                style={{
                  background: eventLegendStatus.find(
                    obj => obj.event === `${event.index}${event.eventName}`,
                  )?.status
                    ? '#2E7EFF'
                    : '#C0C4CC',
                }}
              >
                {event.index}
              </span>
              <Tooltip title={event.eventName}>
                <span>{event.eventName}</span>
              </Tooltip>
            </div>
          ))}
        </div>
      )}
      <div id="barChartColumn" style={{ display: chartType ? 'none' : 'block' }}></div>
      <div id="lineChartColumn" style={{ display: chartType ? 'block' : 'none' }}></div>
    </>
  );
};

export default ChartItem;
