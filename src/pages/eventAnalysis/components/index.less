
.pageHeader{
    background-color: #F7F8FA;
    padding: 0 8px;
    // border-radius: 8px 8px 0 0;
}
.pageCard{
    padding: 16px;
    background-color: #fff;
    border-radius: 8px 8px 0 0;
}
.eventChange{
    display: flex;
    justify-content: space-between;
    line-height: 24px;
    height: 56px;
    padding: 16px;
}
.selectPop{
    overflow: visible;
}
.serialNumber{
    width: 24px;
    height: 24px;
    // margin-bottom: 24px;
    padding: 1px 6px 1px 7px;
    color: #FFF;
    font-weight: 600;
    line-height: 22px;
    background-color: #2E7EFF;
    border-radius: 4px;
}
.itemDelete{
    color: #979797;
    &:hover{
        color: #303133;
        cursor: pointer;
    }
}
.itemDeleteIcon{
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url(../image/iconDelete.png);
    background-repeat: no-repeat;
}
.addFilter{
    // margin-bottom: 24px;
    color: #979797;
    &:hover{
        color: #2E7EFF;
        cursor: pointer;
    }
}
.leftLine{
    position: relative;
    padding-left: 48px;
    border-right: 2px solid #B3D0FF;
    margin: 8px 12px 8px 0;
}
.leftButton{
    position: absolute;
    width: 16px;
    height: 32px;
    // padding: 2px;
    color: #FFF;
    font-weight: 600;
    font-size: 12px;
    top: calc(50% - 16px);
    right: -9px;
    background: #EAF2FF;
    line-height: 12px;
    border-radius: 4px;
}
.relationButtonAnd{
    height: 16px;
    padding: 2px;
    border-radius: 4px 4px 0 0;
    &:hover{
        cursor: pointer;
    }
}
.relationButtonOr{
    height: 16px;
    padding: 2px;
    border-radius: 0 0 4px 4px;
    &:hover{
        cursor: pointer;
    }
}
.eventItem{
    padding:8px 0 8px 24px;
    &:hover{
        background: rgba(247, 248, 250, 0.8);
        // cursor: pointer;
    }
    :global(.ant-form-item-with-help){
        margin-bottom: 0;
    }
}
.paramsModal{
    display: flex;
    flex-Direction: column;
    flex: 1;
    :global(.ant-form-item-with-help){
        margin-bottom: 0;
    }
}
.paramsItem{
    width: 100%;
    padding: 8px 0 8px 12px;
    &:hover{
        background: rgba(247, 248, 250, 0.8);
    }
}
.addButton{
    width: 93px;
    height: 32px;
    color: #303133;
    font-size: 13px;
    margin-bottom: 16px;
    margin-left: 24px;
    padding: 6px 10px;
    text-align: center;
    background: rgba(126, 141, 165, 0.03);
    border: 1px dashed rgba(37, 52, 79, 0.12);
    border-radius: 4px;
    &:hover{
        color: #2E7EFF;
        background: rgba(126, 141, 165, 0.03);
        border:1px dashed rgba(46, 126, 255, 0.8);
        cursor: pointer;
    }
}
.eventExpand{
    color: #979797;
    &:hover{
        color: #2E7EFF;
        cursor: pointer;
    }
}
.dividerLast{
    margin: 16px 0;
    border-Width: 4px; 
    border-Color:#F7F8FA
}
.inputNumber{
    width: 130px;
    // border-width: 0px;
    border: none;
    // border-width: 1px;
    //     border-style: solid;
    //     border-color:transparent;
    background-color:rgba(37, 52, 79, 0.03) ;
    // :hover{
    //     background-color: #fff;
    //     border-width: 1px;
    //     border-color:#2761F3

    // }
    :focus{
        background-color: #fff;
        // border-width: 1px;
        // border-style: solid;
        border-color:#2761F3
    }
}

.chartSelect{
    display: flex;
    justify-content: end;
    padding-right: 12px;
    padding-bottom: 12px;
}

.selectItem{
    width: 160px;
    margin-right: 16px;
}
.radioItem{
    width: 154px;
    height: 32px;
    border-radius: 5px;
    background: #EAEAEA;
    padding: 2px;
}
.radioSelected{
    display: inline-block;
    height: 28px;
    text-align: center;
    line-height: 16px;
    width: 74px;
    padding: 6px;
    border-radius: 4px;
    &:hover{
        cursor: pointer;
    }
}
.chartPng{
    width: 64px;
    height: 42px;
    text-align: center;
    margin: 0 auto;
    margin-top: 80px;
}
.chartText{
    font-size: 13px;
    color: rgba(0, 0, 0, 0.6);
    text-align: center;
    margin-top: 20px;
}

.formError{
    :global(.ant-select-status-error){
        border: 1px solid red !important;
        border-radius: 6px
    }
    :global(.ant-form-item-explain-error){
        display: none;
    }
    :global(.ant-form-item-with-help){
        margin-bottom: 0;
    }
}

.inputFormError{
    :global(.ant-form-item-explain-error){
        display: none;
    }
    :global(.ant-form-item-with-help){
        margin-bottom: 0;
    }
    :global(.ant-input-number-input-wrap){
        background-color:rgba(37, 52, 79, 0.03);
        border-radius: 4px;
    }
    :global(.ant-input-number-status-error){
        border-width: 1px !important;
    }
    :global(.BLMInputNumber_Antd){
        border-width: 0;
    }
}
.loading{
    margin: 20px 0;
//   margin-bottom: 20px;
  padding: 30px 50px;
  text-align: center;
}
.barEventLegend{
    display: flex;
    margin-right: 8px;
    margin-bottom: 12px;
    &:hover{
        cursor: pointer;
    }
}
.barEventLegendItem{
    flex: 1 0 20%;
    max-width: 20%;
    padding: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.serialNumberLegend{
    display: inline-block;
    line-height: 10px;
    width: 14px;
    height: 14px;
    font-size: 12px;
    margin-right: 5px;
    padding: 2px 3px;
    color: #FFF;
    font-weight: 600;
    background-color: #2E7EFF;
    border-radius: 4px;
}