import React, { useEffect, useRef, useState } from 'react';
import { Cascader, Divider, Form, Input, InputNumber, message, Select, Space } from '@blmcp/ui';
import { DeleteOutlined, DownOutlined, PlusOutlined, UpOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import {
  aggListParams,
  eventListByStatus,
  eventParamList,
  searchParamValueList,
} from '../services/analysis';
import '@blmcp/ui/dist/lib/index.css';
import styles from './index.less';
import TenantSelectNew from '@/components/overViewTrackingCom/tenantSelectNew';

const EventItem = ({ form, eventIdItems, eventIdItemChange, businessType, applicationType }) => {
  const numberEnum = [
    { key: 0, value: 'A' },
    { key: 1, value: 'B' },
    { key: 2, value: 'C' },
    { key: 3, value: 'D' },
    { key: 4, value: 'E' },
  ];

  const refs = useRef([]);

  // 属性之间关系控制
  // 与事件相关联，添加事件时注入，删除事件时移除
  // 初始参数应该有值
  const [relationController, setRelationController] = useState([{ key: 0, value: 'AND' }]);
  // 事件展开收起状态
  const [eventExpand, setEventExpand] = useState(true);
  // 事件列表下拉框数据
  const [eventItemList, setEventItemList] = useState([{ key: 0, dataItem: [] }]);
  // 聚合分析数据列表
  const [aggList, setAggList] = useState([]);
  // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
  const [ruleMapItem, setRuleMapItem] = useState({});
  // 事件参数map集合（key为事件对应的key值，value为该事件下可选参数）
  const [eventRuleItem, setEventRuleItem] = useState({});
  // 全量事件数据
  const [allEventItemList, setAllEventItemList] = useState([]);

  const DEFAULTLIST = ['2$$1'];
  // 组件loading状态
  const [spinning, setSpinning] = useState(false);
  // 平台类型： 1-平台名称； 2-平台分组
  const [searchType, setSearchType] = useState(2);
  // 组件下拉数据
  const [tenantIdData, SetTenantIdData] = useState([]);
  // 选中的平台集合
  const [taskCarTeamIdSaved, setTaskCarTeamIdSaved] = useState([]);
  // 组件回调返回数据
  const [tenantCondition, setTenantCondition] = useState({
    eliminate: false,
    platformType: 2, // 平台类型
    selectIdList: DEFAULTLIST, // 选中集合的Id
    selectLabelList: [],
  });

  const getQueryListData = async (defaultVal = false, type = 2, field, subField) => {
    setSpinning(true);
    const formValue = form.getFieldsValue().events;
    const searchParams = {
      businessType,
      applicationType,
      fieldId: formValue[field.name].conditions[subField.name].ruleId[1],
      eventId: formValue[field.name].eventId[1],
      propertyClass: formValue[field.name].conditions[subField.name].propertyClass,
      limit: 100,
      searchType: type,
    };
    await searchParamValueList(searchParams)
      .then(res => {
        if (res.code === 1 && res.data) {
          const ruleMapValue = {
            ...ruleMapItem[`${field.key}_${subField.key}`],
            enumItem: res.data,
          };
          // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
          setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });

          let arr = [];
          res.data.forEach(item => {
            const info = {
              ...item,
              tenantId: type === 2 ? `2$$${item.value}` : item.value,
              tenantIdAndName: item?.labelValue || item?.label || '',
            };
            arr.push(info);
          });
          // 更新下拉平台数据
          SetTenantIdData(arr);
        }
      })
      .catch(e => {
        console.log(e);
      });
    setSpinning(false);
  };

  // 平台类型改变事件
  const switchTag = (type = 2, field, subField) => {
    // setSearchType(type);
    getQueryListData(false, type, field, subField);
  };
  // 平台的回调数据
  const selectCallBack = (info, field, subField) => {
    form.setFieldValue(
      ['events', field.name, 'conditions', subField.name, 'values'],
      info?.selectIdList,
    );
    form.setFieldValue(
      ['events', field.name, 'conditions', subField.name, 'valueType'],
      info?.platformType,
    );
    setTimeout(() => {
      const forms = form.getFieldsValue();
    }, 1000);

    // 更新平台类型
    setSearchType(info?.platformType);
    // 更新选中集合
    setTaskCarTeamIdSaved(info?.selectIdList);
    // 更新组件回调返回数据
    setTenantCondition(info);

    const ruleMapValue = {
      ...ruleMapItem[`${field.key}_${subField.key}`],
      values: info?.selectIdList,
      valueType: info.platformType,
    };
    // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
    setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
  };

  // 添加事件函数
  const handleEventsItemAdd = (add, fields) => {
    // 事件个数超过五次不允许添加事件
    if (relationController.length < 5) {
      // 事件添加
      add();
      // 添加对应事件的关系符
      const newParamsItems = { key: fields[fields.length - 1]?.name + 1, value: 'AND' };
      // 设置每个事件对应的关系符
      setRelationController([...relationController, newParamsItems]);
      setEventItemList([
        ...eventItemList,
        { key: fields[fields.length - 1]?.name + 1, dataItem: allEventItemList },
      ]);
      // 添加事件同时form 表单添加该事件关系符
      form.setFields([
        {
          name: ['events', fields.length, 'operator'],
          value: 'AND',
        },
      ]);
    } else {
      message.warning('最多支持添加5个事件');
    }
  };
  // 删除事件函数
  const handleEventsItemRemove = (remove, field) => {
    const removeController = [...relationController];

    // 移除对应事件关系符
    remove(field.name);
    // 移除对应事件的关系符，并将其与关系符重新排序
    const removeItemIndex = relationController.findIndex(item => item.key === field?.name);
    const removeControllerItems = removeController.splice(removeItemIndex, 1);
    const handleremoveController = removeController.map(item => {
      if (item.key > removeItemIndex) {
        return { ...item, key: item.key - 1 };
      }
      return item;
    });
    setRelationController(handleremoveController);
    // 删除对应事件item，涉及对应某事件的搜索
    const removeEventItemIndex = eventItemList.findIndex(item => item.key === field?.name);
    const removeEventItemList = eventItemList.filter(n => n.key !== field?.name);
    const handleRemoveEventItemList = removeEventItemList.map((item, i) => ({
      ...item,
      key: item.key > removeEventItemIndex ? item.key - 1 : item.key,
    }));
    setEventItemList(handleRemoveEventItemList);

    // 删除事件组合中对应事件id，用于分组查询
    const newEventIfItems = { ...eventIdItems };
    delete newEventIfItems[field.key];
    eventIdItemChange(newEventIfItems);
    // 删除事件组合中对应事件参数数据
    const newEventRuleItem = { ...eventRuleItem };
    delete newEventRuleItem[field.key];
    setEventRuleItem(newEventRuleItem);
  };
  // 关系运算符手动改变
  const handleRelationChange = (field, status) => {
    const updatedController = [...relationController];
    const targetItem = updatedController.find(item => item.key === field?.name);

    // 如果找到了匹配的项，可以修改其属性
    if (targetItem) {
      targetItem.value = status;
    }
    setRelationController(updatedController);
    form.setFields([
      {
        name: ['events', field.name, 'operator'],
        value: status,
      },
    ]);
  };
  // 获取事件列表数据/聚合列表数据
  useEffect(() => {
    const searchParams = {
      businessType,
      applicationType,
    };
    eventListByStatus({ ...searchParams, pageSize: 50, pageNum: 1 }).then(res => {
      if (res.code === 1 && res.data) {
        setEventItemList([{ key: 0, dataItem: res.data?.items }]);
        setAllEventItemList(res.data?.items);
      }
    });
    aggListParams(searchParams).then(res => {
      if (res.code === 1 && res.data) {
        setAggList(res.data);
      }
    });
    // 初次进入页面为第一个事件添加关系符
    form.setFields([
      {
        name: ['events', 0, 'operator'],
        value: 'AND',
      },
    ]);
  }, []);
  const handleItemAdd = field => {
    // 通过ref添加子级参数
    refs?.current[field.key]?.click();
  };

  // 事件参数 参数选择改变
  const handleRuleChange = (e, option, field, subField) => {
    // 设置事件参数是否私参属性
    form.setFields([
      {
        name: ['events', field.name, 'conditions', subField.name],
        value: {
          propertyClass: option && option[1].propertyClass,
          ruleId: e,
          fieldType: option && option[1].fieldType,
        },
      },
    ]);
    if (e) {
      if (option[1].fieldType === 'ENUM') {
        // 参数选择，若参数类型为枚举，直接请求枚举数据
        const searchParams = {
          businessType,
          applicationType,
          fieldId: e[1],
          eventId: eventIdItems[field.key],
          propertyClass: option[1].propertyClass,
          limit: 100,
          fuzzyStr: '',
          searchType: searchType,
        };
        searchParamValueList(searchParams)
          .then(res => {
            if (res.code === 1 && res.data) {
              const ruleMapValue = {
                fieldType: option[1].fieldType,
                propertyClass: option && option[1].propertyClass,
                comparator: option[1].comparator,
                enumItem: res.data,
              };
              // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
              setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
              let arr = [];
              res.data.forEach(item => {
                const info = {
                  ...item,
                  tenantId: searchType === 2 ? `2$$${item.value}` : item.value,
                  tenantIdAndName: item?.labelValue || item?.label || '',
                };
                arr.push(info);
              });
              // 更新下拉平台数据
              SetTenantIdData(arr);
            } else {
              const ruleMapValue = {
                fieldType: option[1].fieldType,
                propertyClass: option && option[1].propertyClass,
                comparator: option[1].comparator,
              };
              // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
              setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
            }
          })
          .catch(() => {
            const ruleMapValue = {
              fieldType: option[1].fieldType,
              propertyClass: option && option[1].propertyClass,
              comparator: option[1].comparator,
            };
            // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
            setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
          });
      } else {
        const ruleMapValue = {
          fieldType: option[1].fieldType,
          comparator: option[1].comparator,
        };
        // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
        setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
      }
    } else {
      const newRuleMapItem = { ...ruleMapItem };
      delete newRuleMapItem[`${field.key}_${subField.key}`];
      setRuleMapItem(newRuleMapItem);
    }
  };
  // 事件参数运算符改变
  const handleComparatorChange = (e, field, subField) => {
    const newRuleMapValue = {
      ...ruleMapItem[`${field.key}_${subField.key}`],
      comparatorSelected: e,
    };
    // 为map对象添加运算符选中字段
    setRuleMapItem({
      ...ruleMapItem,
      [`${field.key}_${subField.key}`]: newRuleMapValue,
    });
  };
  // 事件改变
  const handleEventListChange = (e, option, field) => {
    // 维护事件ID集合
    const newEventIfItems = {
      ...eventIdItems,
      [field.key]: e && e[1],
    };
    eventIdItemChange(newEventIfItems);
    if (e) {
      // 维护事件参数数据集合
      // 获取该事件下的参数集合
      const searchParams = {
        businessType,
        applicationType,
        eventId: e[1],
      };
      eventParamList(searchParams).then(res => {
        if (res.code === 1 && res.data) {
          setEventRuleItem({ ...eventRuleItem, [field.key]: res.data });
        } else {
          setEventRuleItem({ ...eventRuleItem, [field.key]: [] });
        }
      });
    } else {
      // 若为事件清空，同步清楚事件参数集合
      setEventRuleItem({ ...eventRuleItem, [field.key]: [] });
    }

    // 事件改变同步清除所有事件下所有参数
    form.setFields([
      {
        name: ['events', field.name],
        value: {
          operator: 'AND',
          eventId: e,
          aggregator: form.getFieldValue(['events', field.name])?.aggregator,
        },
      },
    ]);
    // 更新维护状态参数
    const updatedController = [...relationController];
    const targetItem = updatedController.find(item => item.key === field?.name);
    // 如果找到了匹配的项，可以修改其属性
    if (targetItem) {
      targetItem.value = 'AND';
    }
    setRelationController(updatedController);
    // 清除该事件下参数集合
    const updateRuleMapItem = new Map(Object.entries(ruleMapItem));
    const deleteRuleMapItem = { ...ruleMapItem };
    for (const key of updateRuleMapItem.keys()) {
      if (key.includes(`${field.key}_`)) {
        delete deleteRuleMapItem[key];
      }
    }
    setRuleMapItem(deleteRuleMapItem);
  };
  const handleChangeEventPageId = (value = '', key) => {
    // pageName
    const searchParams = {
      businessType,
      applicationType,
      searchWord: value,
    };
    const newEventItemList = [...eventItemList];
    eventListByStatus({ ...searchParams, pageSize: 50, pageNum: 1 }).then(res => {
      if (res.code === 1 && res.data) {
        newEventItemList.splice(key, 1, { key, dataItem: res.data?.items });
        setEventItemList(newEventItemList);
      }
    });
  };
  // 枚举值搜索
  const handleEnumPageId = (value = '', field, subField) => {
    const formValue = form.getFieldsValue().events;
    const searchParams = {
      businessType,
      applicationType,
      fieldId: formValue[field.name].conditions[subField.name].ruleId[1],
      eventId: formValue[field.name].eventId[1],
      propertyClass: formValue[field.name].conditions[subField.name].propertyClass,
      limit: 100,
      fuzzyStr: value,
    };
    searchParamValueList(searchParams).then(res => {
      if (res.code === 1 && res.data) {
        const ruleMapValue = {
          ...ruleMapItem[`${field.key}_${subField.key}`],
          enumItem: res.data,
        };
        // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
        setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
      }
    });
  };
  // 枚举变化
  const handleEnumChange = (field, subField) => {
    const formValue = form.getFieldsValue().events;
    // 枚举选择变化，重置下拉数据
    const searchParams = {
      businessType,
      applicationType,
      fieldId: formValue[field.name].conditions[subField.name].ruleId[1],
      eventId: formValue[field.name].eventId[1],
      propertyClass: formValue[field.name].conditions[subField.name].propertyClass,
      limit: 100,
    };
    searchParamValueList(searchParams).then(res => {
      if (res.code === 1 && res.data) {
        const ruleMapValue = {
          ...ruleMapItem[`${field.key}_${subField.key}`],
          enumItem: res.data,
        };
        // 拼凑参数类型map结构，键值为 ‘事件key_参数key’
        setRuleMapItem({ ...ruleMapItem, [`${field.key}_${subField.key}`]: ruleMapValue });
      }
    });
  };
  // 数值区间范围校验
  const resetBetweenErrorInfo = fieldName => {
    form.setFields([
      {
        name: fieldName,
        errors: [],
      },
    ]);
  };
  // 事件级联选中后只展示子级
  const handleDisplayRender = (labels, selectedOptions) => (
    <Tooltip title={`${labels[0]}/${labels[1]}`} placement="top">
      <span>{labels[1]}</span>
    </Tooltip>
  );
  return (
    <Form.Item noStyle style={{ width: '100%' }}>
      {/* 选择事件展开收起状态 */}
      <div className={styles.eventChange}>
        <span>
          选择事件<span style={{ color: '#979797' }}>（{relationController.length}/5）</span>
        </span>
        <span className={styles.eventExpand} onClick={() => setEventExpand(!eventExpand)}>
          {eventExpand ? (
            <>
              <UpOutlined rev="horizontal" />
              收起事件
            </>
          ) : (
            <>
              <DownOutlined rev="horizontal" />
              展开事件
            </>
          )}
        </span>
      </div>
      <Divider style={{ margin: '0 0 16px' }} />
      <Form.List name="events">
        {(fields, { add, remove }) => (
          <div
            style={{
              display: eventExpand ? 'flex' : 'none',
              // rowGap: 8,
              flexDirection: 'column',
            }}
          >
            {fields.map(field => (
              <>
                <Space key={field.key} className={styles.eventItem} align="center">
                  <div className={styles.serialNumber}>
                    {numberEnum.find(item => Number(item?.key) === Number(field.name)).value}
                    {/* {field.key} */}
                  </div>
                  <Form.Item
                    className={styles.formError}
                    name={[field.name, 'eventId']}
                    rules={[
                      {
                        required: true,
                        message: '请选择事件',
                      },
                    ]}
                  >
                    <Cascader
                      key={field.key}
                      style={{ width: 250 }}
                      showSearch
                      placeholder="请选择事件"
                      options={eventItemList[field.name]?.dataItem}
                      allowClear
                      popupClassName={styles.selectPop}
                      onChange={(e, option) => handleEventListChange(e, option, field)}
                      onSearch={value => handleChangeEventPageId(value, field.name)}
                      displayRender={handleDisplayRender}
                    />
                  </Form.Item>
                  <Form.Item
                    className={styles.formError}
                    name={[field.name, 'aggregator']}
                    rules={[
                      {
                        required: true,
                        message: '请选择指标',
                      },
                    ]}
                  >
                    <Select
                      style={{ width: 160 }}
                      showSearch
                      placeholder="请选择指标"
                      allowClear
                      options={aggList}
                      filterOption={(input, option) =>
                        option?.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    />
                  </Form.Item>
                  <div className={styles.addFilter} onClick={() => handleItemAdd(field)}>
                    <PlusOutlined rev="horizontal" />
                    添加参数
                  </div>
                  {fields.length > 1 && (
                    <div
                      className={styles.itemDelete}
                      onClick={() => {
                        handleEventsItemRemove(remove, field);
                      }}
                    >
                      <DeleteOutlined rev="horizontal" />
                      删除
                    </div>
                  )}
                </Space>
                <Form.List name={[field.name, 'conditions']}>
                  {(subFields, subOpt) => (
                    <>
                      <div
                        key={field.name}
                        ref={e => {
                          refs.current[field.key] = e;
                        }}
                        onClick={() => subOpt.add()}
                      ></div>
                      {/* 子级参数 */}
                      {subFields.length > 0 && (
                        <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
                          <div className={styles.leftLine}>
                            {subFields.length > 1 && (
                              <div className={styles.leftButton}>
                                <div
                                  className={styles.relationButtonAnd}
                                  style={{
                                    background:
                                      relationController.find(item => item.key === field?.name)
                                        ?.value === 'AND'
                                        ? '#2E7EFF'
                                        : '#EAF2FF',
                                    color:
                                      relationController.find(item => item.key === field?.name)
                                        ?.value === 'AND'
                                        ? '#FFFFFF'
                                        : '#606266',
                                  }}
                                  onClick={() => handleRelationChange(field, 'AND')}
                                >
                                  且
                                </div>
                                <div
                                  className={styles.relationButtonOr}
                                  style={{
                                    background:
                                      relationController.find(item => item.key === field?.name)
                                        ?.value === 'AND'
                                        ? '#EAF2FF'
                                        : '#2E7EFF',
                                    color:
                                      relationController.find(item => item.key === field?.name)
                                        ?.value === 'AND'
                                        ? '#606266'
                                        : '#FFFFFF',
                                  }}
                                  onClick={() => handleRelationChange(field, 'OR')}
                                >
                                  或
                                </div>
                              </div>
                            )}
                          </div>

                          <div className={styles.paramsModal}>
                            {subFields.map(subField => (
                              <Space key={subField.key} className={styles.paramsItem}>
                                <Form.Item
                                  className={styles.formError}
                                  name={[subField.name, 'ruleId']}
                                  rules={[
                                    {
                                      required: true,
                                      message: '请选择参数',
                                    },
                                  ]}
                                >
                                  <Cascader
                                    style={{ width: 250 }}
                                    options={eventRuleItem[field.key]}
                                    placeholder="请选择参数"
                                    allowClear
                                    popupClassName={styles.selectPop}
                                    onChange={(e, option) =>
                                      handleRuleChange(e, option, field, subField)
                                    }
                                    displayRender={handleDisplayRender}
                                  />
                                </Form.Item>
                                {ruleMapItem[`${field.key}_${subField.key}`] && (
                                  <Form.Item
                                    className={styles.formError}
                                    name={[subField.name, 'comparator']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请选择运算符',
                                      },
                                    ]}
                                  >
                                    <Select
                                      style={{ width: 160 }}
                                      placeholder="请选择运算符"
                                      allowClear
                                      options={
                                        ruleMapItem[`${field.key}_${subField.key}`]?.comparator
                                      }
                                      onChange={e => handleComparatorChange(e, field, subField)}
                                    />
                                  </Form.Item>
                                )}
                                {ruleMapItem[`${field.key}_${subField.key}`]?.fieldType ===
                                  'ENUM' && (
                                  <Form.Item
                                    className={styles.formError}
                                    name={[subField.name, 'values']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请选择枚举值',
                                      },
                                    ]}
                                  >
                                    {ruleMapItem[`${field.key}_${subField.key}`]?.propertyClass ===
                                    'PUBLIC' ? (
                                      <TenantSelectNew
                                        form={form}
                                        placeholder={'平台名称'}
                                        spinning={spinning}
                                        valueType={
                                          ruleMapItem[`${field.key}_${subField.key}`]?.valueType ??
                                          2
                                        }
                                        excludedDomainShow={false}
                                        // platformEliminate={tenantCondition?.eliminate}
                                        tenantOptions={tenantIdData}
                                        defaultValueIds={
                                          form.getFieldsValue().events[field.name]?.conditions[
                                            subField.name
                                          ]?.values ?? []
                                        }
                                        switchTag={type => switchTag(type, field, subField)}
                                        selectCallBack={info =>
                                          selectCallBack(info, field, subField)
                                        }
                                      ></TenantSelectNew>
                                    ) : (
                                      <Select
                                        style={{ width: 160 }}
                                        placeholder="请选择枚举值"
                                        allowClear
                                        showSearch
                                        onFocus={() => handleEnumPageId('', field, subField)}
                                        onSearch={value => handleEnumPageId(value, field, subField)}
                                        onChange={() => handleEnumChange(field, subField)}
                                        filterOption={false}
                                        options={
                                          ruleMapItem[`${field.key}_${subField.key}`]?.enumItem
                                        }
                                      />
                                    )}
                                  </Form.Item>
                                )}
                                {ruleMapItem[`${field.key}_${subField.key}`]?.fieldType ===
                                  'STRING' && (
                                  <Form.Item
                                    className={styles.formError}
                                    name={[subField.name, 'values']}
                                    rules={[
                                      {
                                        required: true,
                                        message: '请输入',
                                      },
                                    ]}
                                  >
                                    <Input
                                      style={{ width: 160, paddingLeft: 10 }}
                                      allowClear
                                      placeholder="请输入"
                                    ></Input>
                                  </Form.Item>
                                )}
                                {ruleMapItem[`${field.key}_${subField.key}`]?.fieldType === 'INT' &&
                                  form.getFieldValue([
                                    'events',
                                    field.name,
                                    'conditions',
                                    subField.name,
                                    'comparator',
                                  ]) !== 'BETWEEN' && (
                                    <Form.Item
                                      className={styles.inputFormError}
                                      name={[subField.name, 'values']}
                                      rules={[
                                        {
                                          required: true,
                                          message: '请输入',
                                        },
                                      ]}
                                    >
                                      <InputNumber precision={0} min={-999999999} max={999999999} />
                                    </Form.Item>
                                  )}
                                {ruleMapItem[`${field.key}_${subField.key}`]?.fieldType === 'INT' &&
                                  form.getFieldValue([
                                    'events',
                                    field.name,
                                    'conditions',
                                    subField.name,
                                    'comparator',
                                  ]) === 'BETWEEN' && (
                                    <>
                                      <Form.Item
                                        className={styles.inputFormError}
                                        name={[subField.name, 'values']}
                                        rules={[
                                          {
                                            required: true,
                                            message: '请输入',
                                          },
                                          {
                                            validator: (rule, value, callback, values) => {
                                              const secondValue = form.getFieldsValue().events[
                                                field.name
                                              ].conditions[subField.name].valuesBetween; // 获取第一个InputNumber的值
                                              if (+secondValue <= +value || !value) {
                                                callback('第一个数值必须小于第二个数值'); // 校验失败
                                              } else if (value) {
                                                resetBetweenErrorInfo([
                                                  'events',
                                                  field.name,
                                                  'conditions',
                                                  subField.name,
                                                  'valuesBetween',
                                                ]);
                                                callback();
                                              } else {
                                                callback();
                                              }
                                            },
                                          },
                                        ]}
                                      >
                                        <InputNumber
                                          precision={0}
                                          min={-999999999}
                                          max={999999999}
                                          // defaultValue={0}
                                        />
                                      </Form.Item>
                                      -
                                      <Form.Item
                                        className={styles.inputFormError}
                                        name={[subField.name, 'valuesBetween']}
                                        rules={[
                                          {
                                            required: true,
                                            message: '请输入',
                                          },
                                          {
                                            validator: (rule, value, callback, values) => {
                                              const firstValue = form.getFieldsValue().events[
                                                field.name
                                              ].conditions[subField.name].values; // 获取第一个InputNumber的值
                                              if (+value <= +firstValue || !value) {
                                                callback('第二个数值必须大于第一个数值'); // 校验失败
                                              } else if (value) {
                                                resetBetweenErrorInfo([
                                                  'events',
                                                  field.name,
                                                  'conditions',
                                                  subField.name,
                                                  'values',
                                                ]);
                                                callback();
                                              } else {
                                                callback();
                                              }
                                            },
                                          },
                                        ]}
                                      >
                                        <InputNumber
                                          precision={0}
                                          min={-999999999}
                                          max={999999999}
                                          // defaultValue={0}
                                        />
                                      </Form.Item>
                                    </>
                                  )}

                                <div
                                  className={styles.itemDelete}
                                  onClick={() => {
                                    subOpt.remove(subField.name);
                                  }}
                                >
                                  <DeleteOutlined rev="horizontal" />
                                  删除
                                </div>
                              </Space>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </Form.List>
              </>
            ))}

            <div className={styles.addButton} onClick={() => handleEventsItemAdd(add, fields)}>
              <PlusOutlined rev="horizontal" />
              添加事件
            </div>
            <Divider style={{ margin: '0 0 16px' }} />
          </div>
        )}
      </Form.List>
    </Form.Item>
  );
};

export default EventItem;
