import React, { useEffect, useState } from 'react';
import { But<PERSON>, DatePicker, Divider, Form, Select, Spin } from '@blmcp/ui';
import '@blmcp/ui/dist/lib/index.css';
import dayjs from 'dayjs';
import ChartItem from './chartItem';
import EventItem from './eventItem';
import { dataReport, dataSubmit, groupParamList } from '../services/analysis';
import chartPng from '@/assets/chart.png';
import styles from './index.less';

const EventAnalysis = ({ applicationType }) => {
  const { RangePicker } = DatePicker;
  const businessType = JSON.parse(sessionStorage.getItem('businessType'));
  const numberEnum = [
    { key: 0, value: 'A' },
    { key: 1, value: 'B' },
    { key: 2, value: 'C' },
    { key: 3, value: 'D' },
    { key: 4, value: 'E' },
  ];
  const formItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 8 },
  };
  const [form] = Form.useForm();
  // 分组数据
  const [groupData, setGroupData] = useState([]);
  // 选中分组数据
  const [groupSelected, setGroupSelected] = useState({
    fieldValue: '',
    id: '',
    propertyClass: '',
    seq: '',
  });
  // 事件id集合
  const [eventIdItems, setEventIdItems] = useState({});
  // 事件分析结果接口轮训
  const [pollingActive, setPollingActive] = useState(false);
  // 存储事件提交接口返回值
  const [analyseDataReport, setAnalyseDataReport] = useState({});
  // 存储事件分析接口返回值
  const [analyseDataFin, setAnalyseDataFin] = useState({});
  // 开始分析loading按钮
  const [analysisLoading, setAnalysisLoading] = useState(false);
  // 是否为第一次搜索
  const [firstSubmit, setFirstSubmit] = useState(true);

  // 存储内部form表单
  // 事件展开收起状态
  // 仅获取表单变动值
  const onRequiredTypeChange = e => {
    console.log(e, 'valueform');
  };
  // 设置时间禁用项
  const disabledDate = current => {
    // 过去90天
    const historyTime = dayjs()
      .subtract(90, 'day')
      .startOf('day');
    const todayTime = dayjs()
      .subtract(0, 'day')
      .startOf('day');
    // 选择过去90的数据 current会精确到时分秒去比较
    return current && (current < historyTime || current > todayTime);
  };
  useEffect(() => {
    if (pollingActive && analyseDataReport?.data?.length) {
      const subInterval = setInterval(() => {
        dataReport({ queryId: analyseDataReport?.data })
          .then(subRes => {
            if (subRes.code === 1 && subRes.data) {
              setPollingActive(false);
              setAnalyseDataFin(subRes?.data);
              setAnalysisLoading(false);
            } else if (subRes.code !== 1) {
              setPollingActive(false);
              setAnalysisLoading(false);
            }
          })
          .catch(() => {
            setPollingActive(false);
            setAnalysisLoading(false);
          });
      }, 1000);
      return () => {
        clearInterval(subInterval);
      };
    }
  }, [pollingActive, analyseDataReport]);
  // 开始分析按钮提交函数
  const handleEventDetail = () => {
    form
      .validateFields()
      .then(formValue => {
        // 点击搜索后不再展示初始状态
        if (firstSubmit) {
          setFirstSubmit(false);
        }
        setAnalysisLoading(true);
        const eventSubmit = formValue.events.map((item, index) => {
          const eventSubmitItem =
            item?.conditions &&
            item.conditions.map(subItem => {
              // const dealValue = subItem.value;
              let dealValue = [];
              // 若参数类型为数值运算符为范围，特殊定义values
              if (subItem?.fieldType === 'INT' && subItem?.comparator === 'BETWEEN') {
                dealValue = [String(subItem.values), String(subItem.valuesBetween)];
              } else {
                if (subItem?.propertyClass === 'PUBLIC') {
                  if (subItem?.valueType === 2) {
                    let arr = [];
                    subItem?.values?.forEach(item => {
                      let parts = item.split('$$');
                      parts?.[1] && arr.push(parts?.[1]);
                    });
                    dealValue = arr;
                  } else {
                    dealValue = subItem.values;
                  }
                } else {
                  dealValue = [String(subItem.values)];
                }
              }
              return {
                ruleId: subItem?.ruleId[1],
                values: dealValue,
                valueType: subItem?.valueType,
                propertyClass: subItem?.propertyClass,
                comparator: subItem?.comparator,
              };
            });
          return {
            eventId: item?.eventId[1],
            aggregator: item.aggregator,
            index: numberEnum.find(numItem => Number(numItem?.key) === Number(index)).value,
            filter:
              eventSubmitItem?.length > 0
                ? {
                    gop: '1',
                    containerList: [
                      {
                        containerId: '1',
                        operator: item.operator,
                        conditions: eventSubmitItem,
                      },
                    ],
                  }
                : undefined,
          };
        });
        const params = {
          businessType,
          applicationType,
          groupName: groupSelected?.fieldValue,
          groupId: groupSelected?.id,
          propertyClass: groupSelected?.propertyClass,
          startTime: formValue.eventDate[0].format('YYYY-MM-DD'),
          endTime: formValue.eventDate[1].format('YYYY-MM-DD'),
          events: eventSubmit,
        };
        dataSubmit(params)
          .then(res => {
            if (res.code === 1 && res.data) {
              setPollingActive(true);
              setAnalyseDataReport(res);
            } else {
              setAnalysisLoading(false);
            }
          })
          .catch(() => {
            setAnalysisLoading(false);
          });
      })
      .catch(err => {
        console.log(err, 'err', form.getFieldsError());
      });
  };
  // 分组属性点击查询
  const handleGroupChange = (e, option) => {
    if (e) {
      setGroupSelected(option);
    } else {
      setGroupSelected({ fieldValue: '', id: '', propertyClass: '', seq: '' });
    }
  };
  // 事件id组合维护
  const handleEventIdItems = state => {
    setEventIdItems(state);
  };

  // 获取分组租户数据，租户数据会根据事件选择而改变（仅事件id变化需变更）
  useEffect(() => {
    const searchParams = {
      businessType,
      applicationType,
      eventIds: Object.values(eventIdItems).filter(value => value !== undefined),
    };
    groupParamList(searchParams).then(res => {
      if (res.code === 1 && res.data) {
        setGroupData(res.data);
      }
    });
  }, [eventIdItems]);

  // 分组数据源变更，重置分组选择项
  useEffect(() => {
    form.setFields([
      {
        name: 'groupValue',
        value: undefined,
      },
    ]);
    setGroupSelected({
      fieldValue: '',
      id: '',
      propertyClass: '',
      seq: '',
    });
  }, [groupData]);

  return (
    <>
      <Form
        name="EventAnalysis"
        form={form}
        autoComplete="off"
        initialValues={{ events: [{}] }}
        labelAlign="right"
        layout="inline"
        {...formItemLayout}
        style={{ marginTop: -16 }}
        onValuesChange={onRequiredTypeChange}
      >
        <div style={{ width: '100%' }}>
          <EventItem
            form={form}
            eventIdItems={eventIdItems}
            eventIdItemChange={handleEventIdItems}
            businessType={businessType}
            applicationType={applicationType}
          />
        </div>
        <Form.Item
          style={{ marginBottom: 16, padding: '0 16px' }}
          label="时间范围"
          name="eventDate"
          initialValue={[dayjs().subtract(15, 'day'), dayjs().subtract(1, 'days')]}
          rules={[
            {
              required: true,
              message: '请选择时间范围',
            },
          ]}
        >
          <RangePicker style={{ width: 240 }} disabledDate={disabledDate} allowClear />
        </Form.Item>
        <Divider style={{ margin: '0 0 16px' }} />
        <Form.Item
          style={{ marginBottom: 16, padding: '0 16px' }}
          label="分组查看"
          name="groupValue"
        >
          {/* 分组查看： */}
          <Select
            showSearch
            style={{ width: 240 }}
            placeholder="不分组"
            // defaultValue={-1}
            allowClear
            fieldNames={{ label: 'fieldValue', value: 'seq' }}
            onChange={(e, option) => handleGroupChange(e, option)}
            filterOption={(input, option) =>
              option?.fieldValue.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            options={groupData}
          ></Select>
        </Form.Item>
        <Divider style={{ margin: '0 0 16px' }} />
        <Button type="primary" loading={analysisLoading} onClick={() => handleEventDetail()}>
          开始分析
        </Button>
        <Divider className={styles.dividerLast} />
      </Form>
      {/* 仅接口有数据返回展示图表 */}
      {firstSubmit ? (
        <div>
          <div className={styles.chartPng} style={{ background: `url(${chartPng})` }}></div>
          <div className={styles.chartText}>请选择事件</div>
        </div>
      ) : analysisLoading ? (
        <div>
          <div className={styles.loading}>
            <Spin size="large"></Spin>
          </div>
          <div className={styles.chartText}>加载中...</div>
        </div>
      ) : analyseDataFin?.analyseData?.length ? (
        <ChartItem data={analyseDataFin} />
      ) : (
        <div>
          <div className={styles.chartPng} style={{ background: `url(${chartPng})` }}></div>
          <div className={styles.chartText}>暂无内容</div>
        </div>
      )}
    </>
  );
};

export default EventAnalysis;
