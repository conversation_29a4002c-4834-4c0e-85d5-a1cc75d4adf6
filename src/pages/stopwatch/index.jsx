import {
  Table,
  Input,
  Button,
  Form,
  Select,
  Pagination,
  Popover,
  Icon,
  Divider,
  notification,
  Modal,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { authBtn } from '@/utils/utils';
import { getEnumParamData, pageDownload, getUserTerminal } from './services';
import AddBuriedPoint from './component/addBuriedPoint';

const StopComponent = ({
  dispatch,
  form,
  total = 0,
  listData = [],
  loading,
  ssoAuthorityRole: {
    data: { curResources = [] },
  },
}) => {
  const FormItem = Form.Item;
  const { Option } = Select;
  const [currentPage, setCurrentPage] = useState(1);
  const [pfData, setPFData] = useState([]);
  const [visible, setVisible] = useState(false);
  const [detail, setDetail] = useState(null);
  const [imageData, setImageData] = useState(null);
  const [imageVisible, setImageVisible] = useState(false);
  const [authList, setAuthList] = useState([]);
  const [appData, setAppData] = useState([]);

  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };

  const { getFieldDecorator } = form;

  const getPFCode = async () => {
    const { code, data } = await getEnumParamData({ enumCategory: 'pf', parentId: ['0'] });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.enumValueDesc, value: d.enumValue }));
      setPFData(newData);
    }
  };

  const queryData = value => {
    form.validateFields(async (err, values) => {
      const parmas = {
        terminalCode:
          !values.terminalCode || Array.isArray(values.terminalCode)
            ? undefined
            : Number(values.terminalCode),
        pfCode: !values.pfCode || Array.isArray(values.pfCode) ? undefined : Number(values.pfCode),
        pageName:
          values.pageName === '' || Array.isArray(values.pageName) ? undefined : values.pageName,
        pageNameOriginal:
          values.pageNameOriginal === '' || Array.isArray(values.pageNameOriginal)
            ? undefined
            : values.pageNameOriginal,
        btnName:
          values.btnName === '' || Array.isArray(values.btnName) ? undefined : values.btnName,
        id: values.id === '' || Array.isArray(values.id) ? undefined : Number(values.id),
        btnNameOriginal:
          values.btnNameOriginal === '' || Array.isArray(values.btnNameOriginal)
            ? undefined
            : values.btnNameOriginal,
        status: !values.status || Array.isArray(values.status) ? undefined : Number(values.status),
        releaseVersion:
          values.releaseVersion === '' || Array.isArray(values.releaseVersion)
            ? undefined
            : values.releaseVersion,
        isDefault:
          values.isDefault === undefined ||
          values.isDefault === null ||
          Array.isArray(values.isDefault)
            ? undefined
            : Number(values.isDefault),
        pageNum: value.pageNum,
        pageSize: value.pageSize,
      };
      dispatch({
        type: 'stopwatch/fetchlistData',
        payload: parmas,
      });
    });
  };

  const getApps = async () => {
    const { code, data } = await getUserTerminal({});
    if (code === 1 && data) {
      setAppData(data);
    }
  };

  useEffect(() => {
    getApps();
    getPFCode();
    const params = {
      pageNum: 1,
      pageSize: 10,
    };
    queryData(params);
  }, []);

  useEffect(() => {
    if (Array.isArray(curResources)) {
      curResources.map(c => {
        if (c.resourceKey === 'documents') {
          const authBtnList = authBtn(c.subList, 'stopwatch');
          setAuthList(authBtnList);
        }
      });
    }
  }, [curResources]);

  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
    queryData({ pageNum: page, pageSize });
  };
  const handlePageSizeChange = (current, pageSize) => {
    queryData({ pageNum: currentPage, pageSize });
  };

  const showTotal = Total => `共${Total}条`;

  const getImgData = async url => {
    const params = {
      path: url,
    };
    const { code, data, msg } = await pageDownload(params);
    if (code === 1 && data) {
      setImageData(data);
      setImageVisible(true);
    } else {
      notification.warn({
        message: '获取图片失败',
        description: msg,
      });
    }
  };

  const showImg = url => {
    if (url) {
      getImgData(url);
    }
  };

  const closeModal = () => {
    setVisible(false);
    setDetail(null);
    const params = {
      pageNum: currentPage,
      pageSize: 10,
    };
    queryData(params);
  };

  const openModal = () => {
    setVisible(true);
  };

  const changeStatus = value => {
    const params = {
      id: value.id,
      status: value.status,
    };
    dispatch({
      type: 'stopwatch/statusUpdata',
      payload: params,
      callback: () => {
        const newData = {
          pageNum: currentPage,
          pageSize: 10,
        };
        queryData(newData);
      },
    });
  };

  const isAuthBtn = key => authList.includes(key);

  const columns = [
    {
      title: '埋点ID',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面中文名称（原始）',
      dataIndex: 'pageNameOriginal',
      key: 'pageNameOriginal',
      align: 'center',
    },
    {
      title: '页面英文文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '页面英文名称（原始）',
      dataIndex: 'pageEngNameOriginal',
      key: 'pageEngNameOriginal',
      align: 'center',
    },
    {
      title: () => (
        <span>
          按钮
          <Popover placement="bottomLeft" content="埋点中文名称的简称，系统自动处理">
            <Icon type="question-circle" style={{ marginLeft: 5 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'btnName',
      key: 'btnName',
      align: 'center',
    },
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: (ext, row) => {
        if (Array.isArray(ext)) {
          return ext.map(e => <div key={e.param}>{`${e.param}: ${e.desc} `}</div>);
        }
        if (!ext) {
          if (Array.isArray(row.extOld)) {
            return row.extOld.map(e => <div key={e.param}>{`${e.param}: ${e.desc} `}</div>);
          }
        }
        return null;
      },
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: status => {
        if (status === 1) {
          return '正常';
        }
        if (status === 2) {
          return '停用';
        }
        return null;
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      render: (_, row) => {
        if (row.status === 2) {
          return (
            <div>
              {isAuthBtn('update') ? (
                <Button type="link" disabled>
                  编辑
                </Button>
              ) : null}
              {isAuthBtn('stop') ? (
                <Button type="link" onClick={() => changeStatus(row)}>
                  启用
                </Button>
              ) : null}
            </div>
          );
        }
        return (
          <div>
            {isAuthBtn('update') ? (
              <Button
                type="link"
                onClick={() => {
                  setDetail(row);
                  openModal();
                }}
              >
                编辑
              </Button>
            ) : null}
            {isAuthBtn('stop') ? (
              <Button type="link" onClick={() => changeStatus(row)}>
                停用
              </Button>
            ) : null}
          </div>
        );
      },
    },
  ];

  const renderContent = () => (
    <>
      <FormItem label="应用">
        {getFieldDecorator('terminalCode', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择应用" style={{ minWidth: '150px' }} allowClear>
            {appData.map(a => (
              <Option value={a.id} key={a.id}>
                {a.name}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>
      <FormItem label="平台">
        {getFieldDecorator('pfCode', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择平台" style={{ minWidth: '150px' }} allowClear>
            {pfData.map(d => (
              <Option value={d.value} key={d.value}>
                {d.label}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>
      <FormItem label="埋点中文名称" labelCol={{ span: 9 }} wrapperCol={{ span: 15 }}>
        {getFieldDecorator('btnNameOriginal', {
          rules: [],
          initialValue: [],
        })(<Input style={{ width: '160px' }} placeholder="请输入中文名称" />)}
      </FormItem>
      <FormItem label="埋点ID" labelCol={{ span: 6 }}>
        {getFieldDecorator('id', {
          rules: [
            {
              pattern: new RegExp(/^[+-]?\d+(\.\d+)?$|^$|^(\d+|\-){7,}$/, 'g'),
              message: '请输入数字',
            },
          ],
          initialValue: [],
        })(<Input style={{ width: '160px' }} placeholder="请输入埋点ID" />)}
      </FormItem>
      <FormItem label="页面中文名称" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        {getFieldDecorator('pageName', {
          rules: [],
          initialValue: [],
        })(<Input style={{ minWidth: '180px' }} placeholder="请输入页面中文名称" />)}
      </FormItem>
      <FormItem label="页面中文名称(原始)" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
        {getFieldDecorator('pageNameOriginal', {
          rules: [],
          initialValue: [],
        })(<Input style={{ minWidth: '180px' }} placeholder="请输入页面中文名称" />)}
      </FormItem>
      <FormItem label="按钮" labelCol={{ span: 6 }}>
        {getFieldDecorator('btnName', {
          rules: [],
          initialValue: [],
        })(<Input style={{ minWidth: '180px' }} placeholder="请输入按钮名称" />)}
      </FormItem>
      <FormItem label="状态">
        {getFieldDecorator('status', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择" style={{ minWidth: '150px' }} allowClear>
            <Option value={1} key={1}>
              启用
            </Option>
            <Option value={2} key={2}>
              停用
            </Option>
          </Select>,
        )}
      </FormItem>
      <FormItem label="埋点版本" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
        {getFieldDecorator('releaseVersion', {
          rules: [],
          initialValue: [],
        })(<Input style={{ minWidth: '180px' }} placeholder="请输入埋点版本" />)}
      </FormItem>
      <FormItem label="是否默认埋点" labelCol={{ span: 8 }} wrapperCol={{ span: 14 }}>
        {getFieldDecorator('isDefault', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择是否默认埋点" style={{ minWidth: '180px' }} allowClear>
            <Option value={1} key={1}>
              是
            </Option>
            <Option value={0} key={0}>
              否
            </Option>
          </Select>,
        )}
      </FormItem>
      {isAuthBtn('query') ? (
        <FormItem>
          <Button
            type="primary"
            size="default"
            onClick={() => {
              const params = {
                pageNum: 1,
                pageSize: 10,
              };
              queryData(params);
            }}
            loading={loading}
          >
            查询
          </Button>
        </FormItem>
      ) : null}
      {isAuthBtn('add') ? (
        <FormItem>
          <Button type="primary" size="default" onClick={openModal}>
            新增
          </Button>
        </FormItem>
      ) : null}
    </>
  );

  return (
    <PageHeaderWrapper
      title={false}
      content={
        <div style={{ marginTop: '10px' }}>
          <Divider style={{ margin: '10px 0' }} />
          <Form {...formLayout} layout="inline">
            {renderContent()}
          </Form>
          <Table
            columns={columns}
            dataSource={listData}
            style={{ marginTop: '20px' }}
            bordered
            rowKey="id"
            loading={loading}
            scroll={{ x: true, scrollToFirstRowOnChange: true }}
            pagination={false}
          />
          <Pagination
            style={{ float: 'right', marginTop: '20px' }}
            showQuickJumper
            showSizeChanger
            disabled={total === 0}
            showTotal={showTotal}
            current={currentPage}
            onChange={handlePageChange}
            onShowSizeChange={handlePageSizeChange}
            total={total}
          />
          <AddBuriedPoint
            visible={visible}
            closeModal={closeModal}
            detail={detail}
            appData={appData}
          />
          <Modal
            visible={imageVisible}
            onCancel={() => {
              setImageVisible(false);
              setImageData(null);
            }}
            footer={null}
            width="300px"
          >
            {ImageData ? (
              <img src={imageData} alt={imageData} style={{ marginTop: '15px', width: '250px' }} />
            ) : null}
          </Modal>
        </div>
      }
    ></PageHeaderWrapper>
  );
};

const StopComponentForm = Form.create()(StopComponent);

export default connect(({ stopwatch, loading, ssoAuthorityRole }) => ({
  listData: stopwatch.listData,
  total: stopwatch.total,
  loading: loading.effects['stopwatch/fetchlistData'],
  ssoAuthorityRole,
}))(StopComponentForm);
