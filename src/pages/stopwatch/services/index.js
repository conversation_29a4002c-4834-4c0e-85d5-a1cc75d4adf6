import request from '@/utils/request';

// 埋点码表同步
export async function getListData(pamas) {
  return request('/admin/v1/eventlog/burypoint/enum/query', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点码表新增
export async function createEnum(pamas) {
  return request('/admin/v1/eventlog/burypoint/enum/add', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点码表状态更新
export async function updataStatus(params) {
  return request('/admin/v1/eventlog/burypoint/enum/status', {
    method: 'POST',
    params,
  });
}

// 埋点码表编辑
export async function editEnum(pamas) {
  return request('/admin/v1/eventlog/burypoint/enum/edit', {
    method: 'POST',
    data: pamas,
  });
}

// 枚举值码表同步
export async function getEnumParamData(params) {
  return request('/admin/v1/eventlog/sync/list/enumparam', {
    params,
  });
}

// 页面下载
export async function pageDownload(params = {}) {
  return request('/admin/v1/eventlog/getPresignedPubUrl', {
    params,
  });
}

// 获取用户应用接口
export async function getUserTerminal(params) {
  return request('/admin/v1/eventlog/auth/terminal/list/get', {
    params,
  });
}
