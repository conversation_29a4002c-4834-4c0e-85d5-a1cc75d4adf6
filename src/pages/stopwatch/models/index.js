import { notification, message } from 'antd';
import { getListData, createEnum, updataStatus, editEnum } from '../services';

const UserModel = {
  namespace: 'stopwatch',
  state: {
    total: 0,
    listData: [],
  },
  effects: {
    *fetchlistData({ payload }, { call, put }) {
      const response = yield call(getListData, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
    *createEnumData({ payload, callback }, { call, put }) {
      const response = yield call(createEnum, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        message.success('新增成功')
        callback();
      } else {
        notification.warn({
          message: '新增失败',
          description: msg,
        });
      }
    },
    *statusUpdata({ payload, callback }, { call, put }) {
      const response = yield call(updataStatus, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        message.success('更新成功')
        callback();
      } else {
        notification.warn({
          message: '更新失败',
          description: msg,
        });
      }
    },
    *enumEdit({ payload, callback }, { call, put }) {
      const response = yield call(editEnum, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        message.success('编辑成功')
        callback();
      } else {
        notification.warn({
          message: '编辑失败',
          description: msg,
        });
      }
    },
  },
  reducers: {
    saveListData(state, action) {
      return { ...state, listData: action.payload }
    },
    saveTotal(state, action) {
      return { ...state, total: action.payload }
    },
  },
};
export default UserModel;
