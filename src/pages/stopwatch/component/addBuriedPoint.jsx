/* eslint-disable no-use-before-define */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable import/named */
import {
  Input,
  Button,
  Form,
  Select,
  message,
  Modal,
  Row,
  Col,
  Popover,
  Descriptions,
  Icon,
} from 'antd';
import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import { getEnumParamData } from '../services';

const StopAddComponent = ({
  dispatch,
  form,
  loading,
  visible,
  detail,
  closeModal,
  appData = [],
}) => {
  const FormItem = Form.Item;
  const { Option } = Select;
  const [sdpVisible, setSdpVisible] = useState(false);
  const [count, setCount] = useState([1]);
  const [extAllData, setExtAllData] = useState([]);
  const [maxCount, setMaxCount] = useState(1);
  const [pfData, setPFData] = useState([]);
  const [extNewData, setExtNewData] = useState(null);
  const { getFieldDecorator, setFieldsValue, getFieldValue, resetFields } = form;

  useEffect(() => {
    resetFields();
    getPFCode();
    if (detail) {
      const newData = Array.isArray(detail.ext)
        ? detail.ext.map((d, index) => ({
            id: index + 1,
            title: d.param,
            doce: d.desc,
          }))
        : [];
      setExtAllData(newData);
      setMaxCount(newData.length);
      const allData = {
        terminalName: detail.terminalName
          ? { label: detail.terminalName, key: Number(detail.terminalCode) }
          : [],
        pfName: detail.pfName ? { label: detail.pfName, key: Number(detail.pfCode) } : [],
        pageName: detail.pageName ? detail.pageName : [],
        pageEngName: detail.pageEngName ? detail.pageEngName : [],
        btnNameOriginal: detail.btnNameOriginal ? detail.btnNameOriginal : [],
        btnEngName: detail.btnEngName ? detail.btnEngName : [],
        releaseVersion: detail.releaseVersion ? detail.releaseVersion : [],
        isDefault: detail.isDefault || detail.isDefault === 0 ? detail.isDefault : [],
        remarks: detail.remarks ? detail.remarks : [],
      };
      setFieldsValue(allData);
    }
    return () => {
      setSdpVisible(false);
      deleteAllData();
    };
  }, [detail]);

  const deleteAllData = () => {
    setCount([1]);
    setExtAllData([]);
    setMaxCount(1);
    setExtNewData(null);
  };

  const queryData = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const newExt = extAllData.map(e => ({ param: e.title, desc: e.doce }));
      const params = {
        terminalName: values.terminalName.label,
        terminalCode: Number(values.terminalName.key),
        pfName: values.pfName.label,
        pfCode: Number(values.pfName.key),
        pageName: values.pageName,
        pageEngName: values.pageEngName,
        btnNameOriginal: values.btnNameOriginal,
        btnEngName: values.btnEngName,
        releaseVersion: values.releaseVersion,
        isDefault: Number(values.isDefault),
        remarks:
          values.remarks === '' || Array.isArray(values.remarks) ? undefined : values.remarks,
        ext: newExt.length === 0 ? undefined : newExt,
      };
      if (detail) {
        const newParams = {
          ...params,
          pageUrl: detail.pageUrl,
          pageId: detail.pageId,
          id: detail.id,
        };
        dispatch({
          type: 'stopwatch/enumEdit',
          payload: newParams,
          callback: () => {
            closeModal();
            deleteAllData();
          },
        });
      } else {
        dispatch({
          type: 'stopwatch/createEnumData',
          payload: params,
          callback: () => {
            closeModal();
            deleteAllData();
          },
        });
      }
    });
  };

  const openSdpModal = () => {
    if (extAllData.length !== 0) {
      const obj = {};
      const d = extAllData.map(item => {
        obj[`ext${item.id}`] = item.title;
        obj[`extDoce${item.id}`] = item.doce;
        return item.id;
      });
      setCount(d);
      setExtNewData(obj);
      setFieldsValue(obj);
    } else {
      setCount([1]);
      setMaxCount(1);
      setExtNewData({
        ext1: [],
        extDoce1: [],
      });
    }
    setSdpVisible(true);
  };

  const addFrom = index => {
    if (count.length === 5) {
      message.warning('最多支持5个场景描述');
      return;
    }
    const d = count;
    const item = maxCount + 1;
    d.splice(index + 1, 0, item);
    setCount(d);
    setMaxCount(item);
  };

  const subtractFrom = index => {
    const d = count;
    d.splice(index, 1);
    setCount([...d]);
  };

  const getPFCode = async () => {
    const { code, data } = await getEnumParamData({ enumCategory: 'pf', parentId: ['0'] });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.enumValueDesc, value: d.enumValue }));
      setPFData(newData);
    }
  };

  const extDataDelect = it => {
    if (extAllData.length === 1) {
      setCount([1]);
      setExtNewData({
        ext1: [],
        extDoce1: [],
      });
      setFieldsValue({
        ext1: [],
        extDoce1: [],
      });
    }
    const d = extAllData.filter(e => e.id !== it.id);
    setExtAllData([...d]);
  };

  const saveExtData = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const data = count.map(d => {
        const extData = getFieldValue(`ext${d}`);
        const extDoceData = getFieldValue(`extDoce${d}`);
        if (!(Array.isArray(extData) || extData === '' || extData === undefined)) {
          return { id: d, title: extData, doce: extDoceData };
        }
      });
      setExtAllData(data.filter(d => d !== undefined));
      setSdpVisible(false);
    });
  };

  const renderContent = () => (
    <>
      <Row>
        <Col span={11}>
          <FormItem label="应用">
            {getFieldDecorator('terminalName', {
              rules: [{ required: true, message: '请选择应用' }],
              initialValue: [],
            })(
              <Select
                placeholder="请选择应用"
                style={{ minWidth: '150px' }}
                labelInValue
                allowClear
              >
                {appData.map(a => (
                  <Option value={a.id} key={a.id}>
                    {a.name}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Col>
        <Col span={11} offset={2}>
          <FormItem label="平台">
            {getFieldDecorator('pfName', {
              rules: [{ required: true, message: '请选择平台' }],
              initialValue: [],
            })(
              <Select placeholder="请选择平台" labelInValue style={{ minWidth: '150px' }}>
                {pfData.map(d => (
                  <Option value={d.value} key={d.value}>
                    {d.label}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={11}>
          <FormItem label="页面中文名称">
            {getFieldDecorator('pageName', {
              rules: [{ required: true, message: '请输入页面中文名称' }],
              initialValue: [],
            })(<Input placeholder="请输入页面中文名称" />)}
          </FormItem>
        </Col>
        <Col span={11} offset={2}>
          <FormItem label="页面英文名称">
            {getFieldDecorator('pageEngName', {
              rules: [{ required: true, message: '请输入页面英文名称' }],
              initialValue: [],
            })(<Input placeholder="请输入页面英文名称" />)}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={11}>
          <FormItem label="埋点中文名称">
            {getFieldDecorator('btnNameOriginal', {
              rules: [{ required: true, message: '请输入埋点中文名称' }],
              initialValue: [],
            })(<Input placeholder="请输入埋点中文名称" />)}
          </FormItem>
        </Col>
        <Col span={11} offset={2}>
          <FormItem label="埋点英文名称">
            {getFieldDecorator('btnEngName', {
              rules: [{ required: true, message: '请输入埋点英文名称' }],
              initialValue: [],
            })(<Input placeholder="请输入埋点英文名称" />)}
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span={11}>
          <FormItem label="埋点版本">
            {getFieldDecorator('releaseVersion', {
              rules: [{ required: true, message: '请输入埋点版本' }],
              initialValue: [],
            })(<Input placeholder="请输入埋点版本" />)}
          </FormItem>
        </Col>
        <Col span={11} offset={2}>
          <FormItem
            label={
              <span>
                是否默认埋点
                <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
                  <Icon type="question-circle" style={{ marginLeft: 10 }} />
                </Popover>
              </span>
            }
          >
            {getFieldDecorator('isDefault', {
              rules: [{ required: true, message: '请选择是否默认埋点' }],
              initialValue: [],
            })(
              <Select placeholder="请选择是否默认埋点" style={{ minWidth: '150px' }}>
                <Option value={1} key={1}>
                  是
                </Option>
                <Option value={0} key={0}>
                  否
                </Option>
              </Select>,
            )}
          </FormItem>
        </Col>
      </Row>
      {extAllData.length !== 0
        ? extAllData.map((it, index) => (
            <div style={{ marginBottom: '4px' }} key={it.id}>
              <span
                style={{
                  paddingBottom: '16px',
                  fontSize: '14px',
                  color: '#000',
                  fontWeight: 500,
                  display: 'block',
                  marginRight: '30px',
                  float: 'left',
                }}
              >
                参数{index + 1}
              </span>
              <span style={{ display: 'inline-block', width: '450px' }}>
                <Descriptions column={4}>
                  <Descriptions.Item label="参数" span={2}>
                    {it.title.length > 10 ? `${it.title.substring(0, 10)}` : it.title}
                    {it.title.length > 10 ? <Popover content={it.title}>...</Popover> : null}
                  </Descriptions.Item>
                  <Descriptions.Item label="参数描述" span={2}>
                    {it.doce.length > 10 ? `${it.doce.substring(0, 10)}` : it.doce}
                    {it.doce.length > 10 ? <Popover content={it.doce}>...</Popover> : null}
                  </Descriptions.Item>
                </Descriptions>
              </span>
              <Button
                type="link"
                icon="delete"
                style={{ float: 'right', color: '#666', marginTop: '-5px' }}
                onClick={() => extDataDelect(it)}
              ></Button>
            </div>
          ))
        : null}
      <FormItem>
        <Button type="primary" block onClick={openSdpModal}>
          自定义参数
        </Button>
      </FormItem>
      <FormItem label="备注">
        {getFieldDecorator('remarks', {
          initialValue: [],
        })(
          <Input.TextArea
            maxLength={50}
            autoSize={{ minRows: 3, maxRows: 5 }}
            placeholder="请输入备注，50字以内"
          />,
        )}
      </FormItem>
    </>
  );

  const sdpContent = () =>
    count.map((item, index) => (
      <Row key={item}>
        <Col span={8}>
          <FormItem label={index === 0 ? '参数' : null}>
            {getFieldDecorator(`ext${item}`, {
              rules: [
                {
                  required: !(
                    getFieldValue(`extDoce${item}`) === '' ||
                    Array.isArray(getFieldValue(`extDoce${item}`)) ||
                    !getFieldValue(`extDoce${item}`)
                  ),
                  message: '请输入参数',
                },
              ],
              initialValue: extNewData ? extNewData[`ext${item}`] : [],
            })(<Input placeholder="请输入参数" maxLength={20} />)}
          </FormItem>
        </Col>
        <Col span={11} offset={1}>
          <FormItem label={index === 0 ? '参数描述' : null}>
            {getFieldDecorator(`extDoce${item}`, {
              rules: [
                {
                  required: !(
                    getFieldValue(`ext${item}`) === '' ||
                    Array.isArray(getFieldValue(`ext${item}`)) ||
                    !getFieldValue(`ext${item}`)
                  ),
                  message: '请输入参数描述',
                },
              ],
              initialValue: extNewData ? extNewData[`extDoce${item}`] : [],
            })(<Input placeholder="请输入参数描述" maxLength={20} />)}
          </FormItem>
        </Col>
        <Col span={3} offset={1} style={index === 0 ? { marginTop: '30px' } : {}}>
          <span style={{ float: 'right' }}>
            <Button size="small" type="link" icon="plus" onClick={() => addFrom(item)}></Button>
            <Button
              size="small"
              type="link"
              icon="close"
              style={{ color: 'red' }}
              disabled={count.length === 1}
              onClick={() => subtractFrom(index)}
            ></Button>
          </span>
        </Col>
      </Row>
    ));

  return (
    <Modal
      visible={visible}
      title={<div style={{ textAlign: 'center' }}>{detail ? '编辑埋点' : '新增埋点'}</div>}
      onCancel={() => {
        deleteAllData();
        closeModal();
      }}
      width="650px"
      onOk={queryData}
      confirmLoading={
        detail ? loading.effects['stopwatch/enumEdit'] : loading.effects['stopwatch/createEnumData']
      }
      destroyOnClose
      centered
    >
      <Form layout="vertical">{renderContent()}</Form>
      <Modal
        visible={sdpVisible}
        title={<div style={{ textAlign: 'center' }}>自定义参数</div>}
        onCancel={() => setSdpVisible(false)}
        centered
        destroyOnClose
        width="600px"
        onOk={saveExtData}
      >
        <Form layout="vertical">{sdpContent()}</Form>
      </Modal>
    </Modal>
  );
};

const ComponentForm = Form.create()(StopAddComponent);

export default connect(({ loading }) => ({ loading }))(ComponentForm);
