import React from 'react';
import Redirect from 'umi/redirect';
import { connect } from 'dva';
import Authorized from '@/utils/Authorized';

const AuthComponent = ({
  children,
  route = {
    routes: [],
  },
  location = {
    pathname: '',
  },
  currResources
}) => {
  const currResourcesList = [];
  const open = (list, fatherName) => {
    list.map(li => {
      const targetName = fatherName ? `${fatherName}_${li.resourceKey}` : li.resourceKey;
      currResourcesList.push(targetName);
      Array.isArray(li.subList) && open(li.subList, targetName);
    });
  };
  Array.isArray(currResources) && open(currResources);
  const matching = matchName => {
    return !!(currResourcesList.indexOf(matchName) > -1 || matchName == 'home' || !currResources);
  };

  const getRouteAuthority = (path, routeData) => {
    let pathName = ''
    const filter = (list, fatherName) => {
      list.map(li => {
        const targetName = fatherName ? `${fatherName}_${li.name}` : li.name;
        if (li.path && li.path.indexOf('/:') > -1) {
          path.indexOf(li.path.split('/:')[0]) === 0 && (pathName = targetName)
        } else if (li.path === path) {
          pathName = targetName
        } else {
          Array.isArray(li.routes) && filter(li.routes, targetName);
        }
      });
    };
    filter(routeData)
    return [matching(pathName) ? 'user' : 'admin'];
  };

  const { routes = [] } = route;
  return (
    <Authorized
      authority={getRouteAuthority(location.pathname, routes) || ''}
      noMatch={<Redirect to="/" />}
    >
      {children}
    </Authorized>
  );
};

export default connect(({ user }) => ({
  user, currResources: user.currResources,
}))(AuthComponent);
