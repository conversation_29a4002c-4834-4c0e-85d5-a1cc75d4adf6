import React, { useState, useEffect } from 'react';
import { Divider } from 'antd';
import { connect } from 'dva';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import DevTestPage from './component/devTestPage';
import ProductPage from './component/productPage';

const DemandComponent = ({ location }) => {
  const [role, setRole] = useState('product');
  useEffect(() => {
    const path = location.pathname;
    if (!path.includes('submit')) {
      setRole('DevOrTest');
    }
  }, []);
  return (
    <PageHeaderWrapper
      title={false}
      content={
        <div style={{ marginTop: '10px' }}>
          <Divider style={{ margin: '10px 0' }} />
          {role === 'product' ? <ProductPage /> : <DevTestPage />}
        </div>
      }
    ></PageHeaderWrapper>
  );
};

export default connect(() => ({}))(DemandComponent);
