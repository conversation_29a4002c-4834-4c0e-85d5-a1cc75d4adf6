import React, { useState, useEffect } from 'react';
import {
  Radio,
  Button,
  Icon,
  Table,
  Pagination,
  Popover,
  Modal,
  Form,
  Input,
  Select,
  Checkbox,
  notification,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { authBtn } from '@/utils/utils';
import QueryForm from './queryForm';
import { getRoleTypeList, pageDownload, getUserInfoByToken } from '../services';

const DevTestComponent = ({
  total = 0,
  form,
  dispatch,
  loading,
  tableList = [],
  ssoAuthorityRole: {
    data: { curResources = [] },
  },
}) => {
  const { getFieldDecorator, resetFields } = form;
  const FormItem = Form.Item;
  const { Option } = Select;
  const [currentPage, setCurrentPage] = useState(1);
  const [type, setType] = useState(10);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [passVisible, setPassVisible] = useState(false);
  const [testPassVisible, setTestPassVisible] = useState(false);
  const [rejectVisible, setRejectVisible] = useState(false);
  const [check, setCheck] = useState(false);
  const [redeployVisible, setRedeployVisible] = useState(false);
  const [queryData, setQueryData] = useState({});
  const [role, setRole] = useState([]);
  const [devData, setDevData] = useState([]);
  const [testData, setTestData] = useState([]);
  const [isList, setIsList] = useState(null);
  const [imageData, setImageData] = useState(null);
  const [imageVisible, setImageVisible] = useState(false);
  const [authList, setAuthList] = useState([]);
  const typeChange = e => {
    setType(e.target.value);
    setCurrentPage(1);
    setSelectedRowKeys([]);
    resetFields();
    setIsList(null);
  };

  const queryListData = params => {
    setQueryData(params);
    setCurrentPage(params.pageNum);
    dispatch({
      type: 'demand/fetchBurypointList',
      payload: params,
    });
  };

  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
    const parmas = {
      ...queryData,
      pageNum: page,
      pageSiz: pageSize,
    };
    queryListData(parmas);
  };
  const handlePageSizeChange = (current, pageSize) => {
    const parmas = {
      ...queryData,
      pageNum: currentPage,
      pageSiz: pageSize,
    };
    queryListData(parmas);
  };

  const getDevList = async () => {
    const { code, data } = await getRoleTypeList({ roleCode: 'kaifarenyuan' });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.name, value: d.id }));
      setDevData(newData);
    }
  };

  const getTestList = async () => {
    const { code, data } = await getRoleTypeList({ roleCode: 'ceshirenyuan' });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.name, value: d.id }));
      setTestData(newData);
    }
  };

  const getImgData = async url => {
    const params = {
      path: url,
    };
    const { code, data, msg } = await pageDownload(params);
    if (code === 1 && data) {
      setImageData(data);
      setImageVisible(true);
    } else {
      notification.warn({
        message: '获取图片失败',
        description: msg,
      });
    }
  };

  const showImg = url => {
    if (url) {
      getImgData(url);
    }
  };
  const getRole = async () => {
    const { code, data } = await getUserInfoByToken();
    if (code === 1 && Array.isArray(data)) {
      if (data.length !== 0) {
        const newData = data.map(d => d.code);
        setRole(newData);
        if (newData.includes('ceshifuzeren') || newData.includes('ceshirenyuan')) {
          setType(20);
        }
      }
    }
  };

  const isRole = () => {
    if (role.includes('kaifafuzeren') || role.includes('kaifarenyuan')) {
      return devData;
    }
    if (role.includes('ceshifuzeren') || role.includes('ceshirenyuan')) {
      return testData;
    }
    return [...devData, ...testData];
  };

  const isRoleType = () => {
    if (role.includes('kaifafuzeren') || role.includes('kaifarenyuan')) {
      return true;
    }
    if (role.includes('ceshifuzeren') || role.includes('ceshirenyuan')) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    getDevList();
    getTestList();
    getRole();
  }, []);

  useEffect(() => {
    if (Array.isArray(curResources)) {
      curResources.map(c => {
        if (c.resourceKey === 'demand') {
          const authBtnList = authBtn(c.subList, 'audit');
          setAuthList(authBtnList);
        }
      });
    }
  }, [curResources]);

  useEffect(
    () => () => {
      dispatch({
        type: 'demand/saveListData',
        payload: [],
      });
      dispatch({
        type: 'demand/saveTotal',
        payload: 0,
      });
      setCurrentPage(1);
    },
    [type],
  );

  const auditData = val => {
    dispatch({
      type: 'demand/burypointAuditData',
      payload: val,
      callback: () => {
        const parmas = {
          ...queryData,
        };
        queryListData(parmas);
        setRejectVisible(false);
        setPassVisible(false);
      },
    });
  };

  const auditPass = ids => {
    const params = {
      ids,
      type: 1,
    };
    auditData(params);
  };

  const notPassChange = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const params = {
        ids: isList || selectedRowKeys,
        type: 0,
        reason: values.reason,
      };
      auditData(params);
    });
  };

  const personnelRedeploy = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const params = {
        ids: isList || selectedRowKeys,
        status: type,
      };
      if (isRoleType()) {
        dispatch({
          type: 'demand/redeployBurypoint',
          payload: {
            ...params,
            developmentUid: values.redeploy.key,
            developmentOwner: values.redeploy.label,
          },
          callback: () => {
            const parmas = {
              ...queryData,
            };
            queryListData(parmas);
            setRedeployVisible(false);
          },
        });
      } else {
        dispatch({
          type: 'demand/redeployBurypoint',
          payload: {
            ...params,
            testOwner: values.redeploy.label,
            testUid: values.redeploy.key,
          },
          callback: () => {
            const parmas = {
              ...queryData,
            };
            queryListData(parmas);
            setRedeployVisible(false);
          },
        });
      }
    });
  };

  const testPass = id => {
    const params = {
      id: id || isList,
      type: 1,
    };
    dispatch({
      type: 'demand/burypointPassSubmit',
      payload: params,
      callback: () => {
        const parmas = {
          ...queryData,
        };
        queryListData(parmas);
        setTestPassVisible(false);
      },
    });
  };

  const testNotPass = id => {
    const params = {
      id,
      type: 0,
    };
    dispatch({
      type: 'demand/burypointPassSubmit',
      payload: params,
      callback: () => {
        const parmas = {
          ...queryData,
        };
        queryListData(parmas);
      },
    });
  };

  const changeToTestStatus = id => {
    const params = {
      id,
    };
    dispatch({
      type: 'demand/burypointDeployqaSubmit',
      payload: params,
      callback: () => {
        const parmas = {
          ...queryData,
        };
        queryListData(parmas);
      },
    });
  };

  const isAuthBtn = key => authList.includes(key);

  const otherColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '提交日期',
      dataIndex: 'submitTime',
      key: 'submitTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '开发',
      dataIndex: 'developmentOwner',
      key: 'developmentOwner',
      align: 'center',
    },
    {
      title: '测试',
      dataIndex: 'testOwner',
      key: 'testOwner',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      render: (_, row) => (
        <div>
          {isAuthBtn('zhuanpai') ? (
            <Button
              type="link"
              style={{ padding: '0 5px' }}
              onClick={() => {
                setIsList([row.id]);
                setRedeployVisible(true);
              }}
            >
              转派
            </Button>
          ) : null}
          {isAuthBtn('pass') ? (
            <Button
              type="link"
              style={{ padding: '0 5px' }}
              onClick={() => {
                auditPass([row.id]);
              }}
            >
              通过
            </Button>
          ) : null}
          {isAuthBtn('bohui') ? (
            <Button
              type="link"
              style={{ padding: '0 5px', color: 'red' }}
              onClick={() => {
                setIsList([row.id]);
                setRejectVisible(true);
              }}
            >
              驳回
            </Button>
          ) : null}
        </div>
      ),
    },
  ];

  const passColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '审核日期',
      dataIndex: 'auditTime',
      key: 'auditTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '审核人',
      dataIndex: 'auditUserName',
      key: 'auditUserName',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '开发',
      dataIndex: 'developmentOwner',
      key: 'developmentOwner',
      align: 'center',
    },
    {
      title: '测试',
      dataIndex: 'testOwner',
      key: 'testOwner',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      align: 'center',
      render: status => {
        if (status === 0) {
          return '待开发';
        }
        if (status === 1) {
          return '待测试';
        }
        if (status === 2) {
          return '测试不通过';
        }
        if (status === 3) {
          return '测试通过';
        }
        return null;
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      render: (_, row) => (
        <div>
          {isAuthBtn('zhuanweibohui') ? (
            <Button
              type="link"
              style={{ padding: '0 5px' }}
              disabled={row.taskStatus !== 0}
              onClick={() => {
                setIsList([row.id]);
                setRejectVisible(true);
              }}
            >
              转为驳回
            </Button>
          ) : null}
          {isAuthBtn('tice') ? (
            <Button
              type="link"
              disabled={row.taskStatus === 1}
              style={{ padding: '0 5px' }}
              onClick={() => changeToTestStatus(row.id)}
            >
              提测
            </Button>
          ) : null}
          {isAuthBtn('zhuanpai') ? (
            <Button
              type="link"
              style={{ padding: '0 5px' }}
              onClick={() => {
                setIsList([row.id]);
                setRedeployVisible(true);
              }}
            >
              转派
            </Button>
          ) : null}
          {isAuthBtn('testpass') ? (
            <Button
              type="link"
              style={{ padding: '0 5px' }}
              disabled={row.taskStatus !== 1}
              onClick={() => {
                if (check) {
                  testPass(row.id);
                } else {
                  setIsList(row.id);
                  setTestPassVisible(true);
                }
              }}
            >
              测试通过
            </Button>
          ) : null}
          {isAuthBtn('testnotpass') ? (
            <Button
              type="link"
              disabled={row.taskStatus !== 1}
              style={row.taskStatus !== 1 ? {} : { padding: '0 5px', color: 'red' }}
              onClick={() => testNotPass(row.id)}
            >
              测试不通过
            </Button>
          ) : null}
        </div>
      ),
    },
  ];

  const notPassColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '审核日期',
      dataIndex: 'auditTime',
      key: 'auditTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '审核人',
      dataIndex: 'auditUserName',
      key: 'auditUserName',
      align: 'center',
    },
    {
      title: '驳回原因',
      dataIndex: 'auditRejectReason',
      key: 'auditRejectReason',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '开发',
      dataIndex: 'developmentOwner',
      key: 'developmentOwner',
      align: 'center',
    },
    {
      title: '测试',
      dataIndex: 'testOwner',
      key: 'testOwner',
      align: 'center',
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKey, selectedRow) => {
      setSelectedRowKeys(selectedRowKey);
    },
  };

  return (
    <div>
      <div>
        {isRoleType() ? (
          <Radio.Group value={type} buttonStyle="solid" onChange={typeChange}>
            <Radio.Button value={10}>待审核</Radio.Button>
            <Radio.Button value={30}>审核驳回</Radio.Button>
            <Radio.Button value={20}>审核通过</Radio.Button>
          </Radio.Group>
        ) : (
          <Radio.Group value={type} buttonStyle="solid" onChange={typeChange}>
            <Radio.Button value={20}>审核通过</Radio.Button>
          </Radio.Group>
        )}
      </div>
      <QueryForm
        type={type}
        roleType="DevOrTest"
        queryListData={queryListData}
        isAuthBtn={isAuthBtn}
      />
      {type !== 30 ? (
        <>
          {type === 10 ? (
            <div style={{ height: '40px' }}>
              <span style={{ margin: '20px 0', float: 'right' }}>
                {isAuthBtn('zhuanpai') ? (
                  <Button
                    disabled={selectedRowKeys.length === 0}
                    type="primary"
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      setIsList(null);
                      setRedeployVisible(true);
                    }}
                  >
                    批量转派
                  </Button>
                ) : null}
                {isAuthBtn('pass') ? (
                  <Button
                    disabled={selectedRowKeys.length === 0}
                    type="primary"
                    style={{ marginRight: '10px' }}
                    onClick={() => setPassVisible(true)}
                  >
                    批量通过
                  </Button>
                ) : null}
                {isAuthBtn('bohui') ? (
                  <Button
                    disabled={selectedRowKeys.length === 0}
                    type="primary"
                    onClick={() => {
                      setIsList(null);
                      setRejectVisible(true);
                    }}
                  >
                    批量驳回
                  </Button>
                ) : null}
              </span>
            </div>
          ) : (
            <div style={{ height: '40px' }}>
              <span style={{ margin: '20px 0', float: 'right' }}>
                {isAuthBtn('zhuanpai') ? (
                  <Button
                    disabled={selectedRowKeys.length === 0}
                    type="primary"
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      setIsList(null);
                      setRedeployVisible(true);
                    }}
                  >
                    批量转派
                  </Button>
                ) : null}
              </span>
            </div>
          )}
          <Table
            columns={type === 10 ? otherColumns : passColumns}
            dataSource={tableList}
            style={{ marginTop: '20px' }}
            bordered
            loading={loading.effects['demand/fetchBurypointList']}
            rowKey="id"
            rowSelection={rowSelection}
            scroll={{ x: true, scrollToFirstRowOnChange: true }}
            pagination={false}
          />
        </>
      ) : (
        <Table
          columns={notPassColumns}
          dataSource={tableList}
          style={{ marginTop: '20px' }}
          bordered
          loading={loading.effects['demand/fetchBurypointList']}
          rowKey="id"
          scroll={{ x: true, scrollToFirstRowOnChange: true }}
          pagination={false}
        />
      )}
      <Pagination
        style={{ float: 'right', marginTop: '20px' }}
        showQuickJumper
        showSizeChanger
        disabled={total === 0}
        current={currentPage}
        onChange={handlePageChange}
        onShowSizeChange={handlePageSizeChange}
        total={total}
      />
      <Modal
        visible={passVisible}
        width="300px"
        onCancel={() => setPassVisible(false)}
        onOk={() => {
          auditPass(selectedRowKeys);
        }}
        confirmLoading={loading.effects['demand/burypointAuditData']}
      >
        <div style={{ margin: '20px 0 10px 0', textAlign: 'center' }}>确认批量通过吗？</div>
      </Modal>
      <Modal
        visible={rejectVisible}
        width="400px"
        onCancel={() => setRejectVisible(false)}
        onOk={notPassChange}
        confirmLoading={loading.effects['demand/burypointAuditData']}
      >
        <Form style={{ marginTop: '20px' }}>
          <FormItem style={{ margin: 0 }}>
            {getFieldDecorator('reason', {
              initialValue: [],
              rules: [{ required: rejectVisible, message: '请输入驳回原因，20字以内' }],
            })(
              <Input.TextArea
                maxLength={20}
                autoSize={{ minRows: 3, maxRows: 5 }}
                placeholder="请输入驳回原因，20字以内"
              />,
            )}
          </FormItem>
        </Form>
      </Modal>
      <Modal
        visible={redeployVisible}
        width="400px"
        onCancel={() => setRedeployVisible(false)}
        onOk={personnelRedeploy}
        confirmLoading={loading.effects['demand/redeployBurypoint']}
        destroyOnClose
      >
        <Form style={{ marginTop: '20px' }} layout="horizontal">
          <FormItem
            style={{ margin: 0 }}
            label="转派给"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 15 }}
          >
            {getFieldDecorator('redeploy', {
              initialValue: [],
              rules: [{ required: redeployVisible, message: '请选择转派的人员' }],
            })(
              <Select placeholder="请选择转派的人员" style={{ minWidth: '150px' }} labelInValue>
                {isRole().map(d => (
                  <Option value={d.value} key={d.value}>
                    {d.label}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Form>
      </Modal>
      <Modal
        visible={testPassVisible}
        width="350px"
        onCancel={() => setTestPassVisible(false)}
        onOk={() => testPass()}
        confirmLoading={loading.effects['demand/burypointPassSubmit']}
      >
        <div style={{ margin: '20px 0 10px 0', textAlign: 'center' }}>
          测试通过后数据流转到埋点码表，且不可更改，确定测试通过吗？
        </div>
        <Checkbox
          checked={check}
          onChange={val => {
            setCheck(val.target.checked);
          }}
        >
          以后不再提示
        </Checkbox>
      </Modal>
      <Modal
        visible={imageVisible}
        onCancel={() => {
          setImageVisible(false);
          setImageData(null);
        }}
        footer={null}
        width="300px"
      >
        {ImageData ? (
          <img src={imageData} alt={imageData} style={{ marginTop: '15px', width: '250px' }} />
        ) : null}
      </Modal>
    </div>
  );
};
const DevTestComponentForm = Form.create()(DevTestComponent);

export default connect(({ loading, demand, ssoAuthorityRole }) => ({
  loading,
  tableList: demand.tableList,
  total: demand.total,
  ssoAuthorityRole,
}))(DevTestComponentForm);
