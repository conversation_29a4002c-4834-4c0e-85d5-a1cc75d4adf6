/* eslint-disable no-nested-ternary */
/* eslint-disable no-use-before-define */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable import/named */
import { Input, Button, Form, Select, DatePicker } from 'antd';
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { connect } from 'dva';
import { getEnumParamData, getUserTerminal } from '../../stopwatch/services';

const QueryComponent = ({ form, type, loading, roleType, queryListData, isAuthBtn }) => {
  const FormItem = Form.Item;
  const { Option } = Select;
  const { RangePicker } = DatePicker;
  const [pfData, setPFData] = useState([]);
  const [appData, setAppData] = useState([]);

  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };

  const { getFieldDecorator, resetFields } = form;

  const getPFCode = async () => {
    const { code, data } = await getEnumParamData({ enumCategory: 'pf', parentId: ['0'] });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.enumValueDesc, value: d.enumValue }));
      setPFData(newData);
    }
  };
  const getApps = async () => {
    const { code, data } = await getUserTerminal({});
    if (code === 1 && data) {
      setAppData(data);
    }
  };

  useEffect(() => {
    getApps();
    getPFCode();
    const params = {
      pageNum: 1,
      pageSize: 10,
    };
    queryData(params);
    resetFields();
  }, [type]);

  const timeFormate = (time, index) => {
    if (time) {
      return time.length !== 0
        ? index === 1
          ? moment(time[index]).format('YYYY-MM-DD 24:00:00')
          : moment(time[index]).format('YYYY-MM-DD 00:00:00')
        : undefined;
    }
    return undefined;
  };

  const queryData = value => {
    form.validateFields(async (err, values) => {
      if (err) return false;
      let params = {};
      const someParams = {
        status: Number(type),
        terminalCode:
          !values.terminalCode || Array.isArray(values.terminalCode)
            ? undefined
            : Number(values.terminalCode),
        pfCode: !values.pfCode || Array.isArray(values.pfCode) ? undefined : Number(values.pfCode),
        pageName:
          values.pageName === '' || Array.isArray(values.pageName) ? undefined : values.pageName,
        pageNameOriginal:
          values.pageNameOriginal === '' || Array.isArray(values.pageNameOriginal)
            ? undefined
            : values.pageNameOriginal,
        releaseVersion:
          values.releaseVersion === '' || Array.isArray(values.releaseVersion)
            ? undefined
            : values.releaseVersion,
        btnNameOriginal:
          values.btnNameOriginal === '' || Array.isArray(values.btnNameOriginal)
            ? undefined
            : values.btnNameOriginal,
        isDefault:
          values.isDefault === undefined ||
          values.isDefault === null ||
          Array.isArray(values.isDefault)
            ? undefined
            : Number(values.isDefault),
        updateTimeStart: timeFormate(values.updateTime, 0),
        updateTimeEnd: timeFormate(values.updateTime, 1),
        ...value,
      };
      if (type === 0) {
        params = {
          ...someParams,
          createTimeStart: timeFormate(values.createTime, 0),
          createTimeEnd: timeFormate(values.createTime, 1),
        };
      }
      if (type === 10) {
        params = {
          ...someParams,
          submitTimeStart: timeFormate(values.submitTime, 0),
          submitTimeEnd: timeFormate(values.submitTime, 1),
        };
      }
      if (type === 30) {
        params = {
          ...someParams,
          auditTimeStart: timeFormate(values.auditTime, 0),
          auditTimeEnd: timeFormate(values.auditTime, 1),
        };
      }
      if (type === 20) {
        if (roleType === 'DevOrTest') {
          params = {
            ...someParams,
            taskStatus:
              (!values.taskStatus && values.taskStatus !== 0) || Array.isArray(values.taskStatus)
                ? undefined
                : Number(values.taskStatus),
            auditTimeStart: timeFormate(values.auditTime, 0),
            auditTimeEnd: timeFormate(values.auditTime, 1),
          };
        } else {
          params = {
            ...someParams,
            auditTimeStart: timeFormate(values.auditTime, 0),
            auditTimeEnd: timeFormate(values.auditTime, 1),
          };
        }
      }
      queryListData(params);
    });
  };

  const dateFormate = () => {
    if (type === 0) {
      return (
        <FormItem label="创建日期" labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
          {getFieldDecorator('createTime', {
            rules: [],
            initialValue: [],
          })(<RangePicker style={{ minWidth: '300px', width: '300px' }} />)}
        </FormItem>
      );
    }
    if (type === 10) {
      return (
        <FormItem label="提交日期" labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
          {getFieldDecorator('submitTime', {
            rules: [],
            initialValue: [],
          })(<RangePicker style={{ minWidth: '300px', width: '300px' }} />)}
        </FormItem>
      );
    }
    return (
      <FormItem label="审核日期" labelCol={{ span: 5 }} wrapperCol={{ span: 17 }}>
        {getFieldDecorator('auditTime', {
          rules: [],
          initialValue: [],
        })(<RangePicker style={{ minWidth: '300px', width: '300px' }} />)}
      </FormItem>
    );
  };

  const renderContent = () => (
    <>
      <FormItem label="应用">
        {getFieldDecorator('terminalCode', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择应用" style={{ minWidth: '150px' }} allowClear>
            {appData.map(a => (
              <Option value={a.id} key={a.id}>
                {a.name}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>
      <FormItem label="平台">
        {getFieldDecorator('pfCode', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择平台" style={{ width: '150px' }} allowClear>
            {pfData.map(d => (
              <Option value={d.value} key={d.value}>
                {d.label}
              </Option>
            ))}
          </Select>,
        )}
      </FormItem>
      <FormItem label="页面中文名称" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
        {getFieldDecorator('pageName', {
          rules: [],
          initialValue: [],
        })(<Input style={{ width: '140px' }} placeholder="请输入名称" />)}
      </FormItem>
      <FormItem
        label="埋点中文名称"
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
        style={{ marginRight: 30 }}
      >
        {getFieldDecorator('btnNameOriginal', {
          rules: [],
          initialValue: [],
        })(<Input style={{ width: '160px' }} placeholder="请输入名称" />)}
      </FormItem>
      <FormItem label="埋点版本" labelCol={{ span: 8 }} wrapperCol={{ span: 16 }}>
        {getFieldDecorator('releaseVersion', {
          rules: [],
          initialValue: [],
        })(<Input style={{ width: '160px' }} placeholder="请输入埋点版本" />)}
      </FormItem>
      <FormItem label="是否默认埋点" labelCol={{ span: 10 }} wrapperCol={{ span: 14 }}>
        {getFieldDecorator('isDefault', {
          rules: [],
          initialValue: [],
        })(
          <Select placeholder="请选择是否默认埋点" style={{ minWidth: '180px' }} allowClear>
            <Option value={1} key={1}>
              是
            </Option>
            <Option value={0} key={0}>
              否
            </Option>
          </Select>,
        )}
      </FormItem>
      {dateFormate()}
      <FormItem label="最近修改日期" labelCol={{ span: 7 }} wrapperCol={{ span: 17 }}>
        {getFieldDecorator('updateTime', {
          rules: [],
          initialValue: [],
        })(<RangePicker style={{ minWidth: '270px', width: '270px' }} />)}
      </FormItem>
      {type === 20 && roleType === 'DevOrTest' ? (
        <FormItem label="状态" labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
          {getFieldDecorator('taskStatus', {
            rules: [],
            initialValue: [],
          })(
            <Select placeholder="请选择测试状态" style={{ minWidth: '160px' }} allowClear>
              <Option value={0} key={0}>
                待开发
              </Option>
              <Option value={1} key={1}>
                待测试
              </Option>
              <Option value={2} key={2}>
                测试不通过
              </Option>
            </Select>,
          )}
        </FormItem>
      ) : null}
      <FormItem style={{ marginRight: 0 }}>
        {isAuthBtn('query') ? (
          <Button
            type="primary"
            onClick={() => {
              const params = {
                pageNum: 1,
                pageSize: 10,
              };
              queryData(params);
            }}
            loading={loading.effects['demand/fetchBurypointList']}
          >
            查询
          </Button>
        ) : null}
      </FormItem>
    </>
  );

  return (
    <div style={{ marginTop: '20px' }}>
      <Form {...formLayout} layout="inline">
        {renderContent()}
      </Form>
    </div>
  );
};

const QueryComponentForm = Form.create()(QueryComponent);

export default connect(({ loading }) => ({ loading }))(QueryComponentForm);
