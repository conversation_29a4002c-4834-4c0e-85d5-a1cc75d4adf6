import React, { useState, useEffect } from 'react';
import {
  Radio,
  Button,
  Icon,
  Table,
  Pagination,
  Popover,
  Form,
  Modal,
  Select,
  notification,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { authBtn } from '@/utils/utils';
import QueryForm from './queryForm';
import AddBuriedPoint from './addBuriedPoint';
import { getRoleTypeList, pageDownload } from '../services';

const ProductComponent = ({
  form,
  total = 0,
  dispatch,
  loading,
  tableList = [],
  ssoAuthorityRole: {
    data: { curResources = [] },
  },
}) => {
  const { getFieldDecorator, resetFields } = form;
  const [currentPage, setCurrentPage] = useState(1);
  const [type, setType] = useState(0);
  const [columns, setColumns] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [visible, setVisible] = useState(false);
  const [submitVisible, setSubmitVisible] = useState(false);
  const [editNotVisible, setEditNotVisible] = useState(false);
  const [detail, setDetail] = useState(null);
  const [queryData, setQueryData] = useState({});
  const [isListSubmit, setIsListSubmit] = useState(null);
  const [deleteId, setDeleteId] = useState(null);
  const [deleteVisible, setDeleteVisible] = useState(false);
  const [devData, setDevData] = useState([]);
  const [testData, setTestData] = useState([]);
  const [imageData, setImageData] = useState(null);
  const [imageVisible, setImageVisible] = useState(false);
  const [authList, setAuthList] = useState([]);
  const FormItem = Form.Item;
  const { Option } = Select;
  const formLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 12 },
  };
  const typeChange = e => {
    setType(e.target.value);
    setCurrentPage(1);
    setSelectedRowKeys([]);
    setDetail(null);
    resetFields();
    setIsListSubmit(null);
  };

  const queryListData = params => {
    setQueryData(params);
    setCurrentPage(params.pageNum);
    dispatch({
      type: 'demand/fetchBurypointList',
      payload: params,
    });
  };

  const handlePageChange = (page, pageSize) => {
    setCurrentPage(page);
    const parmas = {
      ...queryData,
      pageNum: page,
      pageSize,
    };
    queryListData(parmas);
  };
  const handlePageSizeChange = (current, pageSize) => {
    const parmas = {
      ...queryData,
      pageNum: currentPage,
      pageSize,
    };
    queryListData(parmas);
  };

  const allCallback = () => {
    const parmas = {
      ...queryData,
    };
    queryListData(parmas);
  };

  const delectData = () => {
    if (!deleteId) return;
    dispatch({
      type: 'demand/burypointbDelete',
      payload: { id: deleteId },
      callback: () => {
        allCallback();
        setDeleteVisible(false);
        setDeleteId(null);
      },
    });
  };

  const getDevList = async () => {
    const { code, data } = await getRoleTypeList({ roleCode: 'kaifarenyuan' });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.name, value: d.id }));
      setDevData(newData);
    }
  };

  const getTestList = async () => {
    const { code, data } = await getRoleTypeList({ roleCode: 'ceshirenyuan' });
    if (code === 1 && data) {
      const newData = data.map(d => ({ label: d.name, value: d.id }));
      setTestData(newData);
    }
  };

  const submitData = value => {
    dispatch({
      type: 'demand/burypointSubmitData',
      payload: { ...value },
      callback: () => {
        const parmas = {
          ...queryData,
        };
        queryListData(parmas);
        setSubmitVisible(false);
      },
    });
  };

  const submit = () => {
    form.validateFields(async (err, values) => {
      if (err) return;
      const params = {
        developmentUid: values.developmentOwner.key,
        developmentOwner: values.developmentOwner.label,
        testOwner: values.testOwner.label,
        testUid: values.testOwner.key,
      };
      if (isListSubmit) {
        const param = {
          ids: isListSubmit,
          ...params,
        };
        submitData(param);
      } else {
        const param = {
          ids: selectedRowKeys,
          ...params,
        };
        submitData(param);
      }
    });
  };

  const getImgData = async url => {
    const params = {
      path: url,
    };
    const { code, data, msg } = await pageDownload(params);
    if (code === 1 && data) {
      setImageData(data);
      setImageVisible(true);
    } else {
      notification.warn({
        message: '获取图片失败',
        description: msg,
      });
    }
  };

  const showImg = url => {
    if (url) {
      getImgData(url);
    }
  };

  const closeModal = () => {
    setVisible(false);
    setDetail(null);
    const parmas = {
      ...queryData,
    };
    queryListData(parmas);
  };

  const openModal = () => {
    setVisible(true);
  };

  const saveDetail = value => {
    setDetail(value);
    openModal();
  };

  const isAuthBtn = key => authList.includes(key);

  const toSubmitColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      render: (_, row) => (
        <div>
          {isAuthBtn('tijiao') ? (
            <Button
              type="link"
              style={{ padding: '0 5px' }}
              onClick={() => {
                setIsListSubmit([row.id]);
                setSubmitVisible(true);
              }}
            >
              提交
            </Button>
          ) : null}
          {isAuthBtn('update') ? (
            <Button type="link" style={{ padding: '0 5px' }} onClick={() => saveDetail(row)}>
              编辑
            </Button>
          ) : null}
          {isAuthBtn('delete') ? (
            <Button
              type="link"
              style={{ padding: '0 5px', color: 'red' }}
              onClick={() => {
                setDeleteId([row.id]);
                setDeleteVisible(true);
              }}
            >
              删除
            </Button>
          ) : null}
        </div>
      ),
    },
  ];

  const passColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '审核日期',
      dataIndex: 'auditTime',
      key: 'auditTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '审核人',
      dataIndex: 'auditUserName',
      key: 'auditUserName',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '开发',
      dataIndex: 'developmentOwner',
      key: 'developmentOwner',
      align: 'center',
    },
    {
      title: '测试',
      dataIndex: 'testOwner',
      key: 'testOwner',
      align: 'center',
    },
  ];

  const submittedColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '提交日期',
      dataIndex: 'submitTime',
      key: 'submitTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '开发',
      dataIndex: 'developmentOwner',
      key: 'developmentOwner',
      align: 'center',
    },
    {
      title: '测试',
      dataIndex: 'testOwner',
      key: 'testOwner',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      render: (_, row) => (
        <div>
          {isAuthBtn('update') ? (
            <Button type="link" onClick={() => saveDetail(row)}>
              编辑
            </Button>
          ) : null}
        </div>
      ),
    },
  ];

  const otherColumns = [
    {
      title: '应用',
      dataIndex: 'terminalName',
      key: 'terminalName',
      align: 'center',
    },
    {
      title: '平台',
      dataIndex: 'pfName',
      key: 'pfName',
      align: 'center',
    },
    {
      title: '页面中文名称',
      dataIndex: 'pageName',
      key: 'pageName',
      align: 'center',
      render: (pageName, row) => (
        <Button type="link" onClick={() => showImg(row.pageUrl)}>
          {pageName}
        </Button>
      ),
    },
    {
      title: '页面英文名称',
      dataIndex: 'pageEngName',
      key: 'pageEngName',
      align: 'center',
    },
    {
      title: '埋点中文名称',
      dataIndex: 'btnNameOriginal',
      key: 'btnNameOriginal',
      align: 'center',
    },
    {
      title: '埋点英文名称',
      dataIndex: 'btnEngName',
      key: 'btnEngName',
      align: 'center',
    },
    {
      title: () => (
        <span>
          是否默认埋点
          <Popover placement="bottom" content="是否为【页面中文名称】的展示埋点">
            <Icon type="question-circle" style={{ marginLeft: 10 }} />
          </Popover>
        </span>
      ),
      dataIndex: 'isDefault',
      key: 'isDefault',
      align: 'center',
      render: isDefault => {
        if (isDefault === 0) {
          return '否';
        }
        if (isDefault === 1) {
          return '是';
        }
        return null;
      },
    },
    {
      title: '自定义参数',
      dataIndex: 'ext',
      key: 'ext',
      align: 'center',
      render: ext => {
        if (Array.isArray(ext)) {
          return ext.map(e => `${e.param}: ${e.desc} `);
        }
        return null;
      },
    },
    {
      title: '埋点版本',
      dataIndex: 'releaseVersion',
      key: 'releaseVersion',
      align: 'center',
    },
    {
      title: '最近修改日期',
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      key: 'remarks',
      align: 'center',
    },
    {
      title: '审核日期',
      dataIndex: 'auditTime',
      key: 'auditTime',
      align: 'center',
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '审核人',
      dataIndex: 'auditUserName',
      key: 'auditUserName',
      align: 'center',
    },
    {
      title: '驳回原因',
      dataIndex: 'auditRejectReason',
      key: 'auditRejectReason',
      align: 'center',
    },
    {
      title: '产品',
      dataIndex: 'productOwner',
      key: 'productOwner',
      align: 'center',
    },
    {
      title: '开发',
      dataIndex: 'developmentOwner',
      key: 'developmentOwner',
      align: 'center',
    },
    {
      title: '测试',
      dataIndex: 'testOwner',
      key: 'testOwner',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'option',
      key: 'option',
      align: 'center',
      render: (_, row) => (
        <div>
          {isAuthBtn('update') ? (
            <Button type="link" onClick={() => saveDetail(row)}>
              编辑
            </Button>
          ) : null}
        </div>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKey, selectedRow) => {
      setSelectedRowKeys(selectedRowKey);
    },
  };

  useEffect(() => {
    if (Array.isArray(curResources)) {
      curResources.map(c => {
        if (c.resourceKey === 'demand') {
          const authBtnList = authBtn(c.subList, 'submit');
          setAuthList(authBtnList);
        }
      });
    }
  }, [curResources]);

  useEffect(() => {
    getDevList();
    getTestList();
    return () => {
      dispatch({
        type: 'demand/saveListData',
        payload: [],
      });
      dispatch({
        type: 'demand/saveTotal',
        payload: 0,
      });
      setCurrentPage(1);
    };
  }, [type]);

  const columnsType = () => {
    if (type === 20) {
      return passColumns;
    }
    if (type === 30) {
      return otherColumns;
    }
    if (type === 0) {
      return toSubmitColumns;
    }
    if (type === 10) {
      return submittedColumns;
    }
  };

  return (
    <div>
      <div>
        <Radio.Group value={type} buttonStyle="solid" onChange={typeChange}>
          <Radio.Button value={0}>待提交</Radio.Button>
          <Radio.Button value={10}>已提交</Radio.Button>
          <Radio.Button value={30}>审核驳回</Radio.Button>
          <Radio.Button value={20}>审核通过</Radio.Button>
        </Radio.Group>
        {isAuthBtn('add') ? (
          <Button type="primary" style={{ marginLeft: '100px' }} onClick={openModal}>
            新增埋点
          </Button>
        ) : null}
      </div>
      <QueryForm type={type} queryListData={queryListData} isAuthBtn={isAuthBtn} />
      {type === 0 ? (
        <>
          <div style={{ height: '40px' }}>
            {isAuthBtn('tijiao') ? (
              <Button
                disabled={selectedRowKeys.length === 0}
                type="primary"
                style={{ margin: '20px 0', float: 'right' }}
                onClick={() => {
                  setIsListSubmit(null);
                  setSubmitVisible(true);
                }}
              >
                批量提交
              </Button>
            ) : null}
          </div>
          <Table
            columns={columnsType()}
            dataSource={tableList}
            style={{ marginTop: '20px' }}
            bordered
            loading={loading.effects['demand/fetchBurypointList']}
            rowKey="id"
            rowSelection={rowSelection}
            scroll={{ x: true, scrollToFirstRowOnChange: true }}
            pagination={false}
          />
        </>
      ) : (
        <Table
          columns={columnsType()}
          dataSource={tableList}
          style={{ marginTop: '20px' }}
          bordered
          loading={loading.effects['demand/fetchBurypointList']}
          rowKey="id"
          scroll={{ x: true, scrollToFirstRowOnChange: true }}
          pagination={false}
        />
      )}
      <Pagination
        style={{ float: 'right', marginTop: '20px' }}
        showQuickJumper
        showSizeChanger
        disabled={total === 0}
        current={currentPage}
        onChange={handlePageChange}
        onShowSizeChange={handlePageSizeChange}
        total={total}
      />
      <AddBuriedPoint visible={visible} closeModal={closeModal} detail={detail} type={type} />
      <Modal
        visible={submitVisible}
        onCancel={() => setSubmitVisible(false)}
        destroyOnClose
        onOk={submit}
        confirmLoading={loading.effects['demand/burypointSubmitData']}
      >
        <Form layout="horizontal" {...formLayout} style={{ marginTop: 15 }}>
          <FormItem label="开发">
            {getFieldDecorator('developmentOwner', {
              rules: [{ required: true, message: '请选择开发人员' }],
              initialValue: [],
            })(
              <Select
                placeholder="请选择开发人员"
                style={{ minWidth: '150px' }}
                labelInValue
                optionFilterProp="children"
                showSearch
                showArrow={false}
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {devData.map(d => (
                  <Option value={d.value} key={d.value}>
                    {d.label}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
          <FormItem label="测试">
            {getFieldDecorator('testOwner', {
              rules: [{ required: true, message: '请选择测试人员' }],
              initialValue: [],
            })(
              <Select
                placeholder="请选择测试人员"
                style={{ minWidth: '150px' }}
                labelInValue
                optionFilterProp="children"
                showSearch
                showArrow={false}
                filterOption={(input, option) =>
                  option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              >
                {testData.map(d => (
                  <Option value={d.value} key={d.value}>
                    {d.label}
                  </Option>
                ))}
              </Select>,
            )}
          </FormItem>
        </Form>
      </Modal>
      <Modal
        visible={deleteVisible}
        onCancel={() => {
          setDeleteVisible(false);
          setDeleteId(null);
        }}
        destroyOnClose
        onOk={delectData}
        width="300px"
        confirmLoading={loading.effects['demand/burypointbDelete']}
      >
        <div style={{ textAlign: 'center', marginTop: '10px' }}>确定删除吗？</div>
      </Modal>
      <Modal
        visible={editNotVisible}
        width="300px"
        footer={null}
        onCancel={() => setEditNotVisible(false)}
      >
        <div style={{ margin: '20px 0 10px 0', textAlign: 'center' }}>
          该信息已完成审核，请刷新页面！
        </div>
        <Button
          size="small"
          type="primary"
          style={{ marginLeft: '90px' }}
          onClick={() => setEditNotVisible(false)}
        >
          我知道了
        </Button>
      </Modal>
      <Modal
        visible={imageVisible}
        onCancel={() => {
          setImageVisible(false);
          setImageData(null);
        }}
        footer={null}
        width="300px"
      >
        {ImageData ? (
          <img src={imageData} alt={imageData} style={{ marginTop: '15px', width: '250px' }} />
        ) : null}
      </Modal>
    </div>
  );
};
const ProductComponentForm = Form.create()(ProductComponent);

export default connect(({ loading, demand, ssoAuthorityRole }) => ({
  loading,
  tableList: demand.tableList,
  total: demand.total,
  ssoAuthorityRole,
}))(ProductComponentForm);
