import request from '@/utils/request';

// 新增埋点
export async function createBurypoint(params = {}) {
  return request('/admin/v1/eventlog/burypoint/add', {
    method: 'POST',
    data: params,
  });
}

// 埋点编辑
export async function burypointEdit(params = {}) {
  return request('/admin/v1/eventlog/burypoint/edit', {
    method: 'POST',
    data: params,
  });
}

// 埋点删除
export async function burypointDelete(params = {}) {
  return request('/admin/v1/eventlog/burypoint/delete', {
    method: 'POST',
    params,
  });
}

// 埋点提交
export async function burypointSubmit(params = {}) {
  return request('/admin/v1/eventlog/burypoint/submit', {
    method: 'POST',
    data: params,
  });
}

// 埋点转派
export async function burypointRedeploy(params = {}) {
  return request('/admin/v1/eventlog/burypoint/redeploy', {
    method: 'POST',
    data: params,
  });
}

// 埋点审核
export async function burypointAudit(params = {}) {
  return request('/admin/v1/eventlog/burypoint/audit', {
    method: 'POST',
    data: params,
  });
}

// 埋点工单任务状态更新 提测
export async function burypointDeployqa(params = {}) {
  return request('/admin/v1/eventlog/burypoint/task/deployqa', {
    method: 'POST',
    params,
  });
}

// 埋点工单任务测试通过/不通过
export async function burypointPass(params = {}) {
  return request('/admin/v1/eventlog/burypoint/task/pass', {
    method: 'POST',
    params,
  });
}
  // 埋点工单查询
export async function burypointQuery(params = {}) {
  return request('/admin/v1/eventlog/burypoint/query', {
    method: 'POST',
    data: params,
  });
}

// 页面下拉选择框
export async function getPageList(params = {}) {
  return request('/admin/v1/eventlog/pageresource/getflatlistonlypage', {
    method: 'POST',
    data: params,
  });
}

// 根据用户角色Code查询角色用户列表
export async function getRoleTypeList(params = {}) {
  return request('/sso/auths/findUsersByRoleCode', {
    params,
  });
}

// 页面下载
export async function pageDownload(params = {}) {
  return request('/admin/v1/eventlog/getPresignedPubUrl', {
    params,
  });
}

// 通过token获取用户信息
export async function getUserInfoByToken() {
  return request('/sso/auths/currentUserRoles', {
    params: {},
  });
}

// 获取用户应用接口
export async function getUserTerminal(params) {
  return request('/admin/v1/eventlog/auth/terminal/list/get', {
    params,
  });
}
