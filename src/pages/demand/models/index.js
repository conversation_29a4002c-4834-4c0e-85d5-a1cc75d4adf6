import { notification, message } from 'antd';
import {
  createBurypoint,
  burypointEdit,
  burypointDelete,
  burypointSubmit,
  burypointRedeploy,
  burypointAudit,
  burypointDeployqa,
  burypointPass,
  burypointQuery } from '../services';

const UserModel = {
  namespace: 'demand',
  state: {
    tableList: [],
    total: 0,
  },
  effects: {
    *fetchBurypointList({ payload }, { call, put }) {
      const response = yield call(burypointQuery, payload)
      if (!response) return;
      const { code, data, msg } = response;
      if (code === 1) {
        yield put({
          type: 'saveListData',
          payload: Array.isArray(data.items) ? data.items : [],
        });
        yield put({
          type: 'saveTotal',
          payload: data ? data.totalNum : 0,
        });
      } else {
        notification.warn({
          message: '请求列表失败',
          description: msg,
        });
      }
    },
    *createBurypointData({ payload, callback }, { call }) {
      const response = yield call(createBurypoint, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('创建成功');
      } else {
        notification.warn({
          message: '创建失败',
          description: msg,
        });
      }
    },
    *editBurypointData({ payload, callback }, { call }) {
      const response = yield call(burypointEdit, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('编辑成功');
      } else {
        notification.warn({
          message: '编辑失败',
          description: msg,
        });
      }
    },
    *burypointbDelete({ payload, callback }, { call }) {
      const response = yield call(burypointDelete, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('删除成功');
      } else {
        notification.warn({
          message: '删除失败',
          description: msg,
        });
      }
    },
    *burypointSubmitData({ payload, callback }, { call }) {
      const response = yield call(burypointSubmit, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('提交成功');
      } else {
        notification.warn({
          message: '提交失败',
          description: msg,
        });
      }
    },
    *redeployBurypoint({ payload, callback }, { call }) {
      const response = yield call(burypointRedeploy, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('转派成功');
      } else {
        notification.warn({
          message: '转派失败',
          description: msg,
        });
      }
    },
    *burypointAuditData({ payload, callback }, { call }) {
      const response = yield call(burypointAudit, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('审核成功');
      } else {
        notification.warn({
          message: '审核失败',
          description: msg,
        });
      }
    },
    *burypointDeployqaSubmit({ payload, callback }, { call }) {
      const response = yield call(burypointDeployqa, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('更新成功');
      } else {
        notification.warn({
          message: '更新失败',
          description: msg,
        });
      }
    },
    *burypointPassSubmit({ payload, callback }, { call }) {
      const response = yield call(burypointPass, payload)
      if (!response) return;
      const { code, msg } = response;
      if (code === 1) {
        callback();
        message.success('更新成功');
      } else {
        notification.warn({
          message: '更新失败',
          description: msg,
        });
      }
    },
  },
  reducers: {
    saveListData(state, action) {
      return { ...state, tableList: action.payload }
    },
    saveTotal(state, action) {
      return { ...state, total: action.payload }
    },
  },
};
export default UserModel;
