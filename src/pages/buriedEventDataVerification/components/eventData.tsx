import React, { useEffect, useState } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import {
  Button,
  Icon,
  Form,
  Select,
  Table,
  Radio,
  Pagination,
  Drawer,
  Tag,
  message,
  Modal,
} from 'antd';
import { connect } from 'dva';
import ReactJson from 'react-json-view';
import { getUrlKey } from '@/utils/utils';
import {
  queryLogTest,
  queryPageList,
  queryPageEventList,
  queryTestBind,
  queryBindInfo,
} from '../../../services/tracking';
import CountDown from './countDown';
import BizPlatTip from '@/components/BizPlatTip';
import styles from '../index.less';
import PSB from '@/styles/pagingSutionBottom.less';

const { Option } = Select;

const EventData = ({ form, currentUser }) => {
  const { getFieldDecorator } = form;
  const [dataParams, setDataParams] = useState([]);
  const [drawerData, setDrawerData] = useState({ checkResult: [], originData: '' });
  const [drawerVisable, setDrawerVisable] = useState(false);
  // 接口轮训状态
  const [isPolling, setIsPolling] = useState(true);
  // 关联页面
  const [eventPage, setEventPage] = useState({ data: [], fetching: true });
  const [eventIds, setEventIds] = useState([]);
  const [totalData, setTotalData] = useState(0);
  const [verificationModalVisable, setVerificationModalVisable] = useState(false);
  const [isDataVerificationEnd, setIsDataVerificationEnd] = useState(false);
  const [pageData, setPageData] = useState(1);
  const [pageSizeData, setPageSizeData] = useState(10);
  const [pageSizeDataSave, setPageSizeDataSave] = useState(10);
  const [pageDataSave, setPageDataSave] = useState(1);
  const [targetTime, setTargetTime] = useState(30 * 60);
  // 表格可滚动高度
  const [tableHeight, setTableHeight] = useState(414);

  const { businessType = 1, applicationType = 1, __blmValidation, urlHost = '' } = getUrlKey(
    decodeURIComponent(window.location.href),
  );

  const eventTypeEnum = [
    { key: 'pv', value: '页面浏览事件' },
    { key: 'click', value: '模块点击事件' },
    { key: 'exposure', value: '模块曝光事件' },
    { key: 'pl', value: '页面离开事件' },
  ];
  // 倒计时结束
  const handleTimeout = () => {
    console.log('倒计时结束timeLeft');
    setIsPolling(false);
    setVerificationModalVisable(true);
    setIsDataVerificationEnd(true);
  };
  const columns = [
    {
      title: '上报序号',
      width: 100,
      dataIndex: 'seq',
      key: 'seq',
      align: 'center',
    },
    {
      title: 'sdk上报时间',
      dataIndex: 'tsFormat',
      key: 'tsFormat',
      align: 'center',
    },
    {
      title: '事件名称',
      dataIndex: 'eventName',
      key: 'eventName',
      align: 'center',
    },
    { title: '事件唯一标识', dataIndex: 'eventUniqueId', key: 'eventUniqueId', align: 'center' },
    {
      title: '事件类型',
      dataIndex: 'eventType',
      key: 'eventType',
      //   width: 100,
      align: 'center',
      render: row => eventTypeEnum.find(item => item.key === row)?.value || '',
    },
    {
      title: '关联页面',
      //   width: 150,
      dataIndex: 'associatePageList',
      key: 'associatePageList',
      align: 'center',
      render: (row, item) => (
        <div>
          {item.associatePageList.map((str, index) => (
            <span key={index}>
              {str}
              <br />
            </span>
          ))}
        </div>
      ),
    },
    {
      title: '页面唯一标识',
      //   width: 100,
      dataIndex: 'pageUniqueIdList',
      key: 'pageUniqueIdList',
      align: 'center',
      render: (row, item) => (
        <div>
          {item.pageUniqueIdList.map((str, index) => (
            <div key={index}>
              {str}
              <br />
            </div>
          ))}
        </div>
      ),
    },
    {
      title: '验证结果',
      width: 100,
      fixed: 'right',
      dataIndex: 'checkResult',
      key: 'checkResult',
      align: 'center',
      render: (row, item) => {
        const checkStatus = item.checkResult?.length;
        return (
          <Tag color={checkStatus ? 'volcano' : 'green'}>
            {checkStatus ? '上报异常' : '上报成功'}
          </Tag>
        );
      },
    },
    {
      title: '操作',
      key: 'operation',
      fixed: 'right',
      width: 180,
      align: 'center',
      render: row => <Button type="link">查看</Button>,
    },
  ];
  const pollingData = (pageData = pageDataSave, pageSizeData = pageSizeDataSave) => {
    // 发送请求到接口，这里使用了fetch方法，你可以使用你喜欢的Ajax库
    console.log(pageData, pageSizeData, 'pageSizeData');
    setPageData(pageData);
    setPageSizeData(pageSizeData);

    const searchParams = {
      businessType: Number(businessType),
      applicationType: Number(applicationType),
      eventUniqueIdList: form.getFieldValue('eventId'),
      timeRange: form.getFieldValue('timeRange') ?? 5,
      debugKey: currentUser ?? __blmValidation,
      pageSize: pageSizeData,
      pageNum: pageData,
      urlHost: urlHost,
      // urlHost: 'eventlog-test.yueyuechuxing.cn',
    };
    queryLogTest(searchParams)
      .then(res => {
        if (res.code === 1 && res.data) {
          setDataParams(res.data.items);
          setTotalData(res.data.totalNum);
          // setDataParams(dataSource);
        } else if (res.code === 2002) {
          handleTimeout();
        } else {
          message.error(res.msg);
        }
      })
      .catch(error => {
        setIsPolling(false);
        console.error('查询失败');
      }).finally(()=>{
        window?.BlmMonitor?.timeSequenceManager.emitEnd('action',{
          sceneId:'eventlog-buriedDataVerification',
          uniqueId:`eventlog-buriedDataVerification-${new Date().getTime()}`
        })

      })
  };
  useEffect(() => {
    // 初始化后立即调用一次
    if (isPolling) {
      window?.BlmMonitor?.timeSequenceManager.emitStart('action',{
        sceneId:'eventlog-buriedDataVerification',
        uniqueId:`eventlog-buriedDataVerification-${new Date().getTime()}`,
        maxTime:'1000'
      })
      pollingData();
    }

    // 设置轮询定时器，每隔一定时间调用一次fetchData方法
    const pollingInterval = setInterval(() => {
      if (isPolling) {
        pollingData();
      }
    }, 1000);
    // 在组件卸载时清除定时器，以防止内存泄漏
    return () => {
      clearInterval(pollingInterval);
    };
  }, [isPolling, pageDataSave, pageSizeDataSave]);
  // 点击每一行打开抽屉
  const handleTableClick = (event, recode) => {
    setDrawerData({ checkResult: recode?.checkResult, originData: recode?.originData });
    setDrawerVisable(true);
  };
  const handleRrawerClose = () => {
    setDrawerVisable(false);
  };
  // 页面下拉数据
  // 关联页面搜索函数
  const handleChangeEventPageId = (value = '') => {
    // pageName
    const searchParams = {
      businessType: Number(businessType),
      applicationType: Number(applicationType),
      pageNum: 1,
      pageSize: 100,
      keyWord: value,
    };
    queryPageList(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        setEventPage({ data: res.data?.items, fetching: !!res.data?.items.length });
      }
    });
  };
  // 事件下拉查询
  const handleChangeEventId = (value = '') => {
    // pageName
    const searchParams = {
      businessType: Number(businessType),
      applicationType: Number(applicationType),
      pageIds: form.getFieldValue('relatePages'),
    };
    queryPageEventList(searchParams).then(res => {
      if (res?.code === 1 && res?.data) {
        setEventIds(res?.data);
      }
    });
  };

  // 计算表格动态可滚动高度
  const calculateTableHeight = () => {
    // 表格区域的总高度
    const containerTable = document.querySelector('.content_table');
    // 可滚动的高度要减去表格表头的高度
    const containerTableThead = document.querySelector('.ant-table-thead');
    if (containerTable && containerTableThead) {
      const height =
        containerTable.getBoundingClientRect().height -
        containerTableThead.getBoundingClientRect().height;
      setTableHeight(height);
    }
  };
  // 页面改变重置事件选项
  useEffect(() => {
    form.setFieldsValue({ timeRange: -1 });
    const searchParams = {
      businessType: Number(businessType),
      applicationType: Number(applicationType),
      platformUserId: __blmValidation,
      urlHost: urlHost,
      // urlHost: 'eventlog-test.yueyuechuxing.cn',
    };
    queryBindInfo(searchParams).then(res => {
      if (res?.code === 1) {
        setTargetTime(res?.data ?? 0);
      }
    });
    // 在加载和窗口大小改变时重新计算表格高度
    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);
    return () => {
      window.removeEventListener('resize', calculateTableHeight);
    };
  }, []);
  // 分页器页码改变
  const handleChangePagination = (page, pageSize) => {
    pollingData(page, pageSize);
    setPageDataSave(page);
  };
  // 分页器条数改变
  const handleOnShowSizeChange = (current, size) => {
    pollingData(1, size);
    setPageSizeDataSave(size);
    setPageDataSave(1);
  };
  // 清空函数
  const handleClear = () => {
    const searchParams = {
      businessType: Number(businessType),
      applicationType: Number(applicationType),
      platformUserId: currentUser ?? __blmValidation,
      urlHost: urlHost,
      // urlHost: 'eventlog-test.yueyuechuxing.cn',
    };
    queryTestBind(searchParams).then(res => {
      if (res?.code === 1) {
        message.success('已清空');
        setDataParams([]);
      } else {
        message.success(res.msg);
      }
    });
  };
  useEffect(() => {
    // 仅暂停场景下会根据搜索条件调用接口
    if (!isPolling) {
      pollingData();
    }
  }, [form.getFieldValue('timeRange'), form.getFieldValue('eventId')]);
  useEffect(() => {
    form.setFieldsValue({ eventId: [] });
    setEventIds([]);
  }, [form.getFieldValue('relatePages')]);
  return (
    <PageHeaderWrapper
      title={false}
      className={PSB.pageHeaderWrapperStyle}
      content={
        <div className={PSB.tabBox}>
          <div className={PSB.content2} style={{ height: 'calc(100vh - 135px)' }}>
            <div>
              <div style={{ display: 'inline-block' }}>
                <BizPlatTip
                  businessType={businessType}
                  applicationType={applicationType}
                ></BizPlatTip>
              </div>
              <span style={{ float: 'right' }}>
                {!isDataVerificationEnd && (
                  <>
                    <Button style={{ marginRight: 12 }} type="dashed" onClick={() => handleClear()}>
                      清空
                    </Button>
                    {isPolling ? (
                      <Button onClick={() => setIsPolling(false)}>暂停</Button>
                    ) : (
                      <Button onClick={() => setIsPolling(true)}>开始</Button>
                    )}
                  </>
                )}
                <Button
                  type="danger"
                  style={{ marginLeft: 12 }}
                  href={`/dataVerification/buriedDataVerification?defaultBusinessType=${businessType}&defaultApplyType=${applicationType}`}
                  onClick={() => setIsPolling(false)}
                >
                  结束验证
                </Button>
              </span>
              <div
                style={{
                  backgroundColor: '#F7F9FD',
                  margin: '10px 0',
                  padding: '6px 0',
                  fontWeight: 700,
                }}
              >
                <div
                  style={{
                    float: 'left',
                    marginRight: '10px',
                    backgroundColor: '#2E7EFF',
                    width: '3px',
                    height: '18px',
                    borderRadius: '5px',
                  }}
                ></div>
                操作日志
              </div>
              <Form layout="inline">
                <Form.Item>
                  {getFieldDecorator('timeRange')(
                    <Radio.Group>
                      <Radio.Button value={-1}>全部</Radio.Button>
                      <Radio.Button value={5}>近5分钟</Radio.Button>
                      <Radio.Button value={10}>近10分钟</Radio.Button>
                    </Radio.Group>,
                  )}
                </Form.Item>
                <Form.Item>
                  <CountDown initialTime={targetTime} onComplete={handleTimeout} />
                </Form.Item>
                {isPolling ? (
                  <Form.Item style={{ color: '#12B248' }}>
                    <Icon type="loading" style={{ marginRight: 6 }} />
                    持续验证中
                  </Form.Item>
                ) : (
                  <Form.Item style={{ color: '#FF9C2A' }}>
                    <Icon type="play-circle" style={{ marginRight: 6 }} />
                    暂停验证
                  </Form.Item>
                )}
                <div style={{ margin: '12px 0' }}>
                  <Form.Item label="页面" style={{ marginRight: 0 }}>
                    {getFieldDecorator(
                      'relatePages',
                      {},
                    )(
                      // 需要传递数组，待调整
                      <Select
                        showSearch
                        placeholder="请选择页面"
                        onFocus={handleChangeEventPageId}
                        onSearch={value => handleChangeEventPageId(value)}
                        filterOption={false}
                        mode="multiple"
                        maxTagCount={2}
                        style={{ minWidth: 200 }}
                      >
                        {eventPage?.data.map(item => (
                          <Option value={item.pageId} key={item.pageId}>
                            {item.pageName}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                  <Icon type="right" style={{ padding: 12 }} />
                  <Form.Item label="事件">
                    {getFieldDecorator(
                      'eventId',
                      {},
                    )(
                      // 需要传递数组，待调整
                      <Select
                        showSearch
                        placeholder="请选择事件"
                        onFocus={handleChangeEventId}
                        mode="multiple"
                        maxTagCount={2}
                        style={{ minWidth: 200 }}
                        filterOption={(input, option) =>
                          option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                      >
                        {eventIds.map(item => (
                          <Option value={item.eventUniqueId} key={item.eventUniqueId}>
                            {item.eventName}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </div>
              </Form>
            </div>
            <div className={`${PSB.content_table} content_table`}>
              <Table
                columns={columns}
                dataSource={dataParams}
                scroll={{ x: '150%', y: tableHeight }}
                pagination={false}
                rowClassName={() => styles.customTable}
                onRow={record => {
                  console.log(record, 'recode');
                  return {
                    onClick: event => {
                      handleTableClick(event, record);
                    }, // 点击行
                  };
                }}
              />
            </div>
            <div className={`${PSB.content_pagination} pageNew`}>
              <Pagination
                showSizeChanger
                showQuickJumper
                defaultCurrent={1}
                total={totalData}
                current={pageData}
                pageSize={pageSizeData}
                showTotal={total => `共 ${total} 条`}
                onChange={(page, pageSize) => handleChangePagination(page, pageSize)}
                onShowSizeChange={(current, size) => handleOnShowSizeChange(current, size)}
                style={{ float: 'right', margin: '16px 0' }}
              />
            </div>
          </div>
          <Drawer
            title="验证详情"
            placement="right"
            closable
            onClose={() => handleRrawerClose()}
            visible={drawerVisable}
            mask={false}
            width={600}
          >
            <div style={{ marginBottom: 12 }}>
              检验结果：
              {drawerData.checkResult.length ? (
                <Tag color="red">上报异常</Tag>
              ) : (
                <Tag color="green">上报成功</Tag>
              )}
            </div>
            {drawerData.checkResult.length ? (
              <div style={{ marginBottom: 12, lineHeight: '26px' }}>
                异常原因：
                {drawerData.checkResult.map((str, index) => (
                  <div style={{ color: '#f5222d' }} key={index}>
                    {str}
                    <br />
                  </div>
                ))}
              </div>
            ) : (
              <></>
            )}
            {drawerData.originData && (
              <ReactJson
                src={JSON.parse(drawerData.originData)}
                theme="monokai" // 主题样式，可根据需要更改
                collapsed={1} // 设置初始折叠级别
                displayDataTypes={false} // 是否显示数据类型
              />
            )}
          </Drawer>
          <Modal
            visible={verificationModalVisable}
            title="验证结束"
            onCancel={() => setVerificationModalVisable(false)}
            footer={[
              <Button key="submit" onClick={() => setVerificationModalVisable(false)}>
                停留当前页
              </Button>,

              <Button
                key="back"
                type="primary"
                href={`/dataVerification/buriedDataVerification?defaultBusinessType=${businessType}&defaultApplyType=${applicationType}`}
                style={{ marginLeft: 12 }}
              >
                返回入口页
              </Button>,
            ]}
          >
            验证时间超过
            <span style={{ color: 'orange' }}>30分钟</span>浏览器
            <span style={{ color: 'orange' }}>cookie失效</span>，请再次验证或停留在当前页面
          </Modal>
        </div>
      }
    ></PageHeaderWrapper>
  );
};

const EventDataForm = Form.create()(EventData);

export default connect(({ user }) => ({
  currentUser: user.currentUserInfo?.email,
}))(EventDataForm);
