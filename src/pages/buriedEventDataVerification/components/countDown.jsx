import React, { useState, useEffect } from 'react';
import {
    Upload,
    Button,
    Icon,
    Form,
    notification,
    Input,
    Select,
    Row,
    Col,
    Table,
    Radio,
  } from 'antd';

const CountdownTimer = ({ initialTime, onComplete }) => {
  const [time, setTime] = useState(initialTime); // 将分钟转换为秒
  const [isRunning, setIsRunning] = useState(false);

  const startCountdown = () => {
    if (!isRunning) {
      setIsRunning(true);
    }
  };

  const stopCountdown = () => {
    if (isRunning) {
      setIsRunning(false);
    }
  };
  useEffect(() => {
    setTime(initialTime)
  }, [initialTime])

  useEffect(() => {
    let timerInterval;

    const tick = () => {
      if (time > 0) {
        setTime(prevTime => prevTime - 1);
      } else {
        stopCountdown();
        if (onComplete) {
          onComplete();
        }
      }
    };

    if (isRunning) {
      timerInterval = setInterval(tick, 1000);
    } else {
      clearInterval(timerInterval);
    }

    return () => {
      clearInterval(timerInterval);
    };
  }, [time, isRunning, initialTime]);

  const formatTime = seconds => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };
  useEffect(() => {
    if (!isRunning) {
        setIsRunning(true);
      }
  }, [])

  return (
    <>
      <span style={{ fontSize: 14, color: '#909399' }}>验证时间剩余: {formatTime(time)}</span>
    </>
  );
}

export default CountdownTimer;
