import * as api from '@/services/tenant';

const Model = {
  namespace: 'tenant',
  state: {
    tenantList: []
  },
  effects: {

    //list
    *list({ payload }, { call, put }) {
      const res = yield call(api.list, payload)
      if (res) {
        const data = res.data
        yield put({
          type: 'sendTenantList',
          payload: Array.isArray(data) ? data : []
        })
        return Promise.resolve(Array.isArray(data) ? data : [])
      } else {
        return Promise.reject()
      }
    }

  },
  reducers: {
    sendTenantList(state, action) {
      return { ...state, tenantList: action.payload }
    }
  }
}
export default Model
