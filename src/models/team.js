import * as api from '@/services/team';

const TeamModel = {
  namespace: 'team',
  state: {
    selectTeams: [],
  },
  effects: {
    *getTeamList({ payload }, { call, put }) {
        const response = yield call(api.getTeamList, payload);
        if (response && response.code === 1) {
          yield put({
            type: 'save',
            payload: { selectTeams: response.data },
          });
          payload.callback && payload.callback(response.data)
        }
      },
  },
  reducers: {
    save(state, action) {
      return { ...state, ...action.payload }
    },
  },
};
export default TeamModel;
