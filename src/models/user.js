/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-29 16:25:23
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-05-13 10:58:33
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/models/user.js
 */
import * as api from '@/services/user';

const UserModel = {
  namespace: 'user',
  state: {
    currentUser: {},
    currResources: null,
    selectUsers: [],
    businessAppList: [], // 查询业务线和应用关系
    businessPowerList: [], // 有权限的业务线
    platformList: [], // 有权限的平台
  },
  effects: {
    *fetchCurrent(_, { call, put }) {
      const res = yield call(api.getUserInfoByToken);
      if (res) {
        const data = res.data || {};
        localStorage.setItem('tenantId', data.tenantIds ? data.tenantIds.split(',')[0] : '');
        if (Array.isArray(data)) {
          if (data.length !== 0) {
            yield put({
              type: 'save',
              payload: { currentUser: data[0] },
            });
          }
        }
        window.currentUser = data;
        return Promise.resolve(data);
      }
      return Promise.reject();
    },

    *fetchUser({ payload }, { call, put }) {
      const res = yield call(api.getUser);
      if (res) {
        const data = res.data || {};
        yield put({
          type: 'save',
          payload: { currentUserInfo: data },
        });
        console.log('data', data);

        window.currentUserInfo = data;
        // 用户行为埋点SDK，写入用户id
        window.BlmAnalysis && window.BlmAnalysis.set('uid', data.id);
        window?.BlmMonitor?.config({ uid: data?.id });
        return Promise.resolve(data);
      }
      return Promise.reject();
    },

    *getAllUser({ payload }, { call, put }) {
      const response = yield call(api.getAllUser, payload);
      if (response && response.code === 1) {
        yield put({
          type: 'save',
          payload: { selectUsers: response.data },
        });
        payload.callback && payload.callback();
      }
    },
    *getBusinessAppList({ payload }, { call, put }) {
      const response = yield call(api.getBusinessAppList, payload);
      if (response && response.code === 1) {
        yield put({
          type: 'save',
          payload: { businessAppList: response.data },
        });
      }
      return Promise.reject();
    },
    *getPlatformList({ payload }, { call, put }) {
      yield put({
        type: 'save',
        payload: { platformList: payload },
      });
    },
    *getBusinessPowerList({ payload }, { call, put }) {
      yield put({
        type: 'save',
        payload: { businessPowerList: payload },
      });
    },
  },
  reducers: {
    save(state, action) {
      return { ...state, ...action.payload };
    },
  },
};
export default UserModel;
