import React, { useEffect, useState } from 'react';
import { connect } from 'dva';
import { Select } from '@blmcp/ui';
import { formatMessage } from 'umi-plugin-react/locale';
import Avatar from './AvatarDropdown';
import SelectLang from '../SelectLang';
import styles from './index.less';
import { getAllAuthBtn } from '@/utils/utils';

const { Option } = Select;
const GlobalHeaderRight = props => {
  const { dispatch, theme, layout, location, ssoAuthorityRole, businessAppList } = props;
  let className = styles.right;

  if (theme === 'dark' && layout === 'topmenu') {
    className = `${styles.right}  ${styles.dark}`;
  }

  // 拥有业务下拉框的页面路由集合
  const showVisableList = [
    '/buryingPointNew/eventTracking', // 事件配置
    '/buryingPointNew/pageTracking', // 页面管理
    '/buryingPointNew/reviewEventTracking', // 事件审核
    '/dataVerification/buriedDataVerification', // 埋点验证
    '/dataAnalysis/overviewTracking', // 概览
    '/dataAnalysis/eventAnalysis', // 事件分析
  ];

  // 对businessType进行初始化
  const [businessType, setBusinessType] = useState(null);
  const [businessList, setBusinessList] = useState([]);
  // 当前账号的所有rk
  const curResources = ssoAuthorityRole.data?.curResources || [];

  useEffect(() => {
    // 将平台业务的值传到sessionStorage中保存起来
    // sessionStorage.setItem('businessType', JSON.stringify(businessType));

    if (!curResources.length || !businessAppList.length) {
      return;
    }

    const authBtnList = getAllAuthBtn(curResources);
    const options = [];
    // 将映射关系表中有权限的业务线取出来
    businessAppList.forEach(item => {
      if (authBtnList.includes(item.resourceKey)) {
        options.push(item);
      }
    });
    setBusinessList(options);
    // 更新redux中的业务线
    if (dispatch) {
      dispatch({
        type: 'user/getBusinessPowerList',
        payload: options,
      });
    }
    const sessionBusinessType = JSON.parse(sessionStorage.getItem('businessType'));
    const arr = options?.map(i => i.businessType);
    let updatedBusinessType = null;
    if (arr?.includes(sessionBusinessType)) {
      updatedBusinessType = sessionBusinessType;
    } else if (arr.length > 0) {
      updatedBusinessType = arr[0];
    }
    setBusinessType(updatedBusinessType);
    sessionStorage.setItem('businessType', updatedBusinessType);

    // 从映射表中找到对应的业务线集合
    const businessInfo = options.find(i => i.businessType === updatedBusinessType) || {};
    // 根据业务线获取有权限的平台数据
    const platformArr = []; // 有权限&&有映射关系
    const platformPowerArr = []; // 只有权限，找不到映射关系的数据集合
    curResources.forEach(item => {
      if (item?.resourceKey === businessInfo?.resourceKey) {
        item?.subList?.forEach((info, index) => {
          // info?.name && info?.resourceKey && platformArr.push(getMapping(businessInfo, info));
          if (info?.name && info?.resourceKey) {
            const obj = getMapping(businessInfo, info);
            // 只获取有权限&&有映射关系的数据；并按照接口中seq字段排序
            if (obj.seq) {
              platformArr[obj.seq - 1] = obj;
            }
            // else {
            //   platformPowerArr.push(obj);
            // }
          }
        });
      }
    });
    const mergedArr = platformArr.concat(platformPowerArr);
    // 去除platformArr中为空的元素
    const filteredArr = mergedArr.filter(
      item => item !== null && item !== undefined && typeof item === 'object',
    );
    // 更新redux中的平台数据（集合范围为:接口返回数据和权限的交集）
    if (dispatch) {
      dispatch({
        type: 'user/getPlatformList',
        payload: filteredArr,
      });
    }
  }, [businessType, businessAppList, curResources]);

  // 获取平台的对应关系
  const getMapping = (businessInfo, platformPower) => {
    let platformObj = {
      applicationType: null,
      applicationName: '',
      applicationDesc: platformPower.name,
      resourceKey: platformPower.resourceKey,
      seq: null, // 展示顺序
    };
    businessInfo?.applicationList?.forEach(i => {
      if (i.resourceKey === platformPower.resourceKey) {
        platformObj = { ...i };
      }
    });
    return platformObj;
  };

  // 获取业务线数据
  const getBusinessList = () => {
    const authBtnList = getAllAuthBtn(curResources);
    const options = [];
    // 将映射关系表中有权限的业务线取出来
    businessAppList.forEach(item => {
      if (authBtnList.includes(item.resourceKey)) {
        options.push(item);
      }
    });

    return options.map(item => <Option value={item?.businessType}>{item?.businessDesc}</Option>);
  };

  const handleChange = val => {
    sessionStorage.setItem('businessType', val);
    // 更新页面
    window.location.reload();
  };

  return (
    <div className={className}>
      {/* 业务类型 */}
      {location.pathname && showVisableList.includes(location.pathname) && (
        <div>
          <Select
            value={businessType}
            onChange={handleChange}
            style={{ width: '200px', margin: '0 20px' }}
          >
            {businessList?.map(item => (
              <Option value={item?.businessType}>{item?.businessDesc}</Option>
            ))}
          </Select>
        </div>
      )}
      <Avatar />
      {/* <SelectLang className={styles.action} /> */}
    </div>
  );
};

export default connect(({ settings, ssoAuthorityRole, user }) => ({
  theme: settings.navTheme,
  layout: settings.layout,
  ssoAuthorityRole,
  businessAppList: user.businessAppList,
}))(GlobalHeaderRight);
