import React, { useEffect, useState } from 'react';
import { Upload, message, Image } from '@blmcp/ui';
import { CloseOutlined, LoadingOutlined, PlusOutlined, EyeOutlined } from '@ant-design/icons'


import { pageUpload } from '../../pages/pageTracking/services'
import style from './index.less'

const UploadPicture = ({
  pictureUrl = '',
  isEditImg = true,
  imgWidth = '100px',
  imgHeight = '100px',
  maskText = '', // 自定义蒙层上的文字
  getUploadPic,
}) => {
  const [showUpload, setShowUpload] = useState(true)
  const [state, setState] = useState({
    imageUrl: '',
    loading: false,
  })


  useEffect(() => {
    if (pictureUrl) {
      setState({ ...state, imageUrl: pictureUrl })
      setShowUpload(false)
    }
  }, [pictureUrl])

  function beforeUpload(file) {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/bmp';
    if (!isJpgOrPng) {
      message.error('图片格式错误，请重新上传');
    }
    const isLt2M = file.size / 1024 / 1024 < 10;
    if (!isLt2M) {
      message.error('图片超过10M，请重新上传');
    }
    return isJpgOrPng && isLt2M
  }

  // 上传图片
  const uploadImg = async options => {
    setState({ ...state, loading: true })
    const { file, onError } = options;
    const formData = new FormData();
    formData.append('file', file)
    formData.append('contentType', file.type)
    formData.append('group', 'public')
    formData.append('module', 'image')
    formData.append('mode', 'PUBLIC_READ_WITH_CDN')
    const res = await pageUpload(formData)
    if (res && res.url) {
      setState({ ...state, imageUrl: res.url })
      // 将接口返回的url传给父组件
      getUploadPic(res.url)
      setShowUpload(false)
    } else {
      onError('图片上传失败')
      setState({ ...state, loading: false });
      message.error('上传失败,请重新上传')
    }
  }
  function handleChange(info) {
    if (info.file.status === 'uploading') {
      setState({ ...state, loading: true });
      return;
    }
    if (info.file.status === 'done') {
      setState({ ...state, loading: false })
    }
  }
  // 删除图片
  function handleRemove() {
    setState({ ...state, imageUrl: '', loading: false })
    setShowUpload(true)
    getUploadPic('')
  }

  return (
    <div>
      { showUpload ? (
        <Upload
          accept="image/png, image/jpeg, image/jpg, image/bmp"
          listType="picture-card"
          className="avatar-uploader"
          showUploadList={false}
          customRequest={uploadImg}
          beforeUpload={beforeUpload}
          onChange={handleChange}
        >
          {/* 添加图片区域 */}
          {
            state.loading ? (<LoadingOutlined rev="horizontal"/>) : (<PlusOutlined rev="horizontal"/>)
          }
        </Upload>
      ) : (
        <div
          className={style.previewBoxItem}
          style={{ width: `${imgWidth}`, height: `${imgHeight}` }}
        >
          {
            isEditImg ? (
              <div
                className={style.previewBoxItemClose}
                onClick={() => handleRemove()}
              > <CloseOutlined rev="horizontal"/></div>
            ) : (<></>)
          }
          <Image
            width="100%"
            height="100%"
            src={state.imageUrl}
            preview={{ mask: <span><EyeOutlined rev="horizontal"/>{maskText}</span> }}
          />
        </div>
      )}
      {
        isEditImg ? (
          <span style={{ color: '#909399' }}>支持png、jpg、jpeg、bmp格式的图片，每张图片最大10M</span>
        ) : (<></>)
      }
    </div>
  );
};

export default UploadPicture
