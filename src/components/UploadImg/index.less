.previewBoxItem {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  width: 110px;
  height: 110px;
  margin: 10px 10px 0 0;
  background-color: #fff;
  border-radius: 6px;
  box-sizing: content-box;

  .previewBoxItemClose {
    display: none;
  }
  &:hover {
    .previewBoxItemClose {
      display: block;
      z-index: 99;
      position: absolute;
      top: -10px;
      right: -10px;
      font-size: 14px;
      text-align: center;
      line-height: 20px;
      width: 20px;
      height: 20px;
      color: #fff;
      font-size: 12px;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      .el-icon-close {
        cursor: pointer;
      }
    }
  }
}

.iconIn,.iconOut {
  margin: 5px;
  font-size: 30px;
  color: #979899;
  &:hover {
    font-size: 33px;
    color: #333333;
  }
}
