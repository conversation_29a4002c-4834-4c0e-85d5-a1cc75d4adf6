import React from 'react';
import { Tooltip } from '@blmcp/ui';
import { useTooltipShow } from './useTooltipShow';
import styles from './index.less';

const IsShowToolTip = ({ item = '', cardNum = '', showTooltip = true }) => {
  const { tooltipEnable, textRef } = useTooltipShow(item, cardNum);
  const BoxDOM = (
    <span className={styles.ellipsisBox} ref={textRef}>
      {item}
    </span>
  );
  return tooltipEnable && showTooltip ? <Tooltip title={item}>{BoxDOM}</Tooltip> : BoxDOM;
};

export default IsShowToolTip;
