import React, { useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Divider,
  Input,
  message,
  Popover,
  Segmented,
  Select,
  Spin,
  Tree,
} from '@blmcp/ui';
import { CloseOutlined } from '@ant-design/icons';
import VirtualList from 'rc-virtual-list';
import { cloneDeep } from 'lodash';
import styles from './index.less';
import IsShowToolTip from './components/IsShowToolTip';

interface TaskCarTeamNewProps {
  form: any;
  placeholder: any;
  excludedDomainShow: Boolean;
  // 运力公司弹窗状态改变函数
  tenantOptions: Array<any>;
  // 运力公司中文名称拼接
  setTaskCarTeamIdList: (e) => void;
  defaultValueIds: Array<any>; // 默认选中的ID集合
  taskCarTeamIdList: Array<any>;
  valueType: Number; // 平台类型
  platformEliminate: Boolean; // 排除以上选项状态
}

const { TextArea } = Input;
const TenantSelectNew: React.FC<TaskCarTeamNewProps> = ({
  form,
  excludedDomainShow = true,
  tenantOptions,
  defaultValueIds,
  cityCodeSelect,
  selectCallBack,
  switchTag,
  valueType,
  placeholder = '',
  platformEliminate,
  spinning,
}) => {
  // const [messageApi, contextHolder] = message.useMessage();

  // 平台类型 平台名称-1； 平台分组-2
  const [platformType, setPlatformType] = useState(valueType);

  // 运力公司数据集合
  const [taskCarTeamData, setTaskCarTeamData] = useState([]);
  // 弹窗内选中运力公司item集合
  const [taskCarTeamSelectList, setTaskCarTeamSelectList] = useState([]);
  // 右侧展示运力公司数据集合
  const [taskCarTeamSelectShowList, setTaskCarTeamSelectShowList] = useState([]);
  // 弹窗内选中运力公司id集合
  const [taskCarTeamSelectIdList, setTaskCarTeamSelectIdList] = useState([]);
  // 左侧搜索框value值
  const [leftSearchValue, setLeftSearchValue] = useState('');
  // 右侧搜索框value值
  const [rightSearchValue, setRightSearchValue] = useState('');
  // 右侧添加以上全部按钮选中状态
  const [allCheckStatus, setAllCheckStatus] = useState(false);
  // 右侧全部按钮是否为半选状态
  const [isIndeterminateStatus, setIndeterminateStatus] = useState(false);
  // 当前左侧可见（搜索）已选item
  const [searchCheckedItemList, setSearchCheckedItemList] = useState([]);
  // 平台组件弹窗打开状态
  const [taskCarTeamPopoverOpen, setTaskCarTeamPopoverOpen] = useState(false);
  const [quickAddOpen, setQuickAddOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  // 排除以上选项选中状态
  const [eliminate, setEliminate] = useState(false);
  // 最终选中的值
  const [theFinalSelectId, setTheFinalSelectId] = useState([]);
  // 外层select的下拉值集合
  const [selectOptions, setSelectOptions] = useState([]);
  // loading
  // const [spinning, setSpinning] = useState(false);

  // 运力公司添加确认
  const handleAddClick = (isPreserve, isClose = true) => {
    const selectInfo = {
      eliminate, // 排除状态
      platformType, // 平台类型
      selectIdList: [], // 选中集合的Id
      selectItemList: [], // 选中的集合
    };

    if (isPreserve) {
      // 外层input框需要展示选中项的文字
      const handleSelectedTitleList = taskCarTeamSelectList.map(obj => obj.tenantIdAndName);
      // 获取选中项的ID集合
      const handleSelectedIdList = taskCarTeamSelectList.map(obj => obj.tenantId);
      form.setFieldValue('tenantIds', handleSelectedIdList);
      setTheFinalSelectId(handleSelectedIdList);
      // setTaskCarTeamIdList(handleSelectedTitleList);
      // 将选中的信息通过回调函数传到外层
      selectCallBack({
        eliminate, // 排除状态
        platformType, // 平台类型
        selectIdList: handleSelectedIdList, // 选中集合的Id
        selectLabelList: handleSelectedTitleList, // 选中的中文名称集合
      });
    }

    // 关闭弹窗
    if (isClose) {
      setQuickAddOpen(false);
      // 关闭组件弹窗
      setTaskCarTeamPopoverOpen(false);
    }
  };
  // 排除以上选项
  const eliminateChange = val => {
    setEliminate(val);
  };
  // 左侧区域搜索
  const handleLeftSearch = (e: any) => {
    setLeftSearchValue(e.target.value);
    // 源数据中过滤
    const searchTaskCarTeamData = tenantOptions.filter(item =>
      item.tenantIdAndName.includes(e.target.value),
    );
    setTaskCarTeamData(searchTaskCarTeamData);
  };
  // 右侧区域搜索
  const handleRightSearch = (e: any, isChange = true, checkedNodes = []) => {
    if (isChange) {
      setRightSearchValue(e);
    }
    const sourceData = isChange ? taskCarTeamSelectList : checkedNodes;
    // 选中运力公司数据中过滤
    const searchTaskCarTeamData = sourceData.filter(item => item.tenantIdAndName.includes(e));
    setTaskCarTeamSelectShowList(searchTaskCarTeamData);
  };
  // 运力公司树组件选中
  const handleTaskCarTeamCheck = (checkedKeys, e, type) => {
    let isSelected;
    if (type === 'check') {
      isSelected = e.checked;
    } else if (type === 'select') {
      if (taskCarTeamSelectIdList?.length) {
        isSelected = !taskCarTeamSelectIdList.includes(e.node.tenantId);
      } else {
        isSelected = true;
      }
    }
    let filterTaskIdList = [];
    if (isSelected) {
      filterTaskIdList = taskCarTeamSelectIdList.concat(e.node.tenantId);
    } else {
      filterTaskIdList = taskCarTeamSelectIdList.filter(item => item !== e.node.tenantId);
    }
    setTaskCarTeamSelectIdList(filterTaskIdList);
    let filterTaskList = [];
    if (isSelected) {
      filterTaskList = taskCarTeamSelectList.concat(e.node);
    } else {
      filterTaskList = taskCarTeamSelectList.filter(item => item.tenantId !== e.node.tenantId);
    }
    // 选中运力item集合
    setTaskCarTeamSelectList(filterTaskList);
    // 选择之后针对右侧搜索项进行过滤
    handleRightSearch(rightSearchValue, false, filterTaskList);
  };
  // 删除选中的运力公司
  const handelDeleteItem = deleteItem => {
    // 删除后剩余的全量数据
    const taskCarTeamSelectListFilter = taskCarTeamSelectList.filter(
      item => item.tenantId !== deleteItem.tenantId,
    );
    // 删除后剩余的需要展示数据
    const taskCarTeamSelectListShowFilter = taskCarTeamSelectShowList?.filter(
      item => item.tenantId !== deleteItem.tenantId,
    );
    // 删除后运力公司ID集合
    const taskCarTeamSelectListIdFilter = taskCarTeamSelectIdList.filter(
      item => item !== deleteItem.tenantId,
    );
    setTaskCarTeamSelectList(taskCarTeamSelectListFilter);
    setTaskCarTeamSelectShowList(taskCarTeamSelectListShowFilter);
    setTaskCarTeamSelectIdList(taskCarTeamSelectListIdFilter);
  };
  // 左侧添加以上全部选项
  const handleAllCheckClick = checkStatus => {
    if (searchCheckedItemList.length) {
      setAllCheckStatus(true);
    } else {
      setAllCheckStatus(false);
    }
    setIndeterminateStatus(false);
    if (
      checkStatus ||
      (searchCheckedItemList.length && searchCheckedItemList.length !== taskCarTeamData.length)
    ) {
      // 当前右侧未被选中的运力公司集合
      const unCheckedTaskList = taskCarTeamData.filter(
        item => !taskCarTeamSelectIdList.includes(item.tenantId),
      );
      const fieldValuesArray = unCheckedTaskList.map(obj => obj.tenantId);
      const allCheckedTaskIdList = taskCarTeamSelectIdList.concat(fieldValuesArray);
      const allCheckedTaskList = taskCarTeamSelectList.concat(unCheckedTaskList);
      setTaskCarTeamSelectIdList(allCheckedTaskIdList);
      // 选中运力item集合
      setTaskCarTeamSelectList(allCheckedTaskList);
      // 选择之后针对右侧搜索项进行过滤
      handleRightSearch(rightSearchValue, false, allCheckedTaskList);
    } else {
      // 取消当前选中全部
      const taskCarTeamAllIdList = taskCarTeamData.map(obj => obj.tenantId);
      const taskCarTeamCancelIdList = taskCarTeamSelectIdList.filter(
        item => !taskCarTeamAllIdList.includes(item),
      );
      setTaskCarTeamSelectIdList(taskCarTeamCancelIdList);
      const taskCarTeamCancelItemList = taskCarTeamSelectList.filter(
        item => !taskCarTeamAllIdList.includes(item.tenantId),
      );
      // 选中运力item集合
      setTaskCarTeamSelectList(taskCarTeamCancelItemList);
      // 选择之后针对右侧搜索项进行过滤
      handleRightSearch(rightSearchValue, false, taskCarTeamCancelItemList);
    }
  };
  // 处理右侧清空逻辑
  const handleCancelSelectedItem = () => {
    if (rightSearchValue) {
      const filterShowIdList = taskCarTeamSelectShowList.map(obj => obj.tenantId);
      setTaskCarTeamSelectIdList(
        taskCarTeamSelectIdList.filter(item => !filterShowIdList.includes(item)),
      );
      setTaskCarTeamSelectList(
        taskCarTeamSelectList.filter(item => !filterShowIdList.includes(item.tenantId)),
      );
      setTaskCarTeamSelectShowList([]);
    } else {
      setTaskCarTeamSelectIdList([]);
      setTaskCarTeamSelectList([]);
      setTaskCarTeamSelectShowList([]);
    }
  };
  // 搜索高亮处理函数
  const handleHighLight = (searchValue, mainText, item) => {
    const regex = new RegExp(`(${searchValue})`, 'g');
    const parts = mainText.split(regex).map((part, index) => {
      if (regex.test(part)) {
        return (
          <span key={index} style={{ color: '#2761F3' }}>
            {part}
          </span>
        );
      }
      return part;
    });
    return (
      <div>
        <IsShowToolTip item={parts} showTooltip={false}></IsShowToolTip>
        {platformType === 1 && item?.tenantenvtype === 2 ? (
          <div className={styles.dataMarking}>测</div>
        ) : null}
      </div>
    );
  };
  // 平台类型切换
  const segmentedChange = value => {
    setPlatformType(value);
    switchTag(value);
    // 清空左右两个搜索框的值
    setLeftSearchValue('');
    setRightSearchValue('');
    // 清空右侧展示的数据
    setTaskCarTeamSelectShowList([]);
    // 清空左侧选中状态
    setTaskCarTeamSelectIdList([]);
    setTaskCarTeamSelectList([]);
    // 排除以上选项选中状态置为false
    setEliminate(false);
    // setSpinning(true)
  };

  // 点击平台名称/平台组件
  useEffect(() => {
    // setSpinning(true)
  }, [platformType]);
  // 初始化左侧列表数据
  useEffect(() => {
    if (taskCarTeamPopoverOpen) {
      // 平台类型初始化
      // setPlatformType(valueType);
      // 排除以上选项状态
      setEliminate(platformEliminate);

      let taskCarTeamDataCopy = [];
      const taskCarTeamIdSavedCopy = [];
      // 初始化左侧展示的数据集合
      setTaskCarTeamData(tenantOptions);
      taskCarTeamDataCopy = tenantOptions;
      const taskCarTeamIdSavedItem = taskCarTeamDataCopy?.filter(item =>
        defaultValueIds?.includes(item.tenantId),
      );
      setTaskCarTeamSelectIdList(defaultValueIds); // taskCarTeamSelectIdList 弹窗内运力公司集合(左侧默认够选的数据集合)
      setTaskCarTeamSelectList(taskCarTeamIdSavedItem); // taskCarTeamSelectList 弹窗内运力公司item集合
      setTaskCarTeamSelectShowList(taskCarTeamIdSavedItem); // taskCarTeamSelectShowList 右侧展示运力公司数据集合
      console.log(
        '初始化',
        valueType,
        taskCarTeamDataCopy,
        defaultValueIds,
        taskCarTeamIdSavedItem,
      );
    }

    setSelectOptions(tenantOptions);
    setTheFinalSelectId(defaultValueIds);
  }, [
    taskCarTeamPopoverOpen,
    JSON.stringify(tenantOptions),
    defaultValueIds,
    valueType,
    platformEliminate,
  ]);

  // 处理添加以上全部按钮状态
  useEffect(() => {
    if (taskCarTeamPopoverOpen) {
      if (searchCheckedItemList.length === 0) {
        setAllCheckStatus(false);
        setIndeterminateStatus(false);
      } else if (searchCheckedItemList.length === taskCarTeamData.length) {
        setAllCheckStatus(true);
        setIndeterminateStatus(false);
      } else {
        setIndeterminateStatus(true);
      }
    }
  }, [searchCheckedItemList, taskCarTeamData, taskCarTeamPopoverOpen]);
  useEffect(() => {
    if (!taskCarTeamPopoverOpen) {
      setLeftSearchValue('');
      setRightSearchValue('');
    }
  }, [taskCarTeamPopoverOpen, defaultValueIds, tenantOptions, valueType]);
  // 处理当前筛选区域下所选择条数
  useEffect(() => {
    if (taskCarTeamPopoverOpen) {
      const taskCarTeamAllIdList = taskCarTeamData.map(obj => obj.tenantId);
      const filterCheckedIdList = taskCarTeamSelectIdList?.filter(item =>
        taskCarTeamAllIdList.includes(item),
      );
      setSearchCheckedItemList(filterCheckedIdList);
      // setSpinning(false)
    }
  }, [
    leftSearchValue,
    taskCarTeamSelectIdList,
    taskCarTeamPopoverOpen,
    JSON.stringify(taskCarTeamData),
  ]);

  const refrain = (arr: any) => {
    // 定义tmp用于存储空数组
    const tmp: [] = [];
    if (Array.isArray(arr)) {
      // 找到重复的数据并保存到tmp中
      arr
        .concat()
        .sort()
        .sort((a, b) => {
          // a和b相等 && a元素不为空白字符串 && tmp中不存在
          if (a === b && !a.match(/^[ ]*$/) && tmp.indexOf(a) === -1) {
            tmp.push(a);
          }
        });
    }
    return tmp;
  };
  // 快捷添加确认
  const handleQuickAddOk = e => {
    // e.preventDefault();
    // e.stopPropagation();
    const inputList = cloneDeep(inputValue).split(/[\n]/);
    // 1.判断是否存在重复
    const refrainList = refrain(inputList);
    if (refrainList.length) {
      message.open({
        // messageApi.open({
        type: 'warning',
        content: `存在重复平台名称， [${refrainList.toString()}]`,
      });
      return;
    }

    const unMatchList = [];
    const carTeamValue = [];
    const carTeamName = [];
    // 2、判断是否全在list里
    inputList.forEach(item => {
      const teamName = item.trim();
      const carTeamInfo = tenantOptions.find(f => f.tenantIdAndName === teamName);
      // 批量添加不可以添加不限选项
      if (carTeamInfo && carTeamInfo?.tenantId !== 'ALL') {
        carTeamValue.push(carTeamInfo.tenantId);
        carTeamName.push(teamName);
      } else if (!teamName.match(/^[ ]*$/)) {
        unMatchList.push(teamName);
      }
    });
    if (unMatchList.length > 0) {
      message.open({
        // messageApi.open({
        type: 'warning',
        content: `您输入的平台名称 [${unMatchList.toString()}] 不存在`,
      });
      return;
    }
    if (carTeamValue.length === 0) {
      message.open({
        // messageApi.open({
        type: 'warning',
        content: '内容不能为空',
      });
      return;
    }

    setQuickAddOpen(false);
    const carTeamAddList = tenantOptions.filter(item => carTeamValue?.includes(item.tenantId));
    let checkList = [];
    let checkItemList = [];
    // 已有选中运力公司，添加后进行去重
    if (taskCarTeamSelectIdList?.length) {
      checkList = [...new Set([...taskCarTeamSelectIdList, ...carTeamValue])];
      const quickFilterItem = carTeamAddList.filter(
        item => !taskCarTeamSelectIdList?.includes(item?.tenantId),
      );
      checkItemList = [...taskCarTeamSelectList, ...quickFilterItem];
    } else {
      checkList = carTeamValue;
      checkItemList = carTeamAddList;
    }
    // 选中运力item集合
    setTaskCarTeamSelectList(checkItemList);
    // 选择之后针对右侧搜索项进行过滤
    handleRightSearch(rightSearchValue, false, checkItemList);
    // 选中运力id集合
    setTaskCarTeamSelectIdList(checkList);
    setInputValue('');
  };
  // 快捷添加取消
  const handleQuickAddCancel = () => {
    setQuickAddOpen(false);
  };
  const quickAddContent = (
    <div className={styles.quickAdd}>
      <div className={styles['quickAdd-title']}>快捷输入</div>
      <div className={styles['quickAdd-content']}>
        <TextArea
          rows={4}
          onChange={e => setInputValue(e.target.value)}
          placeholder="请输入，需确保输入名称的完整性和正确性"
          style={{ height: 214 }}
          value={inputValue}
        />
      </div>
      <div className={styles['quickAdd-footer']}>
        <Button size="small" style={{ marginRight: 8 }} onClick={handleQuickAddCancel}>
          取消
        </Button>
        <Button size="small" type="primary" onClick={handleQuickAddOk}>
          确定并添加
        </Button>
      </div>
    </div>
  );
  useEffect(() => {
    if (cityCodeSelect?.length === 0) {
      setTaskCarTeamPopoverOpen(false);
      setQuickAddOpen(false);
    }
  }, [cityCodeSelect]);
  useEffect(() => {
    if (!quickAddOpen) {
      setInputValue('');
    }
  }, [quickAddOpen]);

  const popoverContent = (
    <Spin spinning={spinning}>
      <div className={styles.taskCarTeam}>
        <div className={styles['taskCarTeam-content']}>
          <div className={styles['taskCarTeam-content_left']}>
            <div className={styles['taskCarTeam-content_left_top']}>
              平台选择（
              {`${searchCheckedItemList?.length}/${taskCarTeamData?.length}`}）
            </div>
            <Divider style={{ margin: 0 }} />
            <div className={styles['taskCarTeam-content_left_content']}>
              <Segmented
                size="middle"
                value={platformType}
                block
                style={{ marginBottom: '8px' }}
                options={[
                  {
                    label: '平台名称',
                    value: 1,
                  },
                  {
                    label: '平台分组',
                    value: 2,
                  },
                ]}
                onChange={value => segmentedChange(value)}
              />
              <div style={{ height: '274x' }}>
                {platformType === 1 ? (
                  <Input
                    placeholder={`请输入${placeholder}`}
                    style={{ margin: '8px 0', width: 304 }}
                    // prefix={<BLMIconFont type="BLM-ic-search-o" />}
                    onChange={(e: any) => handleLeftSearch(e)}
                    value={leftSearchValue}
                  />
                ) : null}
                {/* 226-48px */}
                {taskCarTeamData.length ? (
                  <Tree
                    checkable
                    checkedKeys={taskCarTeamSelectIdList} // 选中复选框的树节点
                    onCheck={(checkedKeys, e) => handleTaskCarTeamCheck(checkedKeys, e, 'check')}
                    onSelect={(selectedKeys, e) =>
                      handleTaskCarTeamCheck(selectedKeys, e, 'select')
                    }
                    fieldNames={{
                      title: 'tenantIdAndName',
                      key: 'tenantId',
                    }}
                    treeData={taskCarTeamData}
                    height={226}
                    blockNode
                    titleRender={nodeData =>
                      leftSearchValue.length ? (
                        handleHighLight(leftSearchValue, nodeData.tenantIdAndName, nodeData)
                      ) : (
                        <div>
                          <IsShowToolTip
                            item={nodeData.tenantIdAndName}
                            showTooltip={false}
                          ></IsShowToolTip>
                          {nodeData?.tenantEnvType === 2 ? (
                            <div className={styles.dataMarking}>测</div>
                          ) : null}
                        </div>
                      )
                    }
                  />
                ) : (
                  <div className={styles['taskCarTeam-content_right_content-noData']}>
                    <div
                      className={styles['taskCarTeam-content_right_content-noData_imgLeft']}
                    ></div>
                    <p className={styles['taskCarTeam-content_right_content-noData_text']}>
                      暂无数据
                    </p>
                  </div>
                )}
              </div>
            </div>
            {platformType === 1 ? (
              <>
                <Divider style={{ margin: 0 }} />
                <div className={styles['taskCarTeam-content_left_bottom']}>
                  <Checkbox
                    style={{ marginRight: 8, display: 'inline-flex' }}
                    onChange={(e: any) => handleAllCheckClick(e.target.checked)}
                    checked={allCheckStatus}
                    indeterminate={isIndeterminateStatus}
                    disabled={!taskCarTeamData.length}
                  />
                  添加以上全部
                </div>
              </>
            ) : null}
          </div>
          <Divider style={{ margin: 0, height: '100%' }} type="vertical" />
          <div className={styles['taskCarTeam-content_right']}>
            <div className={styles['taskCarTeam-content_right_top']}>
              <span>
                {platformType === 1 && eliminate ? '已排除' : '已添加'}
                {taskCarTeamSelectList?.length}个
              </span>
              <Popover
                trigger="click"
                arrow={false}
                open={quickAddOpen}
                content={quickAddContent}
                placement="bottomRight"
                style={{ padding: 0 }}
                getPopupContainer={(targetNode: any) => targetNode?.parentNode}
                onOpenChange={open => {
                  setQuickAddOpen(open);
                }}
              >
                {platformType === 1 ? (
                  <Button
                    type="link"
                    disabled={!tenantOptions.length}
                    onClick={() => setQuickAddOpen(!quickAddOpen)}
                    style={{ color: '#366CFE' }}
                  >
                    快捷添加
                  </Button>
                ) : null}
              </Popover>
            </div>

            <Divider style={{ margin: 0 }} />
            <div className={styles['taskCarTeam-content_right_content']}>
              {platformType === 1 ? (
                <Input
                  placeholder={`请输入${placeholder}`}
                  style={{ marginBottom: '8px', width: 304 }}
                  // prefix={<BLMIconFont type="BLM-ic-search-o" />}
                  onChange={(e: any) => handleRightSearch(e.target.value)}
                  value={rightSearchValue}
                />
              ) : null}

              <div
                style={{
                  maxHeight: 284,
                  overflow: 'auto',
                }}
              >
                {taskCarTeamSelectShowList.length ? (
                  <VirtualList
                    data={taskCarTeamSelectShowList}
                    height={266}
                    itemHeight={44}
                    itemKey={item => item?.tenantId}
                  >
                    {(item, index) => (
                      <div className={styles['taskCarTeam-content_right_content-render']}>
                        <div
                          className={styles['taskCarTeam-content_right_content-item']}
                          style={
                            platformType === 1 && eliminate
                              ? { textDecoration: 'line-through' }
                              : {}
                          }
                        >
                          <div className={styles['taskCarTeam-content_right_content-item-text']}>
                            {rightSearchValue.length
                              ? handleHighLight(rightSearchValue, item?.tenantIdAndName, item)
                              : item?.tenantIdAndName}
                          </div>
                          <CloseOutlined
                            rev="horizontal"
                            onClick={() => handelDeleteItem(taskCarTeamSelectShowList[index])}
                            style={{ paddingTop: '5px' }}
                          />
                        </div>
                      </div>
                    )}
                  </VirtualList>
                ) : (
                  <div className={styles['taskCarTeam-content_right_content-noData']}>
                    <div className={styles['taskCarTeam-content_right_content-noData_img']}></div>
                    <p className={styles['taskCarTeam-content_right_content-noData_text']}>
                      暂无数据
                    </p>
                  </div>
                )}
              </div>
            </div>
            <Divider style={{ margin: 0 }} />
            {platformType === 1 && excludedDomainShow ? (
              <Checkbox
                className={styles['taskCarTeam-content_right_eliminate']}
                checked={eliminate}
                onChange={() => eliminateChange(!eliminate)}
              >
                排除以上选项
              </Checkbox>
            ) : null}
            <Button
              type="link"
              className={styles['taskCarTeam-content_right_bottom']}
              disabled={!taskCarTeamSelectShowList.length}
              onClick={handleCancelSelectedItem}
            >
              {rightSearchValue ? '清空检索项' : '清空'}
            </Button>
          </div>
        </div>
        <Divider style={{ margin: 0 }} />
        <div className={styles['taskCarTeam-footer']}>
          <Button style={{ marginRight: 8 }} onClick={() => handleAddClick(false)}>
            取消
          </Button>
          <Button type="primary" onClick={() => handleAddClick(true)}>
            确定
          </Button>
        </div>
      </div>
    </Spin>
  );

  return (
    <>
      {/* {contextHolder} */}
      <Popover
        overlayClassName="popoverSelect"
        trigger="click"
        open={taskCarTeamPopoverOpen}
        content={popoverContent}
        placement="bottomLeft"
        arrow={false}
        style={{ width: '672px', padding: 0 }}
        onOpenChange={() => {
          setTaskCarTeamPopoverOpen(!taskCarTeamPopoverOpen);
        }}
      >
        <Select
          mode="multiple"
          allowClear
          onClear={e => {
            setTheFinalSelectId([]);
            setTaskCarTeamSelectIdList([]);
            setTaskCarTeamSelectList([]);
            setTaskCarTeamSelectShowList([]);
            // setTaskCarTeamIdList([]);
            selectCallBack({
              eliminate, // 排除状态
              platformType, // 平台类型
              selectIdList: [], // 选中集合的Id
              selectLabelList: [],
              // 选中的集合
            });
          }}
          open={false}
          placeholder={`请选择${placeholder}` ?? '请选择'}
          options={selectOptions}
          onFocus={() => {
            switchTag(valueType);
            setPlatformType(valueType);
          }}
          value={theFinalSelectId}
          // defaultValue={defaultValueIds}
          fieldNames={{
            label: 'tenantIdAndName',
            value: 'tenantId',
          }}
          style={{ width: '200px' }}
        />
      </Popover>
    </>
  );
};

export default TenantSelectNew;
