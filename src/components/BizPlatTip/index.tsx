import React from 'react';
import { connect } from 'dva';

const Index = ({ businessPowerList, platformList, businessType, applicationType }) => {
  // 数组转换成对象
  const arrToObjBusiness = arr => {
    const obj = {};
    arr?.forEach(item => {
      if (item.businessType) {
        obj[item.businessType] = item.businessDesc ?? '';
      }
    });
    return obj;
  };
  const arrToObjApplication = arr => {
    const obj = {};
    arr?.forEach(item => {
      if (item.applicationType) {
        obj[item.applicationType] = item.applicationDesc ?? '';
      }
    });
    return obj;
  };
  const businessTypeList = arrToObjBusiness(businessPowerList);
  const applicationTypeList = arrToObjApplication(platformList);

  return (
    <h3>
      {businessTypeList[businessType]}-{applicationTypeList[applicationType]}
    </h3>
  );
};

export default connect(({ user }) => ({
  businessPowerList: user.businessPowerList,
  platformList: user.platformList,
}))(Index);
