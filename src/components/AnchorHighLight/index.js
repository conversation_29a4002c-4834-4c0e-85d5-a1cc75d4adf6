import React from 'react'
import {Anchor, Row, Col, Icon, Popover, Button, Form, Input, message} from 'antd';
import moment from 'moment'

import style from './index.less'

const {Link} = Anchor;
class AnchorHighLight extends React.Component {

    constructor(props) {
        super(props);
    }

    state = {
        isshow: false
    }

    render() {
        return (
            <div className={style['anchor-box']}>
                <Button onClick={this.handleClickIcon} className={style['anchor-icon']}><Icon type="highlight" /></Button>
                {this.state.isshow ? 
                (<div className={style['anchor-content']}>
                    <div className={style['anchor-arrow']}></div>
                    <div className={style['anchor-inner']}>{this.getContent()}</div>
                </div>) : 
                null}
            </div>
        )
    }

    handleClickIcon = () => {
        this.setState({
            isshow: !this.state.isshow
        })
    }

    getContent = () => {
        const { getFieldDecorator } = this.props.form;

        return (
            <div>
                <Form 
                    labelCol={{sm: { span: 7 }}} 
                    wrapperCol={{sm: {span: 17}}}
                >
                    <Row>
                        <Col>
                            <Form.Item 
                            label="时间间隔:"
                            >
                            {getFieldDecorator('seconds', {initialValue: this.props.seconds})(<Input placeholder="请输入时间间隔（秒）" />)}
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col>
                            <Form.Item 
                            label="时间比重:"
                            >
                            {getFieldDecorator('percent', {initialValue: this.props.percent})(<Input placeholder="异常的时长/订单的总时长（%）" />)}
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Button type="primary" htmlType="submit" onClick={this.handleSureSetting}>确认设置</Button>
                    </Row>
                </Form>

                {this.highlighted(this.props.currentData, this.props.type)}
            </div>
        )
    }

    handleSureSetting = () => {
        this.props.form.validateFields((err, fieldsValue) => {
            if(err) {
                return;
            }
        
            const seconds = fieldsValue['seconds'];
            const percent = fieldsValue['percent'];
        
            if(seconds || percent) {
                this.props.onSure && this.props.onSure({
                    seconds: isNaN(+seconds) ? null : +seconds || 30,
                    percent: isNaN(+percent) ? null : +percent || 10
                });
                this.props.onSure && this.setState({
                  isshow: !this.state.isshow
              })
            }
        })
    }

    highlighted = (data,tag) => {
        let lastData = ''
        let anchorAry = []
        if(data){
          const ary = data;
          ary.map((item,index)=>{
            if(index===0){
              lastData = item;
              return '';
            }
            let sumDate = 0;
            let dataArr = (this.props.dataArr && this.props.dataArr.length) ? this.props.dataArr : [];
            
            if(dataArr.length === 0) return null;
            sumDate = dataArr[dataArr.length - 1].reportTime - dataArr[0].reportTime;
    
            if(item.latitude === lastData.latitude &&
              item.longitude === lastData.longitude &&
              item.timestamp === lastData.timestamp && 
              item.reportTime - lastData.reportTime > (+this.props.seconds) * 1000 &&
              (item.reportTime - lastData.reportTime) * 100 / sumDate > (+this.props.percent)
            ) {
              anchorAry.push({ title: `${moment(item.reportTime).format('HH:mm:ss')}-${moment(lastData.reportTime).format('HH:mm:ss')}`, href: `#highlighted_${tag}_${index}`});
            }
            lastData = item;
            return '';
          })
        }
        const newView = (
          <Row style={{marginTop: '20px'}}>
            {
              anchorAry && anchorAry.length?(
                <Col>
                  <Anchor  offsetTop={200}>
                    {
                      anchorAry.map((item, index)=>{
                        return <Link key={index} href={window.location.hash + item.href} title={item.title} />
                      })
                    }
                  </Anchor>
                </Col>
              ):null
            }
          </Row>
        )
        return newView
    }
}

export default Form.create()(AnchorHighLight);
