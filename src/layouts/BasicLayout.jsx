/**
 * Ant Design Pro v4 use `@ant-design/pro-layout` to handle Layout.
 * You can view component api by:
 * https://github.com/ant-design/ant-design-pro-layout
 */
import ProLayout from '@ant-design/pro-layout';
import React, { useState, useEffect } from 'react';
import Link from 'umi/link';
import { connect } from 'dva';
import { formatMessage } from 'umi-plugin-react/locale';
import SSOMenuDataRender from '@leopard/umi-plugin-sso-auth/lib/SSOMenuDataRender';
// import Authorized from '@/utils/Authorized';
import RightContent from '@/components/GlobalHeader/RightContent';
// import { isAntDesignPro } from '@/utils/utils';
import { getToken } from '@/utils/auth';
import watermark from 'watermark-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import logo from '../assets/logo.png';

const footerRender = (_, defaultDom) => null;

const BasicLayout = props => {
  const { dispatch, children, settings, currentUser, currResources } = props;

  /**
   * 水印
   */
  const setWaterMark = (tenantName, userMobile) => {
    const walterMarkOption = {
      watermark_x: 20, // 水印起始位置x轴坐标
      watermark_y: 20, // 水印起始位置Y轴坐标
      watermark_rows: 0, // 水印行数
      watermark_cols: 0, // 水印列数
      watermark_x_space: 25, // 水印x轴间隔
      watermark_y_space: 20, // 水印y轴间隔
      watermark_font: '微软雅黑', // 水印字体
      watermark_color: 'black', // 水印字体颜色
      watermark_fontsize: '18px', // 水印字体大小
      watermark_alpha: 0.08, // 水印透明度，要求设置在大于等于0.005
      watermark_width: 130, // 水印宽度
      watermark_height: 100, // 水印高度
      watermark_angle: 20, // 水印倾斜度数
    };
    watermark.load({
      ...walterMarkOption,
      watermark_txt: tenantName + userMobile, // 水印的内容
    });
  };

  const [userInfo, setUserInfo] = useState(null);
  /*
   * 初始化获取信息
   */
  useEffect(() => {
    if (dispatch) {
      // dispatch({
      //   type: 'settings/getSetting',
      // });
      // dispatch({
      //   type: 'user/fetchCurrent',
      // });
      dispatch({
        type: 'user/fetchUser',
      });
      // 获取业务线和应用关系
      dispatch({
        type: 'user/getBusinessAppList',
      });
    }
  }, []);

  /**
   * init variables
   */
  const handleMenuCollapse = payload =>
    dispatch &&
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload,
    });

  /**
   * use Authorized check all menu item
   */
  return (
    <ConfigProvider locale={zhCN}>
      <SSOMenuDataRender>
        <ProLayout
          logo={logo}
          onCollapse={handleMenuCollapse}
          menuItemRender={(menuItemProps, defaultDom) => {
            if (menuItemProps.isUrl) {
              return defaultDom;
            }
            if (menuItemProps.targetBlank) {
              return (
                <Link to={menuItemProps.path} target="_blank">
                  {defaultDom}
                </Link>
              );
            }
            return <Link to={menuItemProps.path}>{defaultDom}</Link>;
          }}
          breadcrumbRender={(routers = []) => [
            {
              path: '/',
              breadcrumbName: formatMessage({
                id: 'menu.home',
                defaultMessage: 'Home',
              }),
            },
            ...routers,
          ]}
          itemRender={(route, params, routes, paths) => {
            const first = routes.indexOf(route) === 0;
            return first ? (
              <Link to={paths.join('/')}>{route.breadcrumbName}</Link>
            ) : (
              <span>{route.breadcrumbName}</span>
            );
          }}
          footerRender={footerRender}
          rightContentRender={rightProps => <RightContent {...rightProps} />}
          {...props}
          {...settings}
          children={null}
        >
          {/* {userInfo && children} */}
          {children}
        </ProLayout>
      </SSOMenuDataRender>
    </ConfigProvider>
  );
};

export default connect(({ global, settings, user, tenant }) => ({
  collapsed: global.collapsed,
  settings,
  currentUser: user.currentUser,
  currResources: user.currResources,
  tenantList: tenant.tenantList,
}))(BasicLayout);
