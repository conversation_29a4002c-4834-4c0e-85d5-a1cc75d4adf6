/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-29 16:25:23
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-24 14:33:02
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/config/index.js
 */
export const SystemName = '密码管理系统'
export const TenantIdKey = '_tenant_id_password_system'
export const TokenKey = '_yycx_admin_password_system'
export const Salt = 'randomsalt_password_system'
export const stk = 'stk'
// 高德地图key
export const aMapKey = '55897440ff54932c599cc73b9b4baf1c'

/**
* 应用
*/
export const EnumApplicationOptions = [
    { label: '司机端', value: '1' }
];

/**
 * 客户端类型
 */
export const EnmuClientTypeOptions = [
    { label: 'app', value: '1' },
    { label: 'web', value: '2' },
    { label: 'h5', value: '3' },
    { label: 'wxmp', value: '4' }
]

/**
 * 事件类型
 */
export const EnumEventTypeOptions = [
    { label: 'click', value: 'click' },
    { label: 'pv', value: 'pv' },
    { label: 'pl', value: 'pl' },
    { label: 'pb', value: 'pb' },
    { label: 'lv', value: 'lv' },
    { label: 'change', value: 'change' },
    { label: 'appstart', value: 'appstart' },
    { label: 'append', value: 'append' },
    { label: 'exposure', value: 'exposure' },
    { label: 'popupShow', value: 'popupShow' },
    { label: 'popupHide', value: 'popupHide' },
]

/**
 * 埋点来源
 */
export const EnumSourceOptions = [
    { label: '全埋点事件', value: '1' },
    { label: '手工埋点事件', value: '2' }
]

/**
 * 埋点状态
 */
export const EnumStatusOptions = [
    { label: '全部', value: '' },
    { label: '开发中', value: 0 },
    { label: '已上线', value: 1 },
]


/**
 * 归属业务
 */
export const EnumBusinessOptions = [
    { label: '网约车', value: 1 },
    { label: '代驾', value: 2 },
    { label: 'CP后台', value: 3 },
    { label: '拉新小程序', value: 4 },
    { label: 'H5', value: 5 },
]