import { Button, message, notification } from 'antd';
import React from 'react';
import { formatMessage } from 'umi-plugin-react/locale';
import defaultSettings from '../config/defaultSettings';
import 'antd/dist/antd.css';
// DatePicker、MonthPicker、RangePicker、WeekPicker 部分 locale 是从 value 中读取，需要先正确设置 moment 的 locale。
import moment from 'moment';
import 'moment/locale/zh-cn';
import Cookies from 'js-cookie';

const { deployEnv = 'dev' } = process.env;

moment.locale('zh-cn');

// 埋点SDK初始化（对接埋点平台）
// eslint-disable-next-line no-unused-expressions
window.BlmAnalysis &&
  window.BlmAnalysis.start({
    devMode: ['dev', 'daily'].includes(deployEnv), // 是否上报到线下环境，false: 线上环境，true：线下环境
    category: 'leopard', // 业务线，需要去埋点管理平台申请业务线后写入
    appnm: 'we_com', // 应用名，需要去埋点管理平台申请应用后写入
    channel: 'web', // 投放渠道
  });

const envOptions = [
  { label: 'localhost', value: 'dev' },
  { label: 'eventlog-test', value: 'daily' },
  { label: 'eventlog-pre2', value: 'pre2' },
  { label: 'eventlog-pre', value: 'pre' },
  { label: 'eventlog', value: 'publish' },
];
// 获取当前URL
const currentURL = window.location.href;
// 使用URL对象解析URL
const urlObject = new URL(currentURL);
const domain = urlObject?.hostname ?? '';
const env = domain?.split('.')[0] ?? '';
let envVar = 'dev';
envOptions.forEach(item => {
  if (item.label === env) {
    envVar = item.value;
  }
});

window?.BlmMonitor?.start({
  devMode: !['pre2', 'pre', 'publish'].includes(envVar), // 上报环境, 判断是否上报到线下环境 ;false: 线上环境，true：线上环境
  project: 'eventlog', // 项目名
  category: 'wyc', // 业务线，目前枚举有①网约车：wyc，②代驾：dj
  appnm: 'inside-platform', // 应用名,
  channel: 'web', // 渠道（web、h5）
  tenantId: Cookies?.get('_tenant_id'), // 租户ID
  envVar: envVar, // 项目环境
  ajax: {
    flag: true, // ajax超时监控，默认关闭false
    duration: 25000, // 启用AJAX超时监控，超时时间设置为25秒
  },
  page: {
    sensory: true, // 开启MutationObserver监听DOM变化后 上报性能指标
  },
  resource: {
    maxNumber: 200,
    delay: 5000, // 资源性能延时上报时间
  },
  log: {
    delay: 5000, // 业务日志延时上报时间
    maxNumber: 20, // 业务日志合并上报 最大条数
  },
});

const { pwa } = defaultSettings; // if pwa is true

if (pwa) {
  // Notify user if offline now
  window.addEventListener('sw.offline', () => {
    message.warning(
      formatMessage({
        id: 'app.pwa.offline',
      }),
    );
  }); // Pop up a prompt on the page asking the user if they want to use the latest version

  window.addEventListener('sw.updated', event => {
    const e = event;

    const reloadSW = async () => {
      // Check if there is sw whose state is waiting in ServiceWorkerRegistration
      // https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerRegistration
      const worker = e.detail && e.detail.waiting;

      if (!worker) {
        return true;
      } // Send skip-waiting event to waiting SW with MessageChannel

      await new Promise((resolve, reject) => {
        const channel = new MessageChannel();

        channel.port1.onmessage = msgEvent => {
          if (msgEvent.data.error) {
            reject(msgEvent.data.error);
          } else {
            resolve(msgEvent.data);
          }
        };

        worker.postMessage(
          {
            type: 'skip-waiting',
          },
          [channel.port2],
        );
      }); // Refresh current page to use the updated HTML and other assets after SW has skiped waiting

      window.location.reload(true);
      return true;
    };

    const key = `open${Date.now()}`;
    const btn = (
      <Button
        type="primary"
        onClick={() => {
          notification.close(key);
          reloadSW();
        }}
      >
        {formatMessage({
          id: 'app.pwa.serviceworker.updated.ok',
        })}
      </Button>
    );
    notification.open({
      message: formatMessage({
        id: 'app.pwa.serviceworker.updated',
      }),
      description: formatMessage({
        id: 'app.pwa.serviceworker.updated.hint',
      }),
      btn,
      key,
      onClose: async () => {},
    });
  });
} else if ('serviceWorker' in navigator) {
  // eslint-disable-next-line compat/compat
  navigator.serviceWorker.ready
    .then(registration => {
      registration.unregister();
      return true;
    })
    .catch(() => {
      console.log('serviceWorker unregister error');
    });
}
