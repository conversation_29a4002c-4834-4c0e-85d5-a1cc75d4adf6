// 分页吸底的页面结构样式


/* 页面整体背景色, 用法:
    <PageHeaderWrapper
      title={false}
      className={XXX.pageHeaderWrapperStyle}
      content={<>...</>}
    />
*/
.pageHeaderWrapperStyle {
  background-color: #F7F8FA !important;
}

/* Tabs的整体样式, 用法:
    <Tabs className={XXX.tabBox}>...</Tabs>
*/
.tabBox {
  height: calc(100vh - 125px);
  padding: 8px 16px;
  border-radius: 8px;
  background-color: #fff;
}

// 所有内容部分
.content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 195px);
}
.content2 {
  display: flex;
  flex-direction: column;
}
// 查询条件部分
.content_form {
  padding: 16px;
  border-radius: 6px;
  background-color: #f7F8Fa;
}
// table表格部分
.content_table {

  margin-top: 12px;
  flex-grow: 1;
  overflow: hidden
}
// 分页
.content_pagination {
  flex-shrink: 0
}


.formItemCss {
  width: 220px !important;
}

.formItemExpand {
  width: 220px !important;
}

.formItemUnExpand {
  width: 0px !important;
  height: 0px;
  margin: 0px !important;
  overflow: hidden;
  visibility: hidden;
}
:global {
  .emptySelectContentText{
    .ant-select-item-empty{
      padding: 12px;
    }
  }
}



