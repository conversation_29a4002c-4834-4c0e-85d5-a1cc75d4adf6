import request from '@/utils/request';

// 通过token获取用户权限组信息
export async function getUserInfoByToken() {
  return request('/sso/auths/currentUserRoles', {
    params: {},
  });
}

// 通过token获取用户信息
export async function getUser() {
  return request('/sso/auths/current', {
    params: {},
  });
}

// 获取所有用户列表
export async function userAll() {
  return request('/admin/v1/invest/user/all', {
    method: 'POST',
    data: {},
  });
}

// 工单用户搜索
export async function fetchUser(params) {
  return request('/admin/invest/ticket/user/search', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

export async function getAllUser(data) {
  return request('/admin/invest/ticket/user/getAll', {
    method: 'POST',
    data,
  });
}

// 获取用户的业务线和平台数据
export async function getBusinessAppList(data) {
  return request('/admin/v1/eventlog/tracking/businessAppList', {
    method: 'POST',
    data,
  });
}
