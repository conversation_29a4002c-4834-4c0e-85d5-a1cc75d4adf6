import request from '@/utils/request';

// 埋点管理-事件列表查询
export async function queryEventList(params) {
  return request('/admin/v1/eventlog/tracking/event/queryEventList', {
    method: 'POST',
    data: params,
  });
}

// 埋点管理-新增事件
export async function queryAddEvent(pamas) {
  return request('/admin/v1/eventlog/tracking/event/addEvent', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-事件列表详情
export async function queryEventDetailById(pamas) {
  return request('/admin/v1/eventlog/tracking/event/queryEventDetailById', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-事件详情编辑
export async function queryEditEvent(pamas) {
  return request('/admin/v1/eventlog/tracking/event/editEvent', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-状态变更
export async function queryUpdateStatus(pamas) {
  return request('/admin/v1/tracking/event/updateStatus', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-关联页面数据
export async function queryDetailsPage(pamas) {
  return request('/admin/v1/eventlog/tracking/event/page/query/details/page', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-创建人审核人信息
export async function queryUserInfo(pamas) {
  return request('/admin/v1/eventlog/tracking/event/queryUserInfo', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-状态流转
export async function updateStatus(pamas) {
  return request('/admin/v1/eventlog/tracking/event/updateStatus', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点管理-审核列表查询
export async function queryNoApproveEventList(pamas) {
  return request('/admin/v1/eventlog/tracking/event/queryNoApproveEventList', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点验证-埋点校验接口
export async function queryLogTest(pamas) {
  return request('/admin/v1/eventlog/tracking/test/logTest', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点验证-页面查询
export async function queryPageList(pamas) {
  return request('/admin/v1/eventlog/tracking/event/page/pageList', {
    method: 'POST',
    data: pamas,
  });
}

// 埋点验证-事件查询
export async function queryPageEventList(pamas) {
  return request('/admin/v1/eventlog/tracking/event/page/pageEventList', {
    method: 'POST',
    data: pamas,
  });
}

export async function queryTestBind(pamas) {
  return request('/admin/v1/eventlog/tracking/test/testBindUpdate', {
    method: 'POST',
    data: pamas,
  });
}

// 查询租户接口
export async function queryTenantListData(pamas) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryTenantListData', {
    method: 'POST',
    data: pamas,
  });
}

// 查询租户接口（新）
export async function queryTenantListDataTmpNew(pamas) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryTenantListDataTmpNew', {
    method: 'POST',
    data: pamas,
  });
}

// 概览-查询租户接口（增加平台选择标签）
export async function queryTenantListDataTmpNewV2(pamas) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryTenantListDataTmpNewV2', {
    method: 'POST',
    data: pamas,
  });
}

// 获取数据最新时间
export async function queryBoardDataGenerateTime(pamas) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryBoardDataGenerateTime', {
    method: 'POST',
    data: pamas,
  });
}

// 事件概览固值
export async function queryBoardData(pamas) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryBoardData', {
    method: 'POST',
    data: pamas,
  });
}

// 事件概览时序看板
export async function queryTimingSequenceBoardData(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryTimingSequenceBoardData', {
    method: 'post',
    data: params,
  });
} // 饼图top5
export async function queryGeneralPieBoard(params = {}) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryGeneralPieBoardTop5Data', {
    method: 'post',
    data: params,
  });
}

// 查询绑定有效时间
export async function queryBindInfo(pamas) {
  return request('/admin/v1/eventlog/tracking/test/queryBindInfo', {
    method: 'POST',
    data: pamas,
  });
}

// 浏览事件租户top10
export async function generalTenantTop10Data(params) {
  return request(
    '/admin/v1/eventlog/tracking/event/board/general/queryGeneralTenantPvAndUvTop10Data',
    {
      method: 'POST',
      data: params,
    },
  );
}

// 页面排行top10
export async function generalPageTop10Data(params) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryGeneralPageTop10Data', {
    method: 'POST',
    data: params,
  });
}

// 事件排行top10
export async function generalCkEventTop10Data(params) {
  return request('/admin/v1/eventlog/tracking/event/board/general/queryGeneralCkEventTop10Data', {
    method: 'POST',
    data: params,
  });
}

// 埋点验证-概览-页面查询
export async function queryOnlinePageList(pamas) {
  return request('/admin/v1/eventlog/tracking/event/page/onlinePageList', {
    method: 'POST',
    data: pamas,
  });
}
