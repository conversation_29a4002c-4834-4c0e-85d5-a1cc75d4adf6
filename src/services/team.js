import request from '@/utils/request';

// 获取业务组列表
export async function getTeamList(data) {
    return request('/admin/invest/ticket/team/list', {
      method: 'POST',
      data,
    })
}

// 新增业务组
export async function addTeam(params) {
    return request('/admin/invest/ticket/team/add', {
      method: 'POST',
      data: {
          ...params,
      },
    })
}

// 更新业务组信息
export async function updateTeam(params) {
    return request('/admin/invest/ticket/team/update', {
      method: 'POST',
      data: {
          ...params,
      },
    })
}

// 删除业务组
export async function delTeam(params) {
    return request('/admin/invest/ticket/team/delete', {
      method: 'POST',
      data: {
          ...params,
      },
    })
}

// 获取业务组内的用户
export async function fetchTeamUser(params) {
    return request('/admin/invest/ticket/team/user/rel/findByTeam', {
      method: 'POST',
      data: {
          ...params,
      },
    });
}
