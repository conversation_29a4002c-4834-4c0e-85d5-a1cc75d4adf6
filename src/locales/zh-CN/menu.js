/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-29 16:25:23
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-22 14:24:28
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/src/locales/zh-CN/menu.js
 */
export default {
  'menu.welcome': '欢迎',
  'menu.home': '首页',
  'menu.login': '登录',
  'menu.account.logout': '退出登录',
  'menu.register': '注册',
  'menu.register.result': '注册结果',
  'menu.tempAccount': '临时账号',
  'menu.information': '埋点数据',
  'menu.documents': '埋点文档',
  'menu.documents.stopwatch': '手工埋点码表',
  'menu.documents.allStopwatch': '全埋点码表',
  'menu.documents.relevance': '埋点映射',
  'menu.documents.pageManagement': '页面管理',
  'menu.documents.blockManagement': '全埋点-页面/区块管理',
  'menu.documents.eventManagement': '全埋点-按钮位置管理',
  'menu.documents.fieldManagement': '全埋点-系统字段值管理',
  'menu.demand': '埋点需求',
  'menu.demand.submit': '埋点需求提交',
  'menu.demand.audit': '埋点需求审核',
  'menu.buryingPoint': '埋点管理',
  'menu.buryingPoint.sourceManagement': '埋点元素管理',
  'menu.exception.403': '403',
  'menu.exception.404': '404',
  'menu.exception.500': '500',
  'menu.result': '结果页',
  'menu.result.success': '成功页',
  'menu.result.fail': '失败页',
  'menu.exception': '异常页',
  'menu.exception.not-permission': '403',
  'menu.exception.not-find': '404',
  'menu.exception.server-error': '500',
  'menu.exception.trigger': '触发错误',
  'menu.buryingPointNew': '埋点管理（新）',
  'menu.buryingPointNew.eventTracking': '事件配置',
  'menu.buryingPointNew.reviewEventTracking': '事件审核',
  'menu.buryingPointNew.pageTracking': '页面管理',
  'menu.dataVerification': '数据验证',
  'menu.dataVerification.buriedDataVerification': '埋点数据验证',
  'menu.dataVerification.buriedEventDataVerification': 'Web验证',
  'menu.dataAnalysis': '数据分析',
  'menu.dataAnalysis.eventAnalysis': '事件分析',
  'menu.dataAnalysis.overviewTracking': '概览',
  'menu.buryingPointNew.eventDetail': '事件详情',
  'menu.buryingPointNew.pageDetail': '页面详情',
};
