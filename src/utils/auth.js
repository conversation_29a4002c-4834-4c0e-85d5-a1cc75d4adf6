import Cookies from 'js-cookie'
import md5 from 'blueimp-md5'
import { TokenKey, TenantIdKey, Salt, stk } from '@/config/'

// expires see: https://github.com/js-cookie/js-cookie/wiki/Frequently-Asked-Questions#expire-cookies-in-less-than-a-day
// 接口返回的 token 有效期目前为 1 天
const oneDay = 1
// const testMinutes = new Date(new Date().getTime() + 2 * 60 * 1000) //For test 2 minutes
const getExpires = expiration => {
  let expires = oneDay
  try {
    expires = (expiration / (24 * 60 * 60 * 1000)) - (new Date()).getTime()
  } catch (e) {
    //console.log(e)
  }
  return Number(expires) > 0 ? expires : oneDay
}

export function getSign(signParam) {
  return md5(`${signParam}@${Salt}`)
}

export function setToken(tokenData) {
  const { token, expiration } = tokenData;
  Cookies.set(stk, token, { expires: getExpires(expiration) });
  return Cookies.set(TokenKey, tokenData, { expires: getExpires(expiration) })
}

export function getTokenObj() {
  return Cookies.getJSON(TokenKey) || {}
}

export function getToken() {
  let { token = '' } = getTokenObj();
  token === '' && (token = Cookies.get('token'))
  return token
}

export function getTokenExpires() {
  const { expiration = 0 } = getTokenObj()
  return expiration
}

export function removeToken() {
  Cookies.remove('token');
  Cookies.remove('user');
  return Cookies.remove(TokenKey)
}

export function setTenantId(tenantId, expires) {
  return Cookies.set(TenantIdKey, tenantId, { expires: getExpires(expires) })
}

export function getTenantId() {
  // return Cookies.get(TenantIdKey) || -1
  return -1;
}

export function removeTenantId() {
  return Cookies.remove(TenantIdKey)
}
