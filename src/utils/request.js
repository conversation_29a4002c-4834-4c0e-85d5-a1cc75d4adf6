/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend } from 'umi-request';
import moment from 'moment';
import { notification, message } from 'antd';
import { getTenantId, getSign, getToken, getTokenExpires, removeToken } from '@/utils/auth';
import { async } from 'q';
import deploy from '../../config/deploy';

const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  413: '上传资源过大，上传失败。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};
/**
 * 异常处理程序
 */

const errorHandler = async error => {
  const { response } = error;
  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;
    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  }
  // if (error instanceof Response) {
  //   const res = await error.clone().json();
  //   return res;
  // }
  return error
};
/**
 * 配置request请求时的默认参数
 */
const request = extend({
  headers: {
    // 携带公参
    _admin_ts: moment().unix(), // 时间戳(秒)  long
    _admin_eid: getTenantId(), // 商户ID  long
    _admin_tk: getToken() || '', // 让每个请求携带自定义token 请根据实际情况自行修改
    // _admin_tk: '9c740895502f4147a4ca47a1b68c29c4', // 让每个请求携带自定义token 请根据实际情况自行修改
  },
  errorHandler,
  // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
});
// request拦截器, 改变url 或 options.
request.interceptors.request.use((url, options) => {
  const tenantId = localStorage.getItem('tenantId') || undefined;
  const data =
    Object.prototype.toString.call(options.data) === '[object FormData]'
      ? options.data
      : { tenantId, ...options.data };
  Object.keys(data).map(a => {
    a && data[a] === '' && (data[a] = undefined)
  })
  // eslint-disable-next-line
  return { url: deploy[deployEnv].base + url, options: { ...options, data } };
});
// response拦截器, 处理response
request.interceptors.response.use(async response => {
  // return new Promise((resolve, reject) => {
    // if(/^(\/dingsetting)/.exec(url.substr(url.indexOf('/dingsetting')))){
    //   resolve(response)
    //   return
    // }
    const res = await response.clone().json();
    if (res.hasOwnProperty('code')) {
      if (res.code !== 1) {
        // message.warning((res && res.msg) || '请求错误');
      }
    }
    return response
});

export default request;
