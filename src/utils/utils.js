import moment from 'moment';
import { parseUrl } from 'query-string';
import { notification, message } from 'antd';
/* eslint no-useless-escape:0 import/prefer-default-export:0 */
const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

const isUrl = path => reg.test(path);

const isAntDesignPro = () => {
  if (ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site') {
    return true;
  }

  return window.location.hostname === 'preview.pro.ant.design';
}; // 给官方演示站点用，用于关闭真实开发环境不需要使用的特性

const isAntDesignProOrDev = () => {
  const { NODE_ENV } = process.env;

  if (NODE_ENV === 'development') {
    return true;
  }

  return isAntDesignPro();
};
// 转换
const renderNum = val => val || 0;
const renderStr = val => val || '无';
const formatTime = val => (val ? moment(val).format('YYYY-MM-DD') : val || '无');
const parseSex = val => (val === 1 ? '男' : '女');
const parseState = val => {
  if (val === 1) {
    return '正常';
  }
  if (val === 2) {
    return '注销';
  }
  return '停用';
};
// 校验
const checkForm = new Map([
  ['phone', { reg: val => /^1[0-9]{10}$/.test(val), msg: '手机号格式不正确！' }],
  ['order', { reg: val => /^[0-9]{18}$/.test(val), msg: '订单号不是18位数字！' }],
  ['passenger', { reg: val => /^[0-9]{10}$/.test(val), msg: '乘客id不是10位数字！' }],
]);

const isEmptyObj = obj => {
  for (const o in obj) {
    return false;
  }
  return true;
};

const deepPath = (obj, path) => {
  let target = obj;
  const len = path.length;
  if (len === 0) return;
  for (let i = 0; i < len; i++) {
    if (target && typeof target === 'object') {
      target = target[path[i]];
    } else {
      target = undefined;
      break;
    }
  }
  return target;
};

// 按钮权限
const authBtn = (authList, key) => {
  let newAuthPage = [];
  authList.map(a => {
    if (a.resourceKey === key) {
      newAuthPage = a.subList.map(s => s.resourceKey);
    }
  });
  return newAuthPage;
};

// 获取所有的resourceKey
const getAllAuthBtn = (authList, newAuthPage = []) => {
  if (Array.isArray(authList)) {
    authList.forEach(item => {
      newAuthPage.push(item.resourceKey)
      if (item.subList && item.subList.length) {
        getAllAuthBtn(item.subList, newAuthPage)
      }
    })
  }
  return newAuthPage;
}


// 导出
const downloadData = param => {
  const url = '/admin/v1/eventlog/export/form/list/pvuv ';
  const xhr = new XMLHttpRequest();
  xhr.open('POST', url, true); // 也可以使用POST方式，根据接口
  xhr.setRequestHeader('Content-Type', 'application/json;charset=utf-8');
  xhr.responseType = 'blob'; // 返回类型blob
  // 定义请求完成的处理函数，请求前也可以增加加载框/禁用下载按钮逻辑
  xhr.onload = function() {
    // 请求完成
    if (this.status === 200) {
      // 返回200
      const blob = this.response;
      const reader = new FileReader();
      const filename = xhr.getResponseHeader('Content-Disposition');
      reader.readAsDataURL(blob); // 转换为base64，可以直接放入a的href
      reader.onload = function(e) {
        // 转换完成，创建一个a标签用于下载
        const a = document.createElement('a');
        const { query } = parseUrl(filename.replace(';', '?'));
        a.download = query.filename;
        a.href = e.target.result;
        document.body.appendChild(a);
        a.click();
        a.remove();
        message.success('导出成功');
      };
    } else {
      notification.warn({
        message: '导出失败',
      });
    }
  };
  // 发送ajax请求
  xhr.send(JSON.stringify(param));
};

const isMobile = () => {
  const system = {
    win: false,
    mac: false,
    xll: false,
  };
  const p = navigator.platform;
  system.win = p.indexOf('Win') == 0;
  system.mac = p.indexOf('Mac') == 0;
  system.x11 = p == 'X11' || p.indexOf('Linux') == 0;

  if (system.win || system.mac || system.xll) {
    return false;
  }
  return true;
};

const getTimeStamp = time => moment(time).valueOf();

const formatTimeTemplate = timestep => {
  const seconds = moment.duration(timestep).seconds();
  const minutes = moment.duration(timestep).minutes();
  const hours = Math.trunc(moment.duration(timestep).asHours());

  const hoursText = hours ? `${hours}时` : '';
  const minutesText = minutes ? `${minutes}分` : '';

  return `${hoursText}${minutesText}${seconds}秒`;
};

const safeParse = str => {
  let obj = {};
  try {
    obj = eval(`(${str})`);
  } catch (error) {}

  if (isEmptyObj(obj)) {
    try {
      obj = JSON.parse(str);
    } catch (error) {}
  }

  return obj;
};

const getUrlParse = str => {
  const reg = /[?&][^?&]+=[^?&]+/g;
  const arr = str.match(reg);
  const obj = {};
  if (arr) {
    arr.map(item => {
      const tempArr = item.substr(1).split('=');
      const key = decodeURIComponent(tempArr[0]);
      const value = decodeURIComponent(tempArr[1]);
      obj[key] = value.trim();
    });
  }
  return obj;
};
const isDingTalk = navigator.userAgent.indexOf('DingTalk') > 0;

const getUrlKey = url => {
  const params = {};
  const urls = url?.split('?');
  const arr = urls[1]?.split('&');
  for (let i = 0, l = arr?.length; i < l; i++) {
    const a = arr[i].split('=');
    params[a[0]] = a[1];
  }
  return params;
};

export {
  isAntDesignProOrDev,
  isAntDesignPro,
  isUrl,
  formatTime,
  parseSex,
  parseState,
  renderNum,
  renderStr,
  checkForm,
  isEmptyObj,
  deepPath,
  isMobile,
  getTimeStamp,
  formatTimeTemplate,
  safeParse,
  getUrlParse,
  isDingTalk,
  downloadData,
  authBtn,
  getAllAuthBtn,
  getUrlKey,
};
