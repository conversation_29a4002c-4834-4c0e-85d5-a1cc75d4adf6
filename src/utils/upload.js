import SparkMD5 from 'spark-md5';

function fileMD5(file, callback) {
  const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
  const chunkSize = 2097152; // Read in chunks of 2MB
  const chunks = Math.ceil(file.size / chunkSize);
  let currentChunk = 0;
  const spark = new SparkMD5.ArrayBuffer();
  const fileReader = new FileReader();

  fileReader.onload = function (e) {
    spark.append(e.target.result); // Append array buffer
    currentChunk += 1;

    if (currentChunk < chunks) {
      loadNext();
    } else {
      const md5 = spark.end();
      callback(md5);
    }
  };

  fileReader.onerror = function () {
    console.warn('oops, something went wrong.');
    callback(null);
  };

  function loadNext() {
    const start = currentChunk * chunkSize;
    const end = start + chunkSize >= file.size ? file.size : start + chunkSize;

    fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
  }
  loadNext();
}

function getFilePayload(
  file,
  group = 'public',
  modulel = 'image',
  uploadMode = 'PUBLIC_READ_WITH_CDN',
) {
  // function getFilePayload(file, group = 'private', modulel = 'file', uploadMode = 'VPC_READ') {
  // 'public', 'image', 'PUBLIC_READ_WITH_CDN'
  return new Promise((resolve, reject) => {
    fileMD5(file, md5 => {
      if (!md5) {
        console.error('cal file md5 failed!');
        reject(new Error('cal file md5 failed!'));
      }
      const data = new FormData();
      data.append('file', file);
      data.append('name', file.name);
      data.append('md5', md5);
      data.append('contentType', file.type);
      data.append('group', group);
      data.append('module', modulel);
      data.append('fileLength', file.size);
      data.append('mode', uploadMode);
      resolve(data);
    });
  });
}
export default getFilePayload;
