import { Form, Input } from 'antd';
export const renderFormItem = (field, idx, form) => (
  <Form.Item key={idx} labelCol={{ span: 5 }} wrapperCol={{ span: 15 }} label={field[0]}>
    {form.getFieldDecorator(field[1], {
      rules: Array.isArray(field[2]) ? field[2] : [],
      initialValue: field[3],
      valuePropName: field[5] || 'value'
    })(field[4] || <Input placeholder="请输入" allowClear autoComplete="off" />)}
  </Form.Item>
);