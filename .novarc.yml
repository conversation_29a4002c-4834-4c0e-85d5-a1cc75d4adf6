project: WEB
env:
  packageManager: npm
  node: 14
app:
  eventlog:
    daily: # 测试环境
      ip: *************
      ipPath: /home/<USER>/www/leopard-web-eventlog/dist
      indexHtml: true
      output: dist
      ossEnv: test
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run build:daily
    pre2: # 预发环境
      ip: *************
      ipPath: /home/<USER>/www/leopard-web-eventlog/dist
      indexHtml: true
      output: dist
      ossEnv: prod
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run build:prepare
    pre: # 灰度环境
      ip: ************
      ipPath: /home/<USER>/www/leopard-web-eventlog/dist
      indexHtml: true
      output: dist
      ossEnv: prod
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run build:prepare
    publish: # 生产环境
      ip: ************
      ipPath: /home/<USER>/www/leopard-web-eventlog/dist
      indexHtml: true
      output: dist
      ossEnv: prod
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run build:publish
