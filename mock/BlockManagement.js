/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-06-21 15:25:19
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-06-21 21:54:40
 * @Description: file content
 * @FilePath: /leopard-web-eventlog/mock/blockManagement.js
 */
export default {
    'POST /admin/v1/eventlog/blockManagement': {
        code: 1,
        ts: 1617194476,
        msg: '服务接口调用成功',
        data: {
          pagenum: 1,
          pageSize: 10,
          totalNum: 2,
          totalPage: 1,
          isMore: 0,
          items: [
            {
              id: 1,
              pageName: '首页',
              business: 1,
              version: 'v0526 | 530',
              creator: '系统创建',
              updator: '',
              updateTime: '1655796702525',
              status: 1,
              eventDetails: [
                {
                  id: 1,
                  blockName: '首页营销位',
                  blockId: '区块ID',
                  pageSource: '首页',
                  business: 1,
                  version: 'v0526 | 530',
                  creator: '系统创建',
                  updator: '',
                  updateTime: '1655796702525',
                  status: 2
                },
                {
                  id: 2,
                  blockName: '首页补录区',
                  blockId: '区块ID',
                  pageSource: '首页',
                  business: 1,
                  version: 'v0526 | 530',
                  creator: '系统创建',
                  updator: '',
                  updateTime: '1655796702525',
                  status: 2
                }
              ]
            },
            {
              id: 2,
              pageName: '客服页',
              business: 2,
              version: 'v0526 | 530',
              creator: '系统创建',
              updator: '',
              updateTime: '1655796702525',
              status: 2
            }
          ]
        }
    }
}