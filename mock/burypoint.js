export default {
  'POST /admin/v1/eventlog/buryConfig/queryVersionList': {
    code: 1,
    ts: 1617194476,
    msg: '服务接口调用成功',
    data: ['470', '480', '490'],
  },
  'POST /admin/v1/eventlog/buryConfig/queryListByVersion': {
    code: 1,
    ts: 1617194476,
    msg: '服务接口调用成功',
    data: [
      {
        pageId: 'https://bailongma.yuque.com/fbyium/ia5tpa/kq2cyyhttps://bailongma.yuque.com/fbyium/ia5tpa/kq2cyyhttps://bailongma.yuque.com/fbyium/ia5tpa/kq2cyy',
        pageShowName: '',
      },
      {
        pageId: '2',
        pageShowName: '页面名称',
      },
    ],
  },
  'POST /admin/v1/eventlog/buryConfig/queryInfoListByPageIdAndVersion': {
    code: 1,
    ts: 1617194476,
    msg: '服务接口调用成功',
    data: {
      pageId: 'https://bailongma.yuque.com/fbyium/ia5tpa/kq2cyyhttps://bailongma.yuque.com/fbyium/ia5tpa/kq2cyyhttps://bailongma.yuque.com/fbyium/ia5tpa/kq2cyy',
      pageShowName: 'XXXX页面',
      tracks: [
        {
          trackingId: 'xxx',
          trackingShowName: 'xxx',
          ele: '1',
        },
        {
          trackingId: 'xxx',
          trackingShowName: 'xxx',
          ele: '2',
        },
        {
          trackingId: 'xxx',
          trackingShowName: 'xxx',
          ele: '3',
        },
        {
          trackingId: 'xxx',
          trackingShowName: 'xxx',
          ele: '4',
        },
        {
          trackingId: 'xxx',
          trackingShowName: 'xxx',
          ele: '1',
        },
        {
          trackingId: 'xxx',
          trackingShowName: 'xxx',
          ele: '2',
        },
      ],
    },
  },
  'POST /admin/v1/eventlog/buryConfig/updateTracking': {
    code: 1,
    ts: 1616398081,
    msg: '服务接口调用成功',
    data: '修改成功',
  },
};
